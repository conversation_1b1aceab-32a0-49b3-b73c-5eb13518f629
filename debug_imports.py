import sys
import os
import traceback

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

try:
    print("Importing database_manager...")
    from database.database_manager import DatabaseManager
    print("DatabaseManager imported successfully!")
    
    print("Importing ReceptionTab...")
    from ui.tabs.reception_tab import ReceptionTab
    print("ReceptionTab imported successfully!")
    
    print("Importing CrushingTab...")
    from ui.tabs.crushing_tab import CrushingTab
    print("CrushingTab imported successfully!")
    
    print("Importing LaverieTab...")
    from ui.tabs.laverie_tab import LaverieTab
    print("LaverieTab imported successfully!")
    
    print("Importing StockTab...")
    from ui.tabs.stock_tab import StockTab
    print("StockTab imported successfully!")
    
    print("Importing HourCounterTab...")
    from ui.tabs.hour_counter_tab import HourCounterTab
    print("HourCounterTab imported successfully!")
    
    print("Importing ReportsTab...")
    from ui.tabs.reports_tab import ReportsTab
    print("ReportsTab imported successfully!")
    
    print("Importing MainWindow...")
    from ui.main_window import MainWindow
    print("MainWindow imported successfully!")
    
    print("All imports successful!")
except Exception as e:
    print(f"Error: {str(e)}")
    traceback.print_exc()