"""
Configuration des tableaux de bord personnalisables.
"""
import json
import os
import logging
from typing import Dict, List, Any, Optional

class DashboardConfig:
    """Gestionnaire de configuration des tableaux de bord personnalisables."""
    
    def __init__(self, config_file: str = "dashboard_config.json"):
        """Initialise le gestionnaire de configuration."""
        self.config_file = config_file
        self.config_path = os.path.join("config", config_file)
        self.default_config = {
            "dashboards": [
                {
                    "id": "default",
                    "name": "Tableau de bord par défaut",
                    "widgets": [
                        {"type": "gauge", "title": "Stock Réception", "data_source": "stock_reception", "position": [0, 0]},
                        {"type": "gauge", "title": "Stock Concassage", "data_source": "stock_concassage", "position": [0, 1]},
                        {"type": "gauge", "title": "Stock Laverie", "data_source": "stock_laverie", "position": [0, 2]},
                        {"type": "table", "title": "Consommations", "data_source": "consumption_summary", "position": [1, 0]},
                        {"type": "table", "title": "Productivité", "data_source": "productivity_summary", "position": [1, 1]},
                        {"type": "table", "title": "Arrêts", "data_source": "downtime_summary", "position": [2, 0]},
                        {"type": "table", "title": "KPI Énergétiques", "data_source": "energy_kpi", "position": [2, 1]}
                    ]
                }
            ],
            "active_dashboard": "default"
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Charge la configuration depuis le fichier."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Créer le fichier avec la configuration par défaut
                self.save_config(self.default_config)
                return self.default_config
        except Exception as e:
            logging.error(f"Erreur lors du chargement de la configuration: {str(e)}")
            return self.default_config
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """Sauvegarde la configuration dans le fichier."""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            self.config = config
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde de la configuration: {str(e)}")
            return False
    
    def get_dashboard(self, dashboard_id: Optional[str] = None) -> Dict[str, Any]:
        """Récupère un tableau de bord par son ID."""
        if dashboard_id is None:
            dashboard_id = self.config.get("active_dashboard", "default")
        
        for dashboard in self.config.get("dashboards", []):
            if dashboard.get("id") == dashboard_id:
                return dashboard
        
        # Si le tableau de bord n'est pas trouvé, retourner le premier
        if self.config.get("dashboards"):
            return self.config["dashboards"][0]
        
        # Si aucun tableau de bord n'existe, retourner le tableau de bord par défaut
        return self.default_config["dashboards"][0]
    
    def get_all_dashboards(self) -> List[Dict[str, Any]]:
        """Récupère tous les tableaux de bord."""
        return self.config.get("dashboards", [])
    
    def add_dashboard(self, name: str) -> str:
        """Ajoute un nouveau tableau de bord."""
        import uuid
        
        dashboard_id = str(uuid.uuid4())
        new_dashboard = {
            "id": dashboard_id,
            "name": name,
            "widgets": []
        }
        
        self.config.setdefault("dashboards", []).append(new_dashboard)
        self.save_config(self.config)
        
        return dashboard_id
    
    def update_dashboard(self, dashboard_id: str, data: Dict[str, Any]) -> bool:
        """Met à jour un tableau de bord existant."""
        for i, dashboard in enumerate(self.config.get("dashboards", [])):
            if dashboard.get("id") == dashboard_id:
                self.config["dashboards"][i].update(data)
                return self.save_config(self.config)
        
        return False
    
    def delete_dashboard(self, dashboard_id: str) -> bool:
        """Supprime un tableau de bord."""
        for i, dashboard in enumerate(self.config.get("dashboards", [])):
            if dashboard.get("id") == dashboard_id:
                del self.config["dashboards"][i]
                
                # Si le tableau de bord supprimé était actif, définir le premier comme actif
                if self.config.get("active_dashboard") == dashboard_id and self.config.get("dashboards"):
                    self.config["active_dashboard"] = self.config["dashboards"][0]["id"]
                
                return self.save_config(self.config)
        
        return False
    
    def set_active_dashboard(self, dashboard_id: str) -> bool:
        """Définit le tableau de bord actif."""
        for dashboard in self.config.get("dashboards", []):
            if dashboard.get("id") == dashboard_id:
                self.config["active_dashboard"] = dashboard_id
                return self.save_config(self.config)
        
        return False
    
    def add_widget(self, dashboard_id: str, widget_data: Dict[str, Any]) -> bool:
        """Ajoute un widget à un tableau de bord."""
        for i, dashboard in enumerate(self.config.get("dashboards", [])):
            if dashboard.get("id") == dashboard_id:
                self.config["dashboards"][i].setdefault("widgets", []).append(widget_data)
                return self.save_config(self.config)
        
        return False
    
    def update_widget(self, dashboard_id: str, widget_index: int, widget_data: Dict[str, Any]) -> bool:
        """Met à jour un widget dans un tableau de bord."""
        for i, dashboard in enumerate(self.config.get("dashboards", [])):
            if dashboard.get("id") == dashboard_id and 0 <= widget_index < len(dashboard.get("widgets", [])):
                self.config["dashboards"][i]["widgets"][widget_index].update(widget_data)
                return self.save_config(self.config)
        
        return False
    
    def delete_widget(self, dashboard_id: str, widget_index: int) -> bool:
        """Supprime un widget d'un tableau de bord."""
        for i, dashboard in enumerate(self.config.get("dashboards", [])):
            if dashboard.get("id") == dashboard_id and 0 <= widget_index < len(dashboard.get("widgets", [])):
                del self.config["dashboards"][i]["widgets"][widget_index]
                return self.save_config(self.config)
        
        return False