"""
Classe de base optimisée pour tous les onglets avec fonctionnalités communes.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLineEdit, QDoubleSpinBox, QDateTimeEdit, QComboBox,
                            QPushButton, QTableWidget, QTableWidgetItem, QLabel,
                            QFrame, QSizePolicy, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDateTime, QTimer, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any, Optional

from ui.professional_theme import SotramineTheme
from ui.widgets.professional_widgets import ProfessionalTable
from models.enums import ProcessStepEnum, StockMovementType, QualityGrade

class BaseOptimizedTab(QWidget):
    """Classe de base pour tous les onglets optimisés."""
    
    def __init__(self, db_manager, title: str, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.title = title
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(10)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        
    def add_section(self, title: str) -> QVBoxLayout:
        """Ajoute une section avec titre."""
        group_box = QGroupBox(title)
        group_box.setFont(QFont("Segoe UI", 10, QFont.Bold))
        layout = QVBoxLayout(group_box)
        layout.setSpacing(8)
        self.main_layout.addWidget(group_box)
        return layout
    
    def add_form_section(self, title: str) -> QVBoxLayout:
        """Ajoute une section de formulaire."""
        return self.add_section(title)
    
    def add_table_section(self, title: str) -> ProfessionalTable:
        """Ajoute une section avec tableau."""
        layout = self.add_section(title)
        table = ProfessionalTable()
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(table)
        return table

    def add_actions_section(self, title: str) -> QHBoxLayout:
        """Ajoute une section d'actions avec boutons."""
        layout = self.add_section(title)
        actions_layout = QHBoxLayout()
        layout.addLayout(actions_layout)
        return actions_layout

    def add_data_table(self, title: str, headers: List[str]) -> ProfessionalTable:
        """Ajoute un tableau de données avec en-têtes."""
        layout = self.add_section(title)
        table = ProfessionalTable()
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(table)
        return table

    def add_stretch(self):
        """Ajoute un espace extensible."""
        self.main_layout.addStretch()

    def add_kpi_section(self, title: str) -> QVBoxLayout:
        """Ajoute une section KPI."""
        return self.add_section(title)

class QuickInputWidget(QFrame):
    """Widget de saisie rapide réutilisable."""
    
    data_saved = pyqtSignal(dict)
    
    def __init__(self, fields_config: List[Dict], parent=None):
        super().__init__(parent)
        self.fields_config = fields_config
        self.fields = {}
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface du widget."""
        self.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(self)
        
        # Créer les champs selon la configuration
        form_layout = QHBoxLayout()
        
        for field_config in self.fields_config:
            field_name = field_config['name']
            field_type = field_config['type']
            field_label = field_config.get('label', field_name)
            
            # Label
            label = QLabel(field_label + ":")
            form_layout.addWidget(label)
            
            # Champ de saisie
            if field_type == 'double':
                field = QDoubleSpinBox()
                field.setRange(0.0, 999999.0)
                field.setDecimals(2)
                field.setSuffix(" " + field_config.get('suffix', ''))
            elif field_type == 'datetime':
                field = QDateTimeEdit()
                field.setDateTime(QDateTime.currentDateTime())
                field.setCalendarPopup(True)
            elif field_type == 'combo':
                field = QComboBox()
                field.addItems(field_config.get('items', []))
            else:  # text
                field = QLineEdit()
            
            self.fields[field_name] = field
            form_layout.addWidget(field)
        
        layout.addLayout(form_layout)
        
        # Boutons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("💾 Enregistrer")
        self.save_button.clicked.connect(self.save_data)
        button_layout.addWidget(self.save_button)
        
        self.clear_button = QPushButton("🗑️ Effacer")
        self.clear_button.clicked.connect(self.clear_form)
        button_layout.addWidget(self.clear_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def save_data(self):
        """Sauvegarde les données du formulaire."""
        data = {}
        for field_name, field in self.fields.items():
            if isinstance(field, QDoubleSpinBox):
                data[field_name] = field.value()
            elif isinstance(field, QDateTimeEdit):
                data[field_name] = field.dateTime().toPyDateTime()
            elif isinstance(field, QComboBox):
                data[field_name] = field.currentText()
            else:
                data[field_name] = field.text()
        
        self.data_saved.emit(data)
    
    def clear_form(self):
        """Efface le formulaire."""
        for field in self.fields.values():
            if isinstance(field, QDoubleSpinBox):
                field.setValue(0.0)
            elif isinstance(field, QDateTimeEdit):
                field.setDateTime(QDateTime.currentDateTime())
            elif isinstance(field, QComboBox):
                field.setCurrentIndex(0)
            else:
                field.clear()

class DataLoader:
    """Chargeur de données optimisé avec cache."""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self._cache = {}
        self._cache_timeout = 60  # 60 secondes
        self._last_update = {}
    
    def get_movements(self, step: ProcessStepEnum, movement_type: Optional[StockMovementType] = None,
                     start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
                     limit: int = 100) -> List[Dict[str, Any]]:
        """Récupère les mouvements avec cache."""
        cache_key = f"{step.value}_{movement_type}_{start_date}_{end_date}_{limit}"
        
        # Vérifier le cache
        if (cache_key in self._cache and 
            cache_key in self._last_update and
            (datetime.now() - self._last_update[cache_key]).seconds < self._cache_timeout):
            return self._cache[cache_key]
        
        # Charger les données
        movements = self.db_manager.get_stock_movements(
            step=step,
            movement_type=movement_type,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        # Mettre en cache
        self._cache[cache_key] = movements
        self._last_update[cache_key] = datetime.now()
        
        return movements
    
    def invalidate_cache(self):
        """Invalide le cache."""
        self._cache.clear()
        self._last_update.clear()

class StatusWidget(QFrame):
    """Widget de statut simple."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface."""
        self.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(self)
        
        self.status_label = QLabel("Prêt")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
    
    def update_status(self, message: str, color: str = None):
        """Met à jour le statut."""
        self.status_label.setText(message)
        if color:
            self.status_label.setStyleSheet(f"color: {color};")

def populate_table_with_data(table: ProfessionalTable, data: List[Dict[str, Any]], 
                           columns: List[str]) -> None:
    """Fonction utilitaire pour remplir un tableau."""
    if not data:
        table.setRowCount(0)
        return
    
    table.setRowCount(len(data))
    table.setColumnCount(len(columns))
    table.setHorizontalHeaderLabels(columns)
    
    for row, item in enumerate(data):
        for col, column in enumerate(columns):
            value = item.get(column.lower().replace(' ', '_'), '')
            
            # Formatage spécial pour certains types
            if isinstance(value, float):
                value = f"{value:.2f}"
            elif isinstance(value, datetime):
                value = value.strftime("%Y-%m-%d %H:%M")
            
            table.setItem(row, col, table.itemClass()(str(value)))
    
    # Ajuster les colonnes
    table.resizeColumnsToContents()
    header = table.horizontalHeader()
    header.setStretchLastSection(True)
