# Standard library imports
import logging
from datetime import datetime, timedelta, date
from typing import Optional, List, Dict, Any

# Third-party imports
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QPushButton, QGroupBox,
                             QMessageBox, QDateEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QComboBox, QFrame, QFormLayout)

# Local imports
from models.enums import ResourceType

class ConsumptionTab(QWidget):
    """Onglet de saisie et suivi des consommations d'eau, d'énergie et de réactifs."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Groupe de saisie des consommations
        input_group = QGroupBox("Saisie des consommations journalières")
        input_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        input_layout = QFormLayout()
        input_layout.setSpacing(10)
        
        # Date de consommation
        self.consumption_date = QDateEdit()
        self.consumption_date.setCalendarPopup(True)
        self.consumption_date.setDate(QDate.currentDate())
        input_layout.addRow("Date:", self.consumption_date)
        
        # Consommation d'eau (m³)
        self.water_input = QLineEdit()
        self.water_input.setPlaceholderText("Consommation en m³")
        input_layout.addRow("Eau (m³):", self.water_input)
        
        # Consommation d'énergie (kWh)
        self.energy_input = QLineEdit()
        self.energy_input.setPlaceholderText("Consommation en kWh")
        input_layout.addRow("Énergie (kWh):", self.energy_input)
        
        # Consommation de réactifs (kg)
        self.reactive_input = QLineEdit()
        self.reactive_input.setPlaceholderText("Consommation en kg")
        input_layout.addRow("Réactifs (kg):", self.reactive_input)
        
        # Bouton d'ajout
        add_button = QPushButton("Ajouter consommation")
        add_button.clicked.connect(self.add_consumption)
        input_layout.addRow("", add_button)
        
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)
        
        # Groupe des consommations cumulées
        cumulative_group = QGroupBox("Consommations cumulées")
        cumulative_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        cumulative_layout = QGridLayout()
        cumulative_layout.setSpacing(20)
        
        # Titres des colonnes
        title_font = QFont()
        title_font.setBold(True)
        
        title_label = QLabel("Ressource")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(title_label, 0, 0)
        
        title_label = QLabel("Aujourd'hui")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(title_label, 0, 1)
        
        title_label = QLabel("Cette semaine")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(title_label, 0, 2)
        
        title_label = QLabel("Ce mois")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(title_label, 0, 3)
        
        # Ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        cumulative_layout.addWidget(line, 1, 0, 1, 4)
        
        # Eau
        water_label = QLabel("Eau")
        water_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(water_label, 2, 0)
        
        self.water_today_label = QLabel("0.00 m³")
        self.water_today_label.setAlignment(Qt.AlignCenter)
        self.water_today_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.water_today_label, 2, 1)
        
        self.water_week_label = QLabel("0.00 m³")
        self.water_week_label.setAlignment(Qt.AlignCenter)
        self.water_week_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.water_week_label, 2, 2)
        
        self.water_month_label = QLabel("0.00 m³")
        self.water_month_label.setAlignment(Qt.AlignCenter)
        self.water_month_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.water_month_label, 2, 3)
        
        # Énergie
        energy_label = QLabel("Énergie")
        energy_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(energy_label, 3, 0)
        
        self.energy_today_label = QLabel("0.00 kWh")
        self.energy_today_label.setAlignment(Qt.AlignCenter)
        self.energy_today_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.energy_today_label, 3, 1)
        
        self.energy_week_label = QLabel("0.00 kWh")
        self.energy_week_label.setAlignment(Qt.AlignCenter)
        self.energy_week_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.energy_week_label, 3, 2)
        
        self.energy_month_label = QLabel("0.00 kWh")
        self.energy_month_label.setAlignment(Qt.AlignCenter)
        self.energy_month_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.energy_month_label, 3, 3)
        
        # Réactifs
        reactive_label = QLabel("Réactifs")
        reactive_label.setAlignment(Qt.AlignCenter)
        cumulative_layout.addWidget(reactive_label, 4, 0)
        
        self.reactive_today_label = QLabel("0.00 kg")
        self.reactive_today_label.setAlignment(Qt.AlignCenter)
        self.reactive_today_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.reactive_today_label, 4, 1)
        
        self.reactive_week_label = QLabel("0.00 kg")
        self.reactive_week_label.setAlignment(Qt.AlignCenter)
        self.reactive_week_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.reactive_week_label, 4, 2)
        
        self.reactive_month_label = QLabel("0.00 kg")
        self.reactive_month_label.setAlignment(Qt.AlignCenter)
        self.reactive_month_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        cumulative_layout.addWidget(self.reactive_month_label, 4, 3)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("Rafraîchir les consommations")
        refresh_button.clicked.connect(self.update_consumption_summary)
        cumulative_layout.addWidget(refresh_button, 5, 0, 1, 4)
        
        cumulative_group.setLayout(cumulative_layout)
        main_layout.addWidget(cumulative_group)
        
        # Groupe de l'historique des consommations
        history_group = QGroupBox("Historique des consommations")
        history_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        history_layout = QVBoxLayout()
        
        # Filtres
        filters_layout = QHBoxLayout()
        filters_layout.setSpacing(15)
        
        # Filtre par ressource
        filters_layout.addWidget(QLabel("<b>Ressource:</b>"))
        self.filter_resource_combo = QComboBox()
        self.filter_resource_combo.addItem("Toutes les ressources")
        self.filter_resource_combo.addItem(ResourceType.WATER.value)
        self.filter_resource_combo.addItem(ResourceType.ENERGY.value)
        self.filter_resource_combo.addItem(ResourceType.REACTIVE.value)
        self.filter_resource_combo.currentTextChanged.connect(self.refresh_consumption_history)
        self.filter_resource_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.filter_resource_combo)
        
        # Filtre par période
        filters_layout.addWidget(QLabel("<b>Période:</b>"))
        self.filter_period_combo = QComboBox()
        self.filter_period_combo.addItems([
            "Aujourd'hui",
            "Cette semaine",
            "Ce mois",
            "Toutes les dates"
        ])
        self.filter_period_combo.currentTextChanged.connect(self.refresh_consumption_history)
        self.filter_period_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.filter_period_combo)
        
        # Bouton de rafraîchissement
        refresh_history_button = QPushButton("Rafraîchir")
        refresh_history_button.clicked.connect(self.refresh_consumption_history)
        filters_layout.addWidget(refresh_history_button)
        
        filters_layout.addStretch()
        history_layout.addLayout(filters_layout)
        
        # Ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        history_layout.addWidget(line)
        
        # Table des consommations
        self.consumption_table = QTableWidget()
        self.consumption_table.setColumnCount(4)
        self.consumption_table.setHorizontalHeaderLabels([
            "Date", "Ressource", "Quantité", "Unité"
        ])
        self.consumption_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.consumption_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.consumption_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.consumption_table.setAlternatingRowColors(True)
        self.consumption_table.verticalHeader().setVisible(False)
        self.consumption_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #e0e0ff;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                font-weight: bold;
                border: 1px solid #d0d0d0;
            }
        """)
        history_layout.addWidget(self.consumption_table)
        
        history_group.setLayout(history_layout)
        main_layout.addWidget(history_group)
        
        self.setLayout(main_layout)
        
        # Charger les données initiales
        self.update_consumption_summary()
        self.refresh_consumption_history()
    
    def add_consumption(self):
        """Ajoute une nouvelle consommation."""
        try:
            # Récupérer la date
            consumption_date = self.consumption_date.date().toPyDate()
            consumption_datetime = datetime.combine(consumption_date, datetime.min.time())
            
            # Vérifier si des valeurs ont été saisies
            water_str = self.water_input.text().strip()
            energy_str = self.energy_input.text().strip()
            reactive_str = self.reactive_input.text().strip()
            
            if not (water_str or energy_str or reactive_str):
                QMessageBox.warning(self, "Erreur de saisie", "Veuillez saisir au moins une consommation.")
                return
            
            # Ajouter la consommation d'eau si saisie
            if water_str:
                try:
                    water_quantity = float(water_str)
                    if water_quantity <= 0:
                        raise ValueError("La consommation d'eau doit être positive.")
                    # Utiliser None pour step_id au lieu de None pour step
                    self.db_manager.add_resource_consumption(
                        step=None,
                        resource_type=ResourceType.WATER,
                        quantity=water_quantity,
                        consumption_date=consumption_datetime
                    )
                except ValueError as e:
                    QMessageBox.warning(self, "Erreur de saisie", f"Eau: {str(e)}")
                    return
            
            # Ajouter la consommation d'énergie si saisie
            if energy_str:
                try:
                    energy_quantity = float(energy_str)
                    if energy_quantity <= 0:
                        raise ValueError("La consommation d'énergie doit être positive.")
                    self.db_manager.add_resource_consumption(
                        step=None,
                        resource_type=ResourceType.ENERGY,
                        quantity=energy_quantity,
                        consumption_date=consumption_datetime
                    )
                except ValueError as e:
                    QMessageBox.warning(self, "Erreur de saisie", f"Énergie: {str(e)}")
                    return
            
            # Ajouter la consommation de réactifs si saisie
            if reactive_str:
                try:
                    reactive_quantity = float(reactive_str)
                    if reactive_quantity <= 0:
                        raise ValueError("La consommation de réactifs doit être positive.")
                    self.db_manager.add_resource_consumption(
                        step=None,
                        resource_type=ResourceType.REACTIVE,
                        quantity=reactive_quantity,
                        consumption_date=consumption_datetime
                    )
                except ValueError as e:
                    QMessageBox.warning(self, "Erreur de saisie", f"Réactifs: {str(e)}")
                    return
            
            QMessageBox.information(self, "Succès", "Consommation(s) ajoutée(s) avec succès.")
            self.clear_inputs()
            self.update_consumption_summary()
            self.refresh_consumption_history()
            
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout de la consommation: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la consommation: {str(e)}")
    
    def clear_inputs(self):
        """Efface les champs de saisie."""
        self.consumption_date.setDate(QDate.currentDate())
        self.water_input.clear()
        self.energy_input.clear()
        self.reactive_input.clear()
    
    def update_consumption_summary(self):
        """Met à jour l'affichage des consommations cumulées."""
        try:
            # Dates pour les périodes
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Consommation d'eau
            water_today = self.get_resource_consumption(ResourceType.WATER, today)
            water_week = self.get_resource_consumption(ResourceType.WATER, start_of_week)
            water_month = self.get_resource_consumption(ResourceType.WATER, start_of_month)
            
            self.water_today_label.setText(f"{water_today:.2f} m³")
            self.water_week_label.setText(f"{water_week:.2f} m³")
            self.water_month_label.setText(f"{water_month:.2f} m³")
            
            # Consommation d'énergie
            energy_today = self.get_resource_consumption(ResourceType.ENERGY, today)
            energy_week = self.get_resource_consumption(ResourceType.ENERGY, start_of_week)
            energy_month = self.get_resource_consumption(ResourceType.ENERGY, start_of_month)
            
            self.energy_today_label.setText(f"{energy_today:.2f} kWh")
            self.energy_week_label.setText(f"{energy_week:.2f} kWh")
            self.energy_month_label.setText(f"{energy_month:.2f} kWh")
            
            # Consommation de réactifs
            reactive_today = self.get_resource_consumption(ResourceType.REACTIVE, today)
            reactive_week = self.get_resource_consumption(ResourceType.REACTIVE, start_of_week)
            reactive_month = self.get_resource_consumption(ResourceType.REACTIVE, start_of_month)
            
            self.reactive_today_label.setText(f"{reactive_today:.2f} kg")
            self.reactive_week_label.setText(f"{reactive_week:.2f} kg")
            self.reactive_month_label.setText(f"{reactive_month:.2f} kg")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des consommations: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la mise à jour des consommations: {str(e)}")
    
    def get_resource_consumption(self, resource_type: ResourceType, start_date: datetime) -> float:
        """Récupère la consommation d'une ressource pour une période donnée."""
        try:
            consumptions = self.db_manager.get_resource_consumptions(
                resource_type=resource_type,
                start_date=start_date
            )
            
            total = sum(consumption['quantity'] for consumption in consumptions)
            return total
            
        except Exception as e:
            logging.error(f"Erreur lors de la récupération des consommations: {str(e)}")
            return 0.0
    
    def refresh_consumption_history(self):
        """Rafraîchit la table de l'historique des consommations."""
        try:
            self.consumption_table.setRowCount(0)
            
            # Récupérer les filtres
            resource = self.filter_resource_combo.currentText()
            period = self.filter_period_combo.currentText()
            
            # Calculer la date de début selon la période
            start_date = None
            if period == "Aujourd'hui":
                start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "Cette semaine":
                start_date = datetime.now() - timedelta(days=datetime.now().weekday())
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "Ce mois":
                start_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # Récupérer les consommations
            resource_type = None
            if resource != "Toutes les ressources":
                for rt in ResourceType:
                    if rt.value == resource:
                        resource_type = rt
                        break
            
            consumptions = self.db_manager.get_resource_consumptions(
                resource_type=resource_type,
                start_date=start_date
            )
            
            # Définir des couleurs pour les différents types de ressources
            color_map = {
                ResourceType.WATER.value: QColor(230, 230, 255),  # Bleu clair
                ResourceType.ENERGY.value: QColor(255, 230, 230),  # Rouge clair
                ResourceType.REACTIVE.value: QColor(230, 255, 230)  # Vert clair
            }
            
            # Définir les unités pour chaque type de ressource
            unit_map = {
                ResourceType.WATER.value: "m³",
                ResourceType.ENERGY.value: "kWh",
                ResourceType.REACTIVE.value: "kg"
            }
            
            for consumption in consumptions:
                row_position = self.consumption_table.rowCount()
                self.consumption_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(consumption['consumption_date'].strftime("%d/%m/%Y"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 0, date_item)
                
                # Ressource
                resource_item = QTableWidgetItem(consumption['resource_type'])
                resource_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 1, resource_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{consumption['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 2, quantity_item)
                
                # Unité
                unit_item = QTableWidgetItem(unit_map.get(consumption['resource_type'], ""))
                unit_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 3, unit_item)
                
                # Appliquer une couleur de fond selon le type de ressource
                if consumption['resource_type'] in color_map:
                    for col in range(4):
                        item = self.consumption_table.item(row_position, col)
                        if item:
                            item.setBackground(color_map[consumption['resource_type']])
            
            # Afficher un message si aucune consommation n'est trouvée
            if self.consumption_table.rowCount() == 0:
                self.consumption_table.setRowCount(1)
                no_data_item = QTableWidgetItem("Aucune consommation trouvée pour les critères sélectionnés")
                no_data_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setSpan(0, 0, 1, 4)
                self.consumption_table.setItem(0, 0, no_data_item)
                
        except Exception as e:
            logging.error(f"Erreur lors du chargement des consommations: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
    
    def refresh_tab(self):
        """Rafraîchit l'onglet lorsqu'il est sélectionné."""
        self.update_consumption_summary()
        self.refresh_consumption_history()