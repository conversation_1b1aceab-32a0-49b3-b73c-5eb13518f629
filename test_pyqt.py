import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel

def main():
    print("Creating application...")
    app = QApplication(sys.argv)
    print("Application created")
    
    print("Creating window...")
    window = QMainWindow()
    window.setWindowTitle("PyQt5 Test")
    window.setGeometry(100, 100, 400, 300)
    print("Window created")
    
    print("Adding label...")
    label = QLabel("Hello, PyQt5!", window)
    label.move(150, 150)
    print("Label added")
    
    print("Showing window...")
    window.show()
    print("Window shown")
    
    print("Starting event loop...")
    return app.exec_()

if __name__ == "__main__":
    print("Starting test...")
    try:
        exit_code = main()
        print(f"Application exited with code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)