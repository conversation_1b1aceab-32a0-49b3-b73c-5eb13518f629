"""
Onglet stock simplifié et fonctionnel.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QTableWidget, QTableWidgetItem, QWidget)
from PyQt5.QtCore import Qt, QTimer
from datetime import datetime
import logging

from .base_optimized import BaseOptimizedTab
from ui.widgets.professional_widgets import KPICard
from models.enums import ProcessStepEnum, StockMovementType

class SimpleStockTab(BaseOptimizedTab):
    """Onglet stock simplifié."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📦 Stocks", parent)
        self.setup_interface()
        self.setup_timers()
    
    def setup_interface(self):
        """Configure l'interface."""
        # Section KPI
        kpi_layout = self.add_section("📊 Stocks Actuels")
        
        # Cartes de stock
        cards_layout = QHBoxLayout()
        
        self.reception_card = KPICard("Réception", "0", "T")
        self.concassage_card = KPICard("Concassage", "0", "T")
        self.laverie_card = KPICard("Laverie", "0", "T")
        self.stockage_card = KPICard("Stockage Final", "0", "T")
        
        cards_layout.addWidget(self.reception_card)
        cards_layout.addWidget(self.concassage_card)
        cards_layout.addWidget(self.laverie_card)
        cards_layout.addWidget(self.stockage_card)
        
        kpi_layout.addLayout(cards_layout)
        
        # Section Contrôles
        controls_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.clicked.connect(self.refresh_data)
        controls_layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("📊 Exporter")
        self.export_btn.clicked.connect(self.export_data)
        controls_layout.addWidget(self.export_btn)
        
        controls_layout.addStretch()
        
        # Ajouter les contrôles
        controls_widget = QWidget()
        controls_widget.setLayout(controls_layout)
        self.main_layout.addWidget(controls_widget)
        
        # Tableau des mouvements
        self.data_table = self.add_table_section("📈 Mouvements de Stock")
        headers = ["Date/Heure", "Étape", "Type", "Quantité", "Qualité", "Référence"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)
        
        # Charger les données
        self.refresh_data()
    
    def setup_timers(self):
        """Configure les timers."""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        # self.refresh_timer.start(60000)  # Actualiser toutes les minutes
    
    def refresh_data(self):
        """Actualise les données."""
        try:
            # Mettre à jour les KPI
            self.update_stock_cards()
            
            # Mettre à jour le tableau
            self.update_movements_table()
            
        except Exception as e:
            logging.error(f"Erreur actualisation stock: {str(e)}")
    
    def update_stock_cards(self):
        """Met à jour les cartes de stock."""
        try:
            for step in ProcessStepEnum:
                stock = self.db_manager.get_current_stock(step)
                
                if step == ProcessStepEnum.RECEPTION:
                    self.reception_card.update_value(f"{stock:.1f}")
                elif step == ProcessStepEnum.CONCASSAGE:
                    self.concassage_card.update_value(f"{stock:.1f}")
                elif step == ProcessStepEnum.LAVERIE:
                    self.laverie_card.update_value(f"{stock:.1f}")
                elif step == ProcessStepEnum.STOCKAGE_FINAL:
                    self.stockage_card.update_value(f"{stock:.1f}")
                    
        except Exception as e:
            logging.error(f"Erreur mise à jour cartes stock: {str(e)}")
    
    def update_movements_table(self):
        """Met à jour le tableau des mouvements."""
        try:
            # Récupérer les mouvements récents
            all_movements = []
            
            for step in ProcessStepEnum:
                movements = self.db_manager.get_stock_movements(step=step, limit=20)
                all_movements.extend(movements)
            
            # Trier par date (plus récent en premier)
            all_movements.sort(key=lambda x: x.get('date', datetime.min), reverse=True)
            
            # Limiter à 50 mouvements
            all_movements = all_movements[:50]
            
            # Remplir le tableau
            self.data_table.setRowCount(len(all_movements))
            
            for row, movement in enumerate(all_movements):
                # Date/Heure
                date_str = movement.get('date', datetime.now()).strftime("%Y-%m-%d %H:%M")
                self.data_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Étape
                step_name = movement.get('step', 'N/A')
                self.data_table.setItem(row, 1, QTableWidgetItem(str(step_name)))
                
                # Type
                movement_type = movement.get('movement_type', 'N/A')
                self.data_table.setItem(row, 2, QTableWidgetItem(str(movement_type)))
                
                # Quantité
                quantity = movement.get('quantity', 0)
                quantity_str = f"{quantity:+.1f}"
                self.data_table.setItem(row, 3, QTableWidgetItem(quantity_str))
                
                # Qualité
                quality = movement.get('quality', '')
                quality_str = f"{quality:.1f}%" if quality else "-"
                self.data_table.setItem(row, 4, QTableWidgetItem(quality_str))
                
                # Référence
                reference = movement.get('reference_id', '')
                self.data_table.setItem(row, 5, QTableWidgetItem(str(reference)))
            
            # Ajuster les colonnes
            self.data_table.resizeColumnsToContents()
            
        except Exception as e:
            logging.error(f"Erreur mise à jour tableau mouvements: {str(e)}")
    
    def export_data(self):
        """Exporte les données."""
        try:
            from PyQt5.QtWidgets import QMessageBox, QFileDialog
            
            # Demander le fichier de destination
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter les données de stock",
                f"stock_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Fichiers CSV (*.csv)"
            )
            
            if file_path:
                # Récupérer toutes les données
                all_movements = []
                for step in ProcessStepEnum:
                    movements = self.db_manager.get_stock_movements(step=step, limit=1000)
                    all_movements.extend(movements)
                
                # Créer le CSV
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # En-têtes
                    writer.writerow(['Date/Heure', 'Étape', 'Type', 'Quantité', 'Qualité', 'Référence'])
                    
                    # Données
                    for movement in all_movements:
                        writer.writerow([
                            movement.get('date', '').strftime("%Y-%m-%d %H:%M") if movement.get('date') else '',
                            movement.get('step', ''),
                            movement.get('movement_type', ''),
                            movement.get('quantity', 0),
                            movement.get('quality', ''),
                            movement.get('reference_id', '')
                        ])
                
                QMessageBox.information(self, "Export réussi", f"Données exportées vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export: {str(e)}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Erreur", f"Erreur lors de l'export: {str(e)}")

# Alias pour compatibilité
OptimizedStockTab = SimpleStockTab
