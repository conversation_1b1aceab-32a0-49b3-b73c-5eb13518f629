# Standard library imports
import logging

# Third-party imports
from PyQt5.QtCore import <PERSON>D<PERSON>, Qt, QTimer, QSize
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QDateEdit,
                             QComboBox, QMessageBox, QTabWidget, QGroupBox, QGridLayout, QSplitter,
                             QFormLayout, QToolButton, QFrame)

# Local imports
from database.database_manager import DatabaseManager
from models.enums import ProcessStepEnum, ResourceType, MovementTypeEnum

class CollapsibleSection(QWidget):
    """Widget pour une section réductible/extensible."""
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.content = QWidget()
        self.content_layout = QVBoxLayout(self.content)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        self.init_ui()
        
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # En-tête avec bouton d'expansion/réduction
        header = QWidget()
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(5, 5, 5, 5)
        
        self.toggle_button = QToolButton()
        self.toggle_button.setArrowType(Qt.DownArrow)
        self.toggle_button.setCheckable(True)
        self.toggle_button.setChecked(True)
        self.toggle_button.clicked.connect(self.toggle_content)
        header_layout.addWidget(self.toggle_button)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        main_layout.addWidget(header)
        main_layout.addWidget(separator)
        main_layout.addWidget(self.content)
        
    def toggle_content(self):
        self.content.setVisible(self.toggle_button.isChecked())
        self.toggle_button.setArrowType(Qt.DownArrow if self.toggle_button.isChecked() else Qt.RightArrow)
        
    def add_widget(self, widget):
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        self.content_layout.addLayout(layout)

class IndicatorWidget(QFrame):
    """Widget pour afficher un indicateur avec titre et valeur."""
    
    def __init__(self, title, value="--", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        title_label = QLabel(self.title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; color: #555;")
        
        self.value_label = QLabel(self.value)
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d7;")
        
        layout.addWidget(title_label)
        layout.addWidget(self.value_label)
        
    def set_value(self, value):
        self.value = value
        self.value_label.setText(value)

class LaverieTab(QWidget):
    def __init__(self, db_manager, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.db_manager = db_manager
        self.process_step = ProcessStepEnum.LAVERIE
        self.logger = logging.getLogger(__name__)
        self.init_ui()
        self.refresh_tab()

        self.timer = QTimer(self)
        self.timer.setInterval(60000)  # 1 minute
        self.timer.timeout.connect(self.refresh_tab)
        self.timer.start()

    def init_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)

        # Indicateurs en haut
        indicators_group = QGroupBox("Indicateurs de Performance")
        indicators_layout = QGridLayout(indicators_group)
        
        self.stock_indicator = IndicatorWidget("Stock Disponible")
        self.productivity_indicator = IndicatorWidget("Productivité")
        self.yield_indicator = IndicatorWidget("Rendement")
        self.availability_indicator = IndicatorWidget("Disponibilité")
        
        indicators_layout.addWidget(self.stock_indicator, 0, 0)
        indicators_layout.addWidget(self.productivity_indicator, 0, 1)
        indicators_layout.addWidget(self.yield_indicator, 0, 2)
        indicators_layout.addWidget(self.availability_indicator, 0, 3)
        
        main_layout.addWidget(indicators_group)
        
        # Onglets pour Production et Temps d'Arrêt
        tabs = QTabWidget()
        
        # Onglet Production
        production_tab = QWidget()
        production_layout = QVBoxLayout(production_tab)
        
        # Formulaire de saisie de production
        production_form_group = QGroupBox("Ajouter une Production")
        production_form_layout = QFormLayout(production_form_group)
        
        self.prod_date_input = QDateEdit(calendarPopup=True)
        self.prod_date_input.setDate(QDate.currentDate())
        production_form_layout.addRow("Date:", self.prod_date_input)
        
        self.prod_hours_input = QLineEdit()
        self.prod_hours_input.setPlaceholderText("Heures (ex: 8.5)")
        production_form_layout.addRow("Heures:", self.prod_hours_input)
        
        self.prod_quantity_input = QLineEdit()
        self.prod_quantity_input.setPlaceholderText("Quantité Produite")
        production_form_layout.addRow("Quantité Produite:", self.prod_quantity_input)
        
        self.prod_quality_input = QLineEdit()
        self.prod_quality_input.setPlaceholderText("Qualité P2O5 (%)")
        production_form_layout.addRow("Qualité P2O5 (%):", self.prod_quality_input)
        
        self.prod_quantity_used_input = QLineEdit()
        self.prod_quantity_used_input.setPlaceholderText("Quantité Utilisée")
        production_form_layout.addRow("Quantité Utilisée:", self.prod_quantity_used_input)
        
        add_production_button = QPushButton("Ajouter Production")
        add_production_button.clicked.connect(self.add_production)
        production_form_layout.addRow("", add_production_button)
        
        production_layout.addWidget(production_form_group)
        
        # Tableau des productions
        production_table_section = CollapsibleSection("Historique des Productions")
        self.production_table = QTableWidget()
        self.production_table.setColumnCount(6)
        self.production_table.setHorizontalHeaderLabels(["Date", "Heures", "Quantité Produite", "Qualité", "Quantité Utilisée", "Action"])
        self.production_table.horizontalHeader().setStretchLastSection(True)
        self.production_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.production_table.setSelectionBehavior(QTableWidget.SelectRows)
        production_table_section.add_widget(self.production_table)
        
        production_layout.addWidget(production_table_section)
        production_layout.addStretch()
        
        # Onglet Temps d'Arrêt
        downtime_tab = QWidget()
        downtime_layout = QVBoxLayout(downtime_tab)
        
        # Formulaire de saisie de temps d'arrêt
        downtime_form_group = QGroupBox("Ajouter un Temps d'Arrêt")
        downtime_form_layout = QFormLayout(downtime_form_group)
        
        self.downtime_date_input = QDateEdit(calendarPopup=True)
        self.downtime_date_input.setDate(QDate.currentDate())
        downtime_form_layout.addRow("Date:", self.downtime_date_input)
        
        self.downtime_duration_input = QLineEdit()
        self.downtime_duration_input.setPlaceholderText("Durée (heures)")
        downtime_form_layout.addRow("Durée:", self.downtime_duration_input)
        
        self.downtime_reason_input = QLineEdit()
        self.downtime_reason_input.setPlaceholderText("Raison")
        downtime_form_layout.addRow("Raison:", self.downtime_reason_input)
        
        add_downtime_button = QPushButton("Ajouter Temps d'Arrêt")
        add_downtime_button.clicked.connect(self.add_downtime)
        downtime_form_layout.addRow("", add_downtime_button)
        
        downtime_layout.addWidget(downtime_form_group)
        
        # Tableau des temps d'arrêt
        downtime_table_section = CollapsibleSection("Historique des Temps d'Arrêt")
        self.downtime_table = QTableWidget()
        self.downtime_table.setColumnCount(4)
        self.downtime_table.setHorizontalHeaderLabels(["Date", "Durée", "Raison", "Action"])
        self.downtime_table.horizontalHeader().setStretchLastSection(True)
        self.downtime_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.downtime_table.setSelectionBehavior(QTableWidget.SelectRows)
        downtime_table_section.add_widget(self.downtime_table)
        
        downtime_layout.addWidget(downtime_table_section)
        downtime_layout.addStretch()
        
        # Ajouter les onglets
        tabs.addTab(production_tab, "Production")
        tabs.addTab(downtime_tab, "Temps d'Arrêt")
        
        main_layout.addWidget(tabs)
        
        self.setLayout(main_layout)

    def refresh_tab(self):
        self.logger.info(f"Refreshing Laverie tab for process step: {self.process_step.name}")
        self.load_production_data()
        self.load_downtime_data()
        self.update_indicators()
        self.clear_inputs()

    def load_production_data(self):
        self.production_table.setRowCount(0)
        try:
            productions = self.db_manager.get_production_summary(self.process_step)
            for row_idx, prod in enumerate(productions):
                self.production_table.insertRow(row_idx)
                # Date
                date_item = QTableWidgetItem(prod['production_date'].strftime("%d/%m/%Y"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_idx, 0, date_item)
                
                # Heures
                hours_value = prod['production_hours']
                hours_item = QTableWidgetItem(f"{hours_value:.2f}" if hours_value is not None else "N/A")
                hours_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_idx, 1, hours_item)
                
                # Quantité produite
                quantity_item = QTableWidgetItem(f"{prod['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_idx, 2, quantity_item)
                
                # Qualité
                quality_value = prod['quality']
                if quality_value is not None:
                    if isinstance(quality_value, (int, float)):
                        quality_text = f"{quality_value:.2f}%"
                    else:
                        quality_text = str(quality_value)
                else:
                    quality_text = "N/A"
                quality_item = QTableWidgetItem(quality_text)
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_idx, 3, quality_item)
                
                # Quantité utilisée
                quantity_used = prod['quantity_used']
                quantity_used_item = QTableWidgetItem(f"{quantity_used:.2f}" if quantity_used is not None else "N/A")
                quantity_used_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_idx, 4, quantity_used_item)
                delete_button = QPushButton("Supprimer")
                delete_button.clicked.connect(lambda checked, p_id=prod['id']: self.delete_production_row(p_id))
                self.production_table.setCellWidget(row_idx, 5, delete_button)
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données de production: {str(e)}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")

    def load_downtime_data(self):
        self.downtime_table.setRowCount(0)
        try:
            downtimes = self.db_manager.get_downtime_summary(self.process_step)
            for row_idx, dt in enumerate(downtimes):
                self.downtime_table.insertRow(row_idx)
                self.downtime_table.setItem(row_idx, 0, QTableWidgetItem(str(dt['downtime_date'])))
                self.downtime_table.setItem(row_idx, 1, QTableWidgetItem(str(dt['duration'])))
                self.downtime_table.setItem(row_idx, 2, QTableWidgetItem(dt['reason']))
                delete_button = QPushButton("Supprimer")
                delete_button.clicked.connect(lambda checked, dt_id=dt['id']: self.delete_downtime_row(dt_id))
                self.downtime_table.setCellWidget(row_idx, 3, delete_button)
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données d'arrêt: {str(e)}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")

    def update_indicators(self):
        self.logger.info(f"Updating indicators for Laverie tab, process step: {self.process_step.name}")
        try:
            # Stock Disponible du Concassage pour la Laverie
            crushing_stock = self.db_manager.get_available_crushing_stock()
            self.stock_indicator.set_value(f"{crushing_stock:.2f} tonnes")
            
            # Colorer l'indicateur de stock en fonction de la valeur
            if crushing_stock < 100:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e81123;")  # Rouge
            elif crushing_stock < 500:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")  # Orange
            else:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #107c10;")  # Vert

            # Récupérer les données de production
            productions = self.db_manager.get_production_summary(self.process_step)
            
            # Calculer les totaux avec vérification des valeurs nulles
            total_quantity_produced = sum(prod['quantity'] for prod in productions if prod['quantity'] is not None)
            total_quantity_used = sum(prod['quantity_used'] for prod in productions if prod['quantity_used'] is not None)
            total_production_hours = sum(prod['production_hours'] for prod in productions if prod['production_hours'] is not None)
            
            self.logger.info(f"Total produit: {total_quantity_produced}, Total utilisé: {total_quantity_used}, Heures: {total_production_hours}")
            
            # Productivité (Quantité Produite / Heures de Production)
            if total_production_hours > 0:
                productivity = total_quantity_produced / total_production_hours
                self.productivity_indicator.set_value(f"{productivity:.2f} T/heure")
                
                # Colorer l'indicateur de productivité
                self._color_indicator(self.productivity_indicator, productivity, 5, 10)
            else:
                self.productivity_indicator.set_value("N/A")
            
            # Rendement (Quantité Produite / Quantité Utilisée * 100)
            if total_quantity_used > 0:
                yield_value = (total_quantity_produced / total_quantity_used) * 100
                self.yield_indicator.set_value(f"{yield_value:.2f}%")
                
                # Colorer l'indicateur de rendement
                self._color_indicator(self.yield_indicator, yield_value, 50, 75)
            else:
                self.yield_indicator.set_value("N/A")

            # Disponibilité (Heures de Production / (Heures de Production + Heures d'Arrêt))
            downtimes = self.db_manager.get_downtime_summary(self.process_step)
            total_downtime_hours = sum(dt['duration'] for dt in downtimes if dt['duration'] is not None)
            
            if (total_production_hours + total_downtime_hours) > 0:
                availability = (total_production_hours / (total_production_hours + total_downtime_hours)) * 100
                self.availability_indicator.set_value(f"{availability:.2f}%")
                
                # Colorer l'indicateur de disponibilité
                self._color_indicator(self.availability_indicator, availability, 60, 80)
            else:
                self.availability_indicator.set_value("100.00%")
                self.availability_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #107c10;")  # Vert

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour des indicateurs: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _color_indicator(self, indicator, value, low_threshold, medium_threshold):
        """Colore un indicateur selon les seuils de valeur."""
        if value < low_threshold:
            color = "#e81123"  # Rouge
        elif value < medium_threshold:
            color = "#ff8c00"  # Orange
        else:
            color = "#107c10"  # Vert
        indicator.value_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {color};")
            
    def add_production(self):
        try:
            date = self.prod_date_input.date().toPyDate()
            hours = float(self.prod_hours_input.text())
            quantity_produced = float(self.prod_quantity_input.text())
            
            # Récupérer et valider la qualité comme un pourcentage
            quality_str = self.prod_quality_input.text().strip()
            if not quality_str:
                quality = None
            else:
                quality = float(quality_str)
                if quality < 0 or quality > 100:
                    raise ValueError("La qualité doit être un pourcentage entre 0 et 100")
                    
            quantity_used = float(self.prod_quantity_used_input.text())

            self.logger.info(f"Adding production: Date={date}, Hours={hours}, Quantity Produced={quantity_produced}, Quality={quality}, Quantity Used={quantity_used}")

            self.db_manager.add_production(
                step=self.process_step,
                production_date=date,
                production_hours=hours,
                quantity=quantity_produced,
                quality=quality,
                quantity_used=quantity_used
            )
            self.refresh_tab()
        except ValueError as e:
            QMessageBox.warning(self, "Erreur de saisie", str(e))
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout de la production: {str(e)}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}") 

    def add_downtime(self):
        try:
            date = self.downtime_date_input.date().toPyDate()
            duration = float(self.downtime_duration_input.text())
            reason = self.downtime_reason_input.text()

            if not reason:
                raise ValueError("La raison de l'arrêt est obligatoire")

            self.db_manager.add_downtime(
                step=self.process_step,
                reason=reason,
                duration=duration,
                downtime_date=date
            )
            QMessageBox.information(self, "Succès", "Temps d'arrêt ajouté avec succès.")
            self.refresh_tab()
        except ValueError as e:
            QMessageBox.warning(self, "Erreur", str(e))
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout du temps d'arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'ajout du temps d'arrêt: {e}")

    def _confirm_deletion(self, title, message):
        """Affiche une boîte de dialogue de confirmation de suppression."""
        reply = QMessageBox.question(self, title, message,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        return reply == QMessageBox.Yes

    def delete_production_row(self, production_id):
        if self._confirm_deletion('Supprimer Production', 
                                  'Êtes-vous sûr de vouloir supprimer cette production ?'):
            try:
                self.db_manager.delete_production(production_id)
                QMessageBox.information(self, "Succès", "Production supprimée avec succès.")
                self.refresh_tab()
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de la suppression de la production: {e}")

    def delete_downtime_row(self, downtime_id):
        if self._confirm_deletion('Supprimer Temps d\'Arrêt', 
                                  'Êtes-vous sûr de vouloir supprimer ce temps d\'arrêt ?'):
            try:
                self.db_manager.delete_downtime(downtime_id)
                QMessageBox.information(self, "Succès", "Temps d'arrêt supprimé avec succès.")
                self.refresh_tab()
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de la suppression du temps d'arrêt: {e}")

    def clear_inputs(self):
        """Efface les champs de saisie."""
        self.prod_date_input.setDate(QDate.currentDate())
        self.prod_hours_input.clear()
        self.prod_quantity_input.clear()
        self.prod_quality_input.clear()
        self.prod_quantity_used_input.clear()
        self.downtime_date_input.setDate(QDate.currentDate())
        self.downtime_duration_input.clear()
        self.downtime_reason_input.clear()
