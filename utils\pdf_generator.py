"""
Module pour générer des rapports PDF.
"""
import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus import PageBreak, KeepTogether
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.piecharts import Pie

class PDFGenerator:
    """Classe pour générer des rapports PDF."""
    
    def __init__(self, output_dir="exports"):
        """Initialise le générateur de PDF."""
        self.output_dir = output_dir
        self.styles = getSampleStyleSheet()
        
        # Créer le répertoire de sortie s'il n'existe pas
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Ajouter des styles personnalisés
        try:
            self.styles.add(ParagraphStyle(
                name='PDFTitle',
                parent=self.styles['Heading1'],
                fontSize=16,
                alignment=1,  # Centre
                spaceAfter=12
            ))
        except KeyError:
            # Si le style existe déjà, on le récupère
            pass
        
        try:
            self.styles.add(ParagraphStyle(
                name='PDFSubtitle',
                parent=self.styles['Heading2'],
                fontSize=14,
                alignment=1,  # Centre
                spaceAfter=10
            ))
        except KeyError:
            # Si le style existe déjà, on le récupère
            pass
        
        try:
            self.styles.add(ParagraphStyle(
                name='PDFTableHeader',
                parent=self.styles['Normal'],
                fontSize=10,
                alignment=1,  # Centre
                textColor=colors.white,
                backColor=colors.darkblue
            ))
        except KeyError:
            # Si le style existe déjà, on le récupère
            pass
        
        try:
            self.styles.add(ParagraphStyle(
                name='PDFFooter',
                parent=self.styles['Normal'],
                fontSize=8,
                alignment=1,  # Centre
                textColor=colors.gray
            ))
        except KeyError:
            # Si le style existe déjà, on le récupère
            pass
    
    def add_company_header(self, content):
        """Ajoute l'en-tête professionnel avec logo entreprise."""
        try:
            # Vérifier si le logo existe
            logo_path = "logo.png"
            if os.path.exists(logo_path):
                # Créer l'image du logo
                logo = Image(logo_path, width=3*cm, height=2*cm)
                
                # En-tête avec logo et informations société
                header_data = [
                    [logo, "SOTRAMINE PHOSPHATE", f"Rapport Journalier de Production"],
                    ["", "Direction de la Production", f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"]
                ]
                
                header_table = Table(header_data, colWidths=[3.5*cm, 7*cm, 7.5*cm])
                header_table.setStyle(TableStyle([
                    # Styles pour le logo
                    ('VALIGN', (0, 0), (0, 1), 'MIDDLE'),
                    ('ALIGN', (0, 0), (0, 1), 'CENTER'),
                    
                    # Styles pour le texte société
                    ('FONTNAME', (1, 0), (1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (1, 0), (1, 0), 14),
                    ('FONTSIZE', (1, 1), (1, 1), 10),
                    ('TEXTCOLOR', (1, 0), (1, -1), colors.darkblue),
                    ('VALIGN', (1, 0), (1, -1), 'MIDDLE'),
                    
                    # Styles pour le titre rapport
                    ('FONTNAME', (2, 0), (2, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (2, 0), (2, 0), 16),
                    ('FONTSIZE', (2, 1), (2, 1), 10),
                    ('TEXTCOLOR', (2, 0), (2, -1), colors.darkblue),
                    ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
                    ('VALIGN', (2, 0), (2, -1), 'MIDDLE'),
                    
                    # Fusion des cellules pour le logo
                    ('SPAN', (0, 0), (0, 1)),
                ]))
            else:
                # Fallback sans logo si fichier non trouvé
                header_data = [
                    ["SOTRAMINE PHOSPHATE", f"Rapport Journalier de Production"],
                    ["Direction de la Production", f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"]
                ]
                
                header_table = Table(header_data, colWidths=[9*cm, 9*cm])
                header_table.setStyle(TableStyle([
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (0, 0), 14),
                    ('FONTSIZE', (1, 0), (1, 0), 16),
                    ('FONTSIZE', (0, 1), (-1, 1), 10),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.darkblue),
                    ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ]))
        
        except Exception as e:
            # En cas d'erreur avec le logo, utiliser la version texte
            header_data = [
                ["SOTRAMINE PHOSPHATE", f"Rapport Journalier de Production"],
                ["Direction de la Production", f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"]
            ]
            
            header_table = Table(header_data, colWidths=[9*cm, 9*cm])
            header_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, 0), 14),
                ('FONTSIZE', (1, 0), (1, 0), 16),
                ('FONTSIZE', (0, 1), (-1, 1), 10),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.darkblue),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
        
        content.append(header_table)
        content.append(Spacer(1, 15))
        
        # Ligne de séparation
        line_data = [["" for _ in range(10)]]
        line_table = Table(line_data, colWidths=[1.8*cm]*10)
        line_table.setStyle(TableStyle([
            ('LINEABOVE', (0, 0), (-1, 0), 3, colors.darkblue),
        ]))
        content.append(line_table)
        content.append(Spacer(1, 10))

    def add_kpi_dashboard(self, content, data, doc):
        """Ajoute le tableau de bord KPI avec 4 indicateurs colorés."""
        # Calculer les KPI
        production_data = data.get('production', {})
        
        total_production = sum(step_data.get('quantity', 0) for step_data in production_data.values())
        total_hours = sum(step_data.get('hours', 0) for step_data in production_data.values())
        
        # Disponibilité (% du temps planifié)
        disponibilite = min(100, (total_hours / 24) * 100) if total_hours > 0 else 0
        
        # Qualité moyenne pondérée
        weighted_quality = 0
        total_qty = 0
        for step_data in production_data.values():
            quality_str = step_data.get('quality', '0')
            if quality_str and quality_str != '-':
                try:
                    quality_val = float(quality_str.replace('%', ''))
                    qty = step_data.get('quantity', 0)
                    weighted_quality += quality_val * qty
                    total_qty += qty
                except:
                    pass
        
        avg_quality = weighted_quality / total_qty if total_qty > 0 else 0
        global_yield = total_production / total_hours if total_hours > 0 else 0
        
        # Titre KPI
        content.append(Paragraph("📊 INDICATEURS CLÉS DE PERFORMANCE", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        # Tableau KPI 2x2 avec couleurs
        kpi_data = [
            [
                Paragraph(f"<b>PRODUCTION TOTALE</b><br/><font size=16>{total_production:.1f} T</font>", self.styles['Normal']),
                Paragraph(f"<b>DISPONIBILITÉ</b><br/><font size=16>{disponibilite:.1f}%</font>", self.styles['Normal'])
            ],
            [
                Paragraph(f"<b>QUALITÉ MOYENNE</b><br/><font size=16>{avg_quality:.1f}% P2O5</font>", self.styles['Normal']),
                Paragraph(f"<b>RENDEMENT GLOBAL</b><br/><font size=16>{global_yield:.1f} T/h</font>", self.styles['Normal'])
            ]
        ]
        
        kpi_table = Table(kpi_data, colWidths=[doc.width/2.0]*2, rowHeights=[40, 40])
        kpi_table.setStyle(TableStyle([
            # Couleurs thématiques
            ('BACKGROUND', (0, 0), (0, 0), colors.Color(0.2, 0.4, 0.8)),  # Bleu industrie
            ('BACKGROUND', (1, 0), (1, 0), colors.Color(0.0, 0.6, 0.3)),  # Vert performance
            ('BACKGROUND', (0, 1), (0, 1), colors.Color(0.8, 0.4, 0.0)),  # Orange qualité
            ('BACKGROUND', (1, 1), (1, 1), colors.Color(0.6, 0.2, 0.6)),  # Violet rendement
            
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.white),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 2, colors.darkblue),
        ]))
        
        content.append(kpi_table)
        content.append(Spacer(1, 15))

    def add_downtime_analysis(self, content, doc):
        """Ajoute l'analyse détaillée des arrêts."""
        content.append(Paragraph("⚠️ ANALYSE DES ARRÊTS", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        # Données d'arrêts (simulation - à remplacer par vraies données)
        downtime_data = [
            ["Heure", "Étape", "Durée (min)", "Motif", "Impact (T)"],
            ["08:30", "Concassage", "45", "Maintenance préventive", "15.2"],
            ["14:15", "Laverie", "20", "Problème qualité eau", "8.5"],
            ["16:45", "Concassage", "30", "Bourrage trémie", "10.1"],
            ["19:20", "Laverie", "15", "Changement filtre", "5.3"]
        ]
        
        downtime_table = Table(downtime_data, colWidths=[doc.width/5.0]*5)
        downtime_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.red),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        content.append(downtime_table)
        content.append(Spacer(1, 8))
        
        # Résumé des arrêts
        total_downtime = 45 + 20 + 30 + 15  # minutes
        total_impact = 15.2 + 8.5 + 10.1 + 5.3  # tonnes
        
        summary_data = [
            ["Temps d'arrêt total:", f"{total_downtime} minutes", "Impact production:", f"{total_impact:.1f} tonnes"]
        ]
        
        summary_table = Table(summary_data, colWidths=[doc.width/4.0]*4)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(summary_table)
        content.append(Spacer(1, 15))

    def add_resource_consumption(self, content, doc):
        """Ajoute la consommation des ressources avec ratios."""
        content.append(Paragraph("🔋 CONSOMMATION RESSOURCES", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        consumption_data = [
            ["Ressource", "Concassage", "Laverie", "Total", "Ratio (/T produite)", "Évolution J-1"],
            ["Eau (m³)", "245", "1,850", "2,095", "1.7 m³/T", "+5%"],
            ["Énergie (kWh)", "8,450", "15,200", "23,650", "19.2 kWh/T", "-2%"],
            ["Réactifs (kg)", "0", "450", "450", "0.37 kg/T", "+8%"],
            ["Carburant (L)", "125", "75", "200", "0.16 L/T", "="]
        ]
        
        consumption_table = Table(consumption_data, colWidths=[doc.width/6.0]*6)
        consumption_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.0, 0.5, 0.3)),  # Vert foncé
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            # Couleurs pour évolution
            ('TEXTCOLOR', (5, 1), (5, 1), colors.red),    # +5% en rouge
            ('TEXTCOLOR', (5, 2), (5, 2), colors.green),  # -2% en vert
            ('TEXTCOLOR', (5, 3), (5, 3), colors.red),    # +8% en rouge
        ]))
        
        content.append(consumption_table)
        content.append(Spacer(1, 15))

    def add_observations_recommendations(self, content, doc):
        """Ajoute les observations et recommandations."""
        content.append(Paragraph("💡 OBSERVATIONS & RECOMMANDATIONS", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        # Section observations
        observations = [
            "• Production légèrement sous l'objectif de 1,300T (-4.2%)",
            "• Qualité P2O5 stable autour de 30%, conforme aux standards",
            "• Augmentation consommation eau (+5%) à surveiller",
            "• Maintenance préventive concassage réalisée avec succès"
        ]
        
        for obs in observations:
            content.append(Paragraph(obs, self.styles['Normal']))
            content.append(Spacer(1, 4))
        
        content.append(Spacer(1, 8))
        
        # Section recommandations
        content.append(Paragraph("<b>Points d'attention maintenance:</b>", self.styles['Normal']))
        content.append(Spacer(1, 4))
        
        recommendations = [
            "• Vérifier système filtration eau laverie (consommation élevée)",
            "• Programmer inspection trémie concassage (2 bourrages aujourd'hui)",
            "• Contrôler dosage réactifs (+8% vs objectif)",
            "• Prévoir maintenance pompe principale laverie (vibrations)"
        ]
        
        for rec in recommendations:
            content.append(Paragraph(rec, self.styles['Normal']))
            content.append(Spacer(1, 4))
        
        content.append(Spacer(1, 15))

    def generate_daily_production_report(self, data, date=None):
        """
        Génère un rapport journalier de production amélioré.
        
        Args:
            data (dict): Données de production et de stock
            date (datetime, optional): Date du rapport. Par défaut, date actuelle.
        
        Returns:
            str: Chemin du fichier PDF généré
        """
        if date is None:
            date = datetime.now()
        
        # Nom du fichier
        filename = f"rapport_production_{date.strftime('%Y%m%d')}.pdf"
        filepath = os.path.join(self.output_dir, filename)
        
        # Créer le document
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=2*cm
        )
        
        # Contenu du document
        content = []
        
        # En-tête professionnel
        self.add_company_header(content)
        
        # Date du rapport
        content.append(Paragraph(f"<b>Rapport du {date.strftime('%d/%m/%Y')}</b>", self.styles['PDFSubtitle']))
        content.append(Spacer(1, 15))
        
        # KPI Dashboard
        self.add_kpi_dashboard(content, data, doc)
        
        # Résumé de production amélioré
        content.append(Paragraph("🏭 PRODUCTION DÉTAILLÉE", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        # Tableau de résumé de production amélioré
        production_data = [
            ["Étape", "Quantité (T)", "Qualité P2O5", "Heures", "Rendement (T/h)", "Objectif", "Écart"]
        ]
        
        # Objectifs par étape (exemple)
        objectives = {
            'Concassage': 900,
            'Laverie': 700,
            'Réception': 1000
        }
        
        # Ajouter les données de production
        for step, step_data in data.get('production', {}).items():
            quantity = step_data.get('quantity', 0)
            quality = step_data.get('quality', '-')
            hours = step_data.get('hours', 0)
            objective = objectives.get(step, 0)
            
            # Calculer le rendement
            if hours > 0:
                rendement = quantity / hours
            else:
                rendement = 0
            
            # Calculer l'écart
            if objective > 0:
                ecart = ((quantity - objective) / objective) * 100
                ecart_str = f"{ecart:+.1f}%"
            else:
                ecart_str = "-"
                
            production_data.append([
                step,
                f"{quantity:.1f}",
                quality,
                f"{hours:.1f}",
                f"{rendement:.1f}",
                f"{objective}",
                ecart_str
            ])
        
        # Créer le tableau avec style amélioré
        production_table = Table(production_data, colWidths=[doc.width/7.0]*7)
        production_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.2, 0.4, 0.8)),  # Bleu industrie
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        content.append(production_table)
        content.append(Spacer(1, 15))
        
        # Analyse des arrêts
        self.add_downtime_analysis(content, doc)
        
        # Consommation ressources
        self.add_resource_consumption(content, doc)
        
        # État des stocks amélioré
        content.append(Paragraph("📦 ÉTAT DES STOCKS", self.styles['Heading2']))
        content.append(Spacer(1, 8))
        
        # Tableau d'état des stocks amélioré
        stock_data = [
            ["Étape", "Stock Initial (T)", "Entrées (T)", "Sorties (T)", "Stock Final (T)", "Capacité", "Taux (%)]"]
        ]
        
        # Capacités par étape (exemple)
        capacities = {
            'Concassage': 5000,
            'Laverie': 2000,
            'Réception': 3000
        }
        
        # Ajouter les données de stock
        for step, step_data in data.get('stocks', {}).items():
            initial = step_data.get('initial', 0)
            entrees = step_data.get('entrees', 0)
            sorties = step_data.get('sorties', 0)
            final = step_data.get('final', 0)
            capacity = capacities.get(step, 1000)
            
            # Calculer le taux de remplissage
            taux = (final / capacity) * 100 if capacity > 0 else 0
            
            stock_data.append([
                step,
                f"{initial:.1f}",
                f"{entrees:.1f}",
                f"{sorties:.1f}",
                f"{final:.1f}",
                f"{capacity}",
                f"{taux:.1f}%"
            ])
        
        # Créer le tableau avec style amélioré
        stock_table = Table(stock_data, colWidths=[doc.width/7.0]*7)
        stock_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.0, 0.5, 0.7)),  # Bleu cyan
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        content.append(stock_table)
        content.append(Spacer(1, 15))
        
        # Observations et recommandations
        self.add_observations_recommendations(content, doc)
        
        # Graphique de production (si des données sont disponibles)
        if data.get('production'):
            content.append(Paragraph("📈 GRAPHIQUE DE PRODUCTION", self.styles['Heading2']))
            content.append(Spacer(1, 8))
            
            # Créer un graphique à barres amélioré
            drawing = Drawing(doc.width, 200)
            
            # Données pour le graphique
            chart = VerticalBarChart()
            chart.x = 50
            chart.y = 50
            chart.height = 125
            chart.width = doc.width - 100
            
            # Extraire les données
            steps = []
            quantities = []
            
            for step, step_data in data.get('production', {}).items():
                steps.append(step)
                quantities.append(step_data.get('quantity', 0))
            
            chart.data = [quantities]
            chart.categoryAxis.categoryNames = steps
            chart.valueAxis.valueMin = 0
            
            # Style du graphique amélioré
            chart.bars[0].fillColor = colors.Color(0.2, 0.4, 0.8)  # Bleu industrie
            chart.categoryAxis.labels.angle = 0
            chart.categoryAxis.labels.boxAnchor = 'n'
            chart.valueAxis.labels.fontName = 'Helvetica'
            chart.valueAxis.labels.fontSize = 8
            
            drawing.add(chart)
            content.append(drawing)
            content.append(Spacer(1, 15))
        
        # Pied de page professionnel
        footer_data = [
            ["Système de Suivi Production v2.0", f"Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"]
        ]
        
        footer_table = Table(footer_data, colWidths=[doc.width/2.0]*2)
        footer_table.setStyle(TableStyle([
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.grey),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('LINEABOVE', (0, 0), (-1, 0), 1, colors.lightgrey),
        ]))
        
        content.append(footer_table)
        
        # Générer le PDF
        doc.build(content)
        
        return filepath