"""
Widget vide pour remplacer les graphiques dans l'application.
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton, QSizePolicy

class EmptyChartWidget(QWidget):
    """Widget vide qui remplace les graphiques."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget vide."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Aujourd'hui", "<PERSON><PERSON> semaine", "<PERSON> mois", "<PERSON>tte année"])
        header_layout.addWidget(QLabel("Période:"))
        header_layout.addWidget(self.period_combo)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        header_layout.addWidget(self.export_button)
        
        self.layout.addLayout(header_layout)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        self.layout.addWidget(info_label)
        
        # Définir une taille minimale
        self.setMinimumHeight(100)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    
    def update_chart(self, data=None, categories=None):
        """Méthode vide pour compatibilité avec l'interface des graphiques."""
        pass
    
    def export_chart(self):
        """Méthode vide pour compatibilité avec l'interface des graphiques."""
        pass

# Créer des alias pour les différents types de graphiques
MatplotlibBarChart = EmptyChartWidget
MatplotlibLineChart = EmptyChartWidget
BarChartWidget = EmptyChartWidget
LineChartWidget = EmptyChartWidget