import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional
from .constants import DEFAULT_LOG_LEVEL, DEFAULT_LOG_FILE, DEFAULT_LOG_MAX_SIZE, DEFAULT_LOG_BACKUP_COUNT

class Logger:
    """Classe de gestion des logs de l'application."""
    
    def __init__(self,
                 name: str = "app",
                 level: str = DEFAULT_LOG_LEVEL,
                 log_file: str = DEFAULT_LOG_FILE,
                 max_size: int = DEFAULT_LOG_MAX_SIZE,
                 backup_count: int = DEFAULT_LOG_BACKUP_COUNT):
        """Initialise le logger."""
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Créer le répertoire de logs si nécessaire
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # Configurer le handler de fichier
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        
        # Configurer le handler de console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, level.upper()))
        
        # Configurer le format
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Ajouter les handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str) -> None:
        """Log un message de niveau DEBUG."""
        self.logger.debug(message)
    
    def info(self, message: str) -> None:
        """Log un message de niveau INFO."""
        self.logger.info(message)
    
    def warning(self, message: str) -> None:
        """Log un message de niveau WARNING."""
        self.logger.warning(message)
    
    def error(self, message: str) -> None:
        """Log un message de niveau ERROR."""
        self.logger.error(message)
    
    def critical(self, message: str) -> None:
        """Log un message de niveau CRITICAL."""
        self.logger.critical(message)
    
    def exception(self, message: str) -> None:
        """Log un message de niveau ERROR avec la trace d'exception."""
        self.logger.exception(message)

# Créer une instance globale du logger
logger = Logger()

def get_logger(name: Optional[str] = None) -> Logger:
    """Récupère une instance du logger."""
    if name:
        return Logger(name=name)
    return logger 