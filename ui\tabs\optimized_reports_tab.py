"""
Onglet rapport optimisé avec chargement paresseux et interface simplifiée.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QComboBox, QPushButton, QDateEdit, QScrollArea,
                            QTabWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QProgressBar, QFrame,
                            QSplitter, QStackedWidget)
from PyQt5.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging
import os

from ui.widgets.gauge_widget import GaugeWidget
from ui.widgets.professional_widgets import KPICard, StatusIndicator, ProfessionalTable
from models.enums import ProcessStepEnum, ResourceType
from core.services.report_service import ReportService

class DataLoader(QThread):
    """Thread pour charger les données en arrière-plan."""
    
    data_loaded = pyqtSignal(str, object)  # (data_type, data)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, db_manager, data_type, start_date, end_date):
        super().__init__()
        self.db_manager = db_manager
        self.data_type = data_type
        self.start_date = start_date
        self.end_date = end_date
        
    def run(self):
        """Charge les données selon le type."""
        try:
            self.progress_updated.emit(10)
            
            if self.data_type == "dashboard":
                data = self.load_dashboard_data()
            elif self.data_type == "production":
                data = self.load_production_data()
            elif self.data_type == "consumption":
                data = self.load_consumption_data()
            elif self.data_type == "stock":
                data = self.load_stock_data()
            elif self.data_type == "downtime":
                data = self.load_downtime_data()
            else:
                data = None
                
            self.progress_updated.emit(100)
            self.data_loaded.emit(self.data_type, data)
            
        except Exception as e:
            logging.error(f"Erreur lors du chargement des données {self.data_type}: {str(e)}")
            self.data_loaded.emit(self.data_type, None)
    
    def load_dashboard_data(self):
        """Charge les données du tableau de bord."""
        data = {}
        
        # Stocks actuels
        data['stocks'] = {
            'reception': self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION),
            'crushing': self.db_manager.get_available_crushing_stock(),
            'laverie': self.db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        }
        
        # KPI production du jour
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        productions = self.db_manager.get_production_summary(start_date=today)
        
        total_production = sum(p['quantity'] for p in productions)
        total_hours = sum(p['production_hours'] for p in productions if p['production_hours'])
        
        data['kpi'] = {
            'production_today': total_production,
            'hours_today': total_hours,
            'efficiency': (total_production / total_hours * 100) if total_hours > 0 else 0
        }
        
        return data
    
    def load_production_data(self):
        """Charge les données de production."""
        return self.db_manager.get_production_summary(
            start_date=self.start_date,
            end_date=self.end_date
        )
    
    def load_consumption_data(self):
        """Charge les données de consommation."""
        return self.db_manager.get_resource_consumptions(
            start_date=self.start_date,
            end_date=self.end_date
        )
    
    def load_stock_data(self):
        """Charge les données de stock."""
        return self.db_manager.get_stock_movements(
            start_date=self.start_date,
            end_date=self.end_date
        )
    
    def load_downtime_data(self):
        """Charge les données d'arrêt."""
        return self.db_manager.get_downtime_summary(
            start_date=self.start_date,
            end_date=self.end_date
        )

class DashboardWidget(QWidget):
    """Widget tableau de bord optimisé."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # En-tête
        header_layout = QHBoxLayout()
        title = QLabel("📊 Tableau de Bord - Temps Réel")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #1E3A8A;")
        header_layout.addWidget(title)
        
        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_btn)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # KPI Cards en grille
        kpi_layout = QHBoxLayout()
        kpi_layout.setSpacing(15)
        
        self.production_card = KPICard("Production Totale", "0", "T")
        self.efficiency_card = KPICard("Efficacité", "0", "%")
        self.hours_card = KPICard("Heures Travaillées", "0", "h")
        
        kpi_layout.addWidget(self.production_card)
        kpi_layout.addWidget(self.efficiency_card)
        kpi_layout.addWidget(self.hours_card)
        
        layout.addLayout(kpi_layout)
        
        # Jauges de stock
        gauge_frame = QFrame()
        gauge_frame.setStyleSheet("QFrame { border: 1px solid #E5E7EB; border-radius: 8px; padding: 10px; }")
        gauge_layout = QHBoxLayout(gauge_frame)
        
        self.reception_gauge = GaugeWidget(title="Stock Réception", unit="t")
        self.reception_gauge.set_range(0, 1000)
        
        self.crushing_gauge = GaugeWidget(title="Disponible Laverie", unit="t") 
        self.crushing_gauge.set_range(0, 500)
        
        self.laverie_gauge = GaugeWidget(title="Stock Laverie", unit="t")
        self.laverie_gauge.set_range(0, 300)
        
        gauge_layout.addWidget(self.reception_gauge)
        gauge_layout.addWidget(self.crushing_gauge)
        gauge_layout.addWidget(self.laverie_gauge)
        
        layout.addWidget(gauge_frame)
        
        # Auto-refresh timer - DÉSACTIVÉ
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        # self.refresh_timer.start(30000)  # DÉSACTIVÉ
        
        # Charger les données initiales
        self.refresh_data()
        
    def refresh_data(self):
        """Actualise les données du tableau de bord."""
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("⏳ Chargement...")
        
        # Charger en arrière-plan
        self.data_loader = DataLoader(self.db_manager, "dashboard", None, None)
        self.data_loader.data_loaded.connect(self.on_data_loaded)
        self.data_loader.start()
        
    def on_data_loaded(self, data_type, data):
        """Callback quand les données sont chargées."""
        if data and data_type == "dashboard":
            # Mettre à jour les KPI
            self.production_card.update_value(f"{data['kpi']['production_today']:.1f}")
            self.efficiency_card.update_value(f"{data['kpi']['efficiency']:.1f}")
            self.hours_card.update_value(f"{data['kpi']['hours_today']:.1f}")
            
            # Mettre à jour les jauges
            self.reception_gauge.set_value(data['stocks']['reception'])
            self.crushing_gauge.set_value(data['stocks']['crushing'])
            self.laverie_gauge.set_value(data['stocks']['laverie'])
        
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("🔄 Actualiser")

class LazyLoadTab(QWidget):
    """Onglet avec chargement paresseux."""
    
    def __init__(self, db_manager, data_type, table_headers, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.data_type = data_type
        self.table_headers = table_headers
        self.data_loaded = False
        self.start_date = None
        self.end_date = None
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface."""
        layout = QVBoxLayout(self)
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Message de statut
        self.status_label = QLabel("Cliquez sur 'Charger les données' pour afficher les informations")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #6B7280; font-style: italic; padding: 20px;")
        layout.addWidget(self.status_label)
        
        # Bouton de chargement
        self.load_btn = QPushButton(f"📥 Charger les données - {self.data_type.title()}")
        self.load_btn.clicked.connect(self.load_data)
        layout.addWidget(self.load_btn)
        
        # Tableau (caché initialement)
        self.table = ProfessionalTable()
        self.table.setColumnCount(len(self.table_headers))
        self.table.setHorizontalHeaderLabels(self.table_headers)
        self.table.setVisible(False)
        layout.addWidget(self.table)
        
    def set_date_range(self, start_date, end_date):
        """Définit la plage de dates."""
        self.start_date = start_date
        self.end_date = end_date
        self.data_loaded = False
        
        # Masquer le tableau et afficher le bouton de chargement
        self.table.setVisible(False)
        self.load_btn.setVisible(True)
        self.status_label.setText("Nouvelles dates sélectionnées. Cliquez sur 'Charger les données'")
        
    def load_data(self):
        """Charge les données de manière asynchrone."""
        if not self.start_date or not self.end_date:
            return
            
        self.load_btn.setEnabled(False)
        self.load_btn.setText("⏳ Chargement en cours...")
        self.progress_bar.setVisible(True)
        self.status_label.setText("Chargement des données...")
        
        # Charger en arrière-plan
        self.data_loader = DataLoader(self.db_manager, self.data_type, self.start_date, self.end_date)
        self.data_loader.data_loaded.connect(self.on_data_loaded)
        self.data_loader.progress_updated.connect(self.progress_bar.setValue)
        self.data_loader.start()
        
    def on_data_loaded(self, data_type, data):
        """Callback quand les données sont chargées."""
        self.progress_bar.setVisible(False)
        self.load_btn.setEnabled(True)
        self.load_btn.setText(f"🔄 Recharger - {self.data_type.title()}")
        
        if data:
            self.populate_table(data)
            self.table.setVisible(True)
            self.status_label.setText(f"✅ {len(data)} enregistrements chargés")
            self.data_loaded = True
        else:
            self.status_label.setText("❌ Erreur lors du chargement des données")
            
    def populate_table(self, data):
        """Remplit le tableau avec les données."""
        # À implémenter dans les classes dérivées
        pass

class ProductionTab(LazyLoadTab):
    """Onglet production avec chargement paresseux."""
    
    def __init__(self, db_manager, parent=None):
        headers = ["Date", "Étape", "Quantité (t)", "Qualité", "Heures", "Rendement (t/h)"]
        super().__init__(db_manager, "production", headers, parent)
        
    def populate_table(self, data):
        """Remplit le tableau de production."""
        self.table.setRowCount(len(data))
        
        for row, production in enumerate(data):
            # Date
            self.table.setItem(row, 0, QTableWidgetItem(
                production['production_date'].strftime("%d/%m/%Y %H:%M")
            ))
            
            # Étape
            self.table.setItem(row, 1, QTableWidgetItem(production['step']))
            
            # Quantité
            self.table.setItem(row, 2, QTableWidgetItem(f"{production['quantity']:.2f}"))
            
            # Qualité
            quality = production['quality'] if production['quality'] else "-"
            self.table.setItem(row, 3, QTableWidgetItem(quality))
            
            # Heures
            hours = f"{production['production_hours']:.2f}" if production['production_hours'] else "-"
            self.table.setItem(row, 4, QTableWidgetItem(hours))
            
            # Rendement
            if production['production_hours'] and production['production_hours'] > 0:
                rendement = production['quantity'] / production['production_hours']
                self.table.setItem(row, 5, QTableWidgetItem(f"{rendement:.2f}"))
            else:
                self.table.setItem(row, 5, QTableWidgetItem("-"))

class ConsumptionTab(LazyLoadTab):
    """Onglet consommation avec chargement paresseux."""
    
    def __init__(self, db_manager, parent=None):
        headers = ["Date", "Ressource", "Quantité", "Unité"]
        super().__init__(db_manager, "consumption", headers, parent)
        
    def populate_table(self, data):
        """Remplit le tableau des consommations."""
        self.table.setRowCount(len(data))
        
        unit_map = {
            ResourceType.WATER.value: "m³",
            ResourceType.ENERGY.value: "kWh", 
            ResourceType.REACTIVE.value: "kg"
        }
        
        for row, consumption in enumerate(data):
            # Date
            self.table.setItem(row, 0, QTableWidgetItem(
                consumption['consumption_date'].strftime("%d/%m/%Y")
            ))
            
            # Ressource
            self.table.setItem(row, 1, QTableWidgetItem(consumption['resource_type']))
            
            # Quantité
            self.table.setItem(row, 2, QTableWidgetItem(f"{consumption['quantity']:.2f}"))
            
            # Unité
            unit = unit_map.get(consumption['resource_type'], "")
            self.table.setItem(row, 3, QTableWidgetItem(unit))

class StockTab(LazyLoadTab):
    """Onglet mouvements de stock avec chargement paresseux."""
    
    def __init__(self, db_manager, parent=None):
        headers = ["Date", "Étape", "Type", "Quantité (t)", "Qualité"]
        super().__init__(db_manager, "stock", headers, parent)
        
    def populate_table(self, data):
        """Remplit le tableau des mouvements de stock."""
        self.table.setRowCount(len(data))
        
        for row, movement in enumerate(data):
            # Date
            self.table.setItem(row, 0, QTableWidgetItem(
                movement['date'].strftime("%d/%m/%Y %H:%M")
            ))
            
            # Étape
            self.table.setItem(row, 1, QTableWidgetItem(movement['step']))
            
            # Type
            self.table.setItem(row, 2, QTableWidgetItem(movement['movement_type']))
            
            # Quantité
            self.table.setItem(row, 3, QTableWidgetItem(f"{movement['quantity']:.2f}"))
            
            # Qualité
            quality = f"{movement['quality']:.2f}" if movement['quality'] else "-"
            self.table.setItem(row, 4, QTableWidgetItem(quality))

class DowntimeTab(LazyLoadTab):
    """Onglet arrêts avec chargement paresseux."""
    
    def __init__(self, db_manager, parent=None):
        headers = ["Date", "Étape", "Raison", "Durée (h)", "Description"]
        super().__init__(db_manager, "downtime", headers, parent)
        
    def populate_table(self, data):
        """Remplit le tableau des arrêts."""
        self.table.setRowCount(len(data))
        
        for row, downtime in enumerate(data):
            # Date
            self.table.setItem(row, 0, QTableWidgetItem(
                downtime['downtime_date'].strftime("%d/%m/%Y %H:%M")
            ))
            
            # Étape
            self.table.setItem(row, 1, QTableWidgetItem(downtime['step']))
            
            # Raison
            self.table.setItem(row, 2, QTableWidgetItem(downtime['reason']))
            
            # Durée (convertir minutes en heures)
            duration_hours = downtime['duration'] / 60.0
            self.table.setItem(row, 3, QTableWidgetItem(f"{duration_hours:.2f}"))
            
            # Description
            description = downtime['description'] if downtime['description'] else "-"
            self.table.setItem(row, 4, QTableWidgetItem(description))

class OptimizedReportsTab(QWidget):
    """Onglet rapport optimisé avec chargement paresseux."""
    
    def __init__(self, db_manager, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.db_manager = db_manager
        self.report_service = ReportService(db_manager)
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur optimisée."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # En-tête compact
        header_layout = QHBoxLayout()
        
        # Contrôles de période
        header_layout.addWidget(QLabel("📅 Période:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Personnalisée"])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        header_layout.addWidget(self.period_combo)
        
        # Dates personnalisées (masquées par défaut)
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setVisible(False)
        
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setVisible(False)
        
        header_layout.addWidget(self.start_date)
        header_layout.addWidget(self.end_date)
        
        # Boutons d'action
        header_layout.addStretch()
        
        self.pdf_button = QPushButton("📄 Générer PDF")
        self.pdf_button.clicked.connect(self.generate_pdf_report)
        header_layout.addWidget(self.pdf_button)
        
        main_layout.addLayout(header_layout)
        
        # Onglets principaux optimisés
        self.main_tabs = QTabWidget()
        
        # 1. Tableau de bord (toujours visible)
        self.dashboard = DashboardWidget(self.db_manager)
        self.main_tabs.addTab(self.dashboard, "📊 Tableau de Bord")
        
        # 2. Onglets avec chargement paresseux
        self.production_tab = ProductionTab(self.db_manager)
        self.main_tabs.addTab(self.production_tab, "🏭 Production")
        
        self.consumption_tab = ConsumptionTab(self.db_manager)
        self.main_tabs.addTab(self.consumption_tab, "⚡ Consommations")
        
        self.stock_tab = StockTab(self.db_manager)
        self.main_tabs.addTab(self.stock_tab, "📦 Mouvements Stock")
        
        self.downtime_tab = DowntimeTab(self.db_manager)
        self.main_tabs.addTab(self.downtime_tab, "⏹️ Arrêts")
        
        # Connecter le changement d'onglet
        self.main_tabs.currentChanged.connect(self.on_tab_changed)
        
        main_layout.addWidget(self.main_tabs)
        
    def on_period_changed(self, period):
        """Gère le changement de période."""
        custom_period = period == "Personnalisée"
        self.start_date.setVisible(custom_period)
        self.end_date.setVisible(custom_period)
        
        # Mettre à jour les plages de dates pour tous les onglets
        start_date, end_date = self.get_date_range()
        self.update_date_ranges(start_date, end_date)
        
    def update_date_ranges(self, start_date, end_date):
        """Met à jour les plages de dates pour tous les onglets."""
        tabs = [self.production_tab, self.consumption_tab, self.stock_tab, self.downtime_tab]
        for tab in tabs:
            tab.set_date_range(start_date, end_date)
            
    def on_tab_changed(self, index):
        """Gère le changement d'onglet."""
        # Le tableau de bord se met à jour automatiquement
        # Les autres onglets se chargent à la demande
        pass
        
    def get_date_range(self):
        """Récupère la plage de dates sélectionnée."""
        period = self.period_combo.currentText()
        end_date = datetime.now()
        
        if period == "Aujourd'hui":
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "Cette semaine":
            start_date = end_date - timedelta(days=end_date.weekday())
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "Ce mois":
            start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == "Personnalisée":
            start_date = datetime.combine(self.start_date.date().toPyDate(), datetime.min.time())
            end_date = datetime.combine(self.end_date.date().toPyDate(), datetime.max.time())
        else:
            start_date = end_date - timedelta(days=7)
            
        return start_date, end_date
        
    def generate_pdf_report(self):
        """Génère un rapport PDF."""
        try:
            if self.period_combo.currentText() == "Personnalisée":
                selected_date = self.start_date.date().toPyDate()
            else:
                selected_date = datetime.now()
                
            pdf_path = self.report_service.generate_daily_production_pdf(date=selected_date)
            
            QMessageBox.information(
                self, 
                "✅ PDF Généré", 
                f"Rapport généré avec succès :\n{pdf_path}"
            )
            
        except Exception as e:
            logging.error(f"Erreur PDF: {str(e)}")
            QMessageBox.critical(self, "❌ Erreur", f"Erreur lors de la génération : {str(e)}")
