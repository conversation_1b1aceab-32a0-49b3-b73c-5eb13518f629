"""change_quality_column_to_enum

Revision ID: d9780a85eb0c
Revises: cf8d9575f00d
Create Date: 2025-06-13 05:37:26.239369

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from models.enums import QualityGrade


# revision identifiers, used by Alembic.
revision: str = 'd9780a85eb0c'
down_revision: Union[str, None] = 'cf8d9575f00d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

quality_enum = sa.Enum(QualityGrade, name='quality_grade')


def upgrade() -> None:
    """Upgrade schema."""
    # Create the enum type
    quality_enum.create(op.get_bind(), checkfirst=True)

    # Backup existing data
    op.execute('CREATE TABLE productions_backup AS SELECT * FROM productions')

    # Drop old column
    with op.batch_alter_table('productions') as batch_op:
        batch_op.drop_column('quality')

    # Add new column
    with op.batch_alter_table('productions') as batch_op:
        batch_op.add_column(sa.Column('quality', quality_enum, nullable=True))

    # Drop backup table
    op.execute('DROP TABLE productions_backup')


def downgrade() -> None:
    """Downgrade schema."""
    # Create float column
    with op.batch_alter_table('productions') as batch_op:
        batch_op.alter_column('quality',
                            type_=sa.Float(),
                            nullable=True)

    # Drop the enum type
    quality_enum.drop(op.get_bind(), checkfirst=True)
