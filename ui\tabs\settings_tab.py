"""
Onglet des paramètres de l'application.
"""
# Standard library imports
import logging
import os
import shutil
from typing import Dict, Any, List, Optional

# Third-party imports
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QColor, QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QLabel, QComboBox, QPushButton, QTabWidget,
                             QScrollArea, QCheckBox, QListWidget, QListWidgetItem,
                             QDialog, QLineEdit, QFormLayout, QSpinBox, QMessageBox,
                             QColorDialog, QGridLayout, QFrame, QApplication,
                             QInputDialog)

# Local imports
from config.dashboard_config import DashboardConfig
from ui.theme_manager import theme_manager, apply_theme
from utils.cache_manager import get_cache_stats, clear_cache, clean_expired_cache

class ColorPreview(QFrame):
    """Widget pour prévisualiser une couleur."""
    
    def __init__(self, color: str, parent: Optional[QWidget] = None):
        """
        Initialise le widget de prévisualisation de couleur.
        
        Args:
            color: Couleur à prévisualiser (format hexadécimal)
            parent: Widget parent
        """
        super().__init__(parent)
        self.setFixedSize(24, 24)
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        self.set_color(color)
    
    def set_color(self, color: str) -> None:
        """
        Définit la couleur à prévisualiser.
        
        Args:
            color: Couleur à prévisualiser (format hexadécimal)
        """
        self.setStyleSheet(f"background-color: {color}; border: 1px solid #cccccc;")

class ThemeEditorDialog(QDialog):
    """Boîte de dialogue pour éditer un thème."""
    
    def __init__(self, theme_id: str, theme_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        Initialise la boîte de dialogue d'édition de thème.
        
        Args:
            theme_id: ID du thème à éditer
            theme_data: Données du thème
            parent: Widget parent
        """
        super().__init__(parent)
        self.theme_id = theme_id
        self.theme_data = theme_data.copy()
        self.is_new_theme = theme_id not in theme_manager.get_all_themes()
        
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("Éditeur de thème")
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Informations générales
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit(self.theme_data.get("name", ""))
        form_layout.addRow("Nom:", self.name_edit)
        
        self.description_edit = QLineEdit(self.theme_data.get("description", ""))
        form_layout.addRow("Description:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # Couleurs du thème
        colors_group = QGroupBox("Couleurs du thème")
        colors_layout = QGridLayout()
        
        self.color_widgets = {}
        styles = self.theme_data.get("styles", {})
        
        row = 0
        col = 0
        for color_key, color_value in styles.items():
            # Créer un label pour le nom de la couleur
            label = QLabel(color_key.replace("_", " ").title())
            
            # Créer un widget de prévisualisation de la couleur
            color_preview = ColorPreview(color_value)
            
            # Créer un bouton pour changer la couleur
            color_button = QPushButton("Changer")
            color_button.setProperty("color_key", color_key)
            color_button.clicked.connect(self.on_color_button_clicked)
            
            # Ajouter les widgets à la grille
            colors_layout.addWidget(label, row, col)
            colors_layout.addWidget(color_preview, row, col + 1)
            colors_layout.addWidget(color_button, row, col + 2)
            
            # Stocker les widgets pour pouvoir les mettre à jour
            self.color_widgets[color_key] = {
                "label": label,
                "preview": color_preview,
                "button": color_button
            }
            
            # Passer à la ligne suivante
            row += 1
            if row >= 10:  # 10 couleurs par colonne
                row = 0
                col += 3
        
        colors_group.setLayout(colors_layout)
        
        # Ajouter un scroll area pour les couleurs
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(colors_group)
        layout.addWidget(scroll_area)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def on_color_button_clicked(self) -> None:
        """Gère le clic sur un bouton de couleur."""
        button = self.sender()
        if not button:
            return
        
        color_key = button.property("color_key")
        current_color = self.theme_data["styles"][color_key]
        
        color_dialog = QColorDialog(QColor(current_color), self)
        if color_dialog.exec_():
            new_color = color_dialog.selectedColor().name()
            self.theme_data["styles"][color_key] = new_color
            self.color_widgets[color_key]["preview"].set_color(new_color)
    
    def get_theme_data(self) -> Dict[str, Any]:
        """
        Récupère les données du thème modifié.
        
        Returns:
            Dictionnaire contenant les données du thème
        """
        self.theme_data["name"] = self.name_edit.text()
        self.theme_data["description"] = self.description_edit.text()
        return self.theme_data

class DashboardEditorDialog(QDialog):
    """Boîte de dialogue pour éditer un tableau de bord."""
    
    def __init__(self, dashboard_id: str, dashboard_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        Initialise la boîte de dialogue d'édition de tableau de bord.
        
        Args:
            dashboard_id: ID du tableau de bord à éditer
            dashboard_data: Données du tableau de bord
            parent: Widget parent
        """
        super().__init__(parent)
        self.dashboard_id = dashboard_id
        self.dashboard_data = dashboard_data.copy()
        self.dashboard_config = DashboardConfig()
        
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("Éditeur de tableau de bord")
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Informations générales
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit(self.dashboard_data.get("name", ""))
        form_layout.addRow("Nom:", self.name_edit)
        
        layout.addLayout(form_layout)
        
        # Liste des widgets
        widgets_group = QGroupBox("Widgets")
        widgets_layout = QVBoxLayout()
        
        self.widgets_list = QListWidget()
        self.update_widgets_list()
        widgets_layout.addWidget(self.widgets_list)
        
        # Boutons pour gérer les widgets
        widgets_buttons_layout = QHBoxLayout()
        
        self.add_widget_button = QPushButton("Ajouter")
        self.add_widget_button.clicked.connect(self.on_add_widget)
        widgets_buttons_layout.addWidget(self.add_widget_button)
        
        self.edit_widget_button = QPushButton("Modifier")
        self.edit_widget_button.clicked.connect(self.on_edit_widget)
        widgets_buttons_layout.addWidget(self.edit_widget_button)
        
        self.delete_widget_button = QPushButton("Supprimer")
        self.delete_widget_button.clicked.connect(self.on_delete_widget)
        widgets_buttons_layout.addWidget(self.delete_widget_button)
        
        widgets_layout.addLayout(widgets_buttons_layout)
        widgets_group.setLayout(widgets_layout)
        layout.addWidget(widgets_group)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def update_widgets_list(self) -> None:
        """Met à jour la liste des widgets."""
        self.widgets_list.clear()
        
        for widget in self.dashboard_data.get("widgets", []):
            item = QListWidgetItem(f"{widget.get('title', 'Sans titre')} ({widget.get('type', 'inconnu')})")
            item.setData(Qt.UserRole, widget)
            self.widgets_list.addItem(item)
    
    def on_add_widget(self) -> None:
        """Gère l'ajout d'un widget."""
        # TODO: Implémenter l'ajout de widget
        QMessageBox.information(self, "Information", "Fonctionnalité en cours de développement.")
    
    def on_edit_widget(self) -> None:
        """Gère la modification d'un widget."""
        # TODO: Implémenter la modification de widget
        QMessageBox.information(self, "Information", "Fonctionnalité en cours de développement.")
    
    def on_delete_widget(self) -> None:
        """Gère la suppression d'un widget."""
        current_item = self.widgets_list.currentItem()
        if not current_item:
            return
        
        widget_index = self.widgets_list.row(current_item)
        
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment supprimer ce widget ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            del self.dashboard_data["widgets"][widget_index]
            self.update_widgets_list()
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """
        Récupère les données du tableau de bord modifié.
        
        Returns:
            Dictionnaire contenant les données du tableau de bord
        """
        self.dashboard_data["name"] = self.name_edit.text()
        return self.dashboard_data

class SettingsTab(QWidget):
    """Onglet des paramètres de l'application."""
    
    def __init__(self, parent: Optional[QWidget] = None, db_manager=None):
        """
        Initialise l'onglet des paramètres.
        
        Args:
            parent: Widget parent
            db_manager: Gestionnaire de base de données
        """
        super().__init__(parent)
        self.theme_manager = theme_manager
        self.dashboard_config = DashboardConfig()
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        
        # Onglets de paramètres
        self.settings_tabs = QTabWidget()
        
        # Onglet d'apparence
        appearance_tab = QWidget()
        appearance_layout = QVBoxLayout(appearance_tab)
        
        # Groupe pour les thèmes
        themes_group = QGroupBox("Thèmes")
        themes_layout = QVBoxLayout()
        
        # Sélection du thème
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("Thème:"))
        
        self.theme_combo = QComboBox()
        self.update_theme_combo()
        self.theme_combo.currentIndexChanged.connect(self.on_theme_changed)
        theme_layout.addWidget(self.theme_combo)
        
        # Boutons pour gérer les thèmes
        self.new_theme_button = QPushButton("Nouveau")
        self.new_theme_button.clicked.connect(self.on_new_theme)
        theme_layout.addWidget(self.new_theme_button)
        
        self.edit_theme_button = QPushButton("Modifier")
        self.edit_theme_button.clicked.connect(self.on_edit_theme)
        theme_layout.addWidget(self.edit_theme_button)
        
        self.delete_theme_button = QPushButton("Supprimer")
        self.delete_theme_button.clicked.connect(self.on_delete_theme)
        theme_layout.addWidget(self.delete_theme_button)
        
        themes_layout.addLayout(theme_layout)
        
        # Aperçu du thème
        preview_label = QLabel("Aperçu:")
        themes_layout.addWidget(preview_label)
        
        # TODO: Ajouter un aperçu du thème
        
        themes_group.setLayout(themes_layout)
        appearance_layout.addWidget(themes_group)
        
        # Groupe pour les options d'interface
        ui_options_group = QGroupBox("Options d'interface")
        ui_options_layout = QVBoxLayout()
        
        # Option pour activer/désactiver l'interface responsive
        self.responsive_checkbox = QCheckBox("Interface responsive (s'adapte à la taille de l'écran)")
        self.responsive_checkbox.setChecked(True)
        ui_options_layout.addWidget(self.responsive_checkbox)
        
        # Option pour activer/désactiver les animations
        self.animations_checkbox = QCheckBox("Activer les animations")
        self.animations_checkbox.setChecked(True)
        ui_options_layout.addWidget(self.animations_checkbox)
        
        # Option pour activer/désactiver les tooltips
        self.tooltips_checkbox = QCheckBox("Afficher les infobulles")
        self.tooltips_checkbox.setChecked(True)
        ui_options_layout.addWidget(self.tooltips_checkbox)
        
        ui_options_group.setLayout(ui_options_layout)
        appearance_layout.addWidget(ui_options_group)
        
        appearance_layout.addStretch()
        
        self.settings_tabs.addTab(appearance_tab, "Apparence")
        
        # Onglet des tableaux de bord
        dashboards_tab = QWidget()
        dashboards_layout = QVBoxLayout(dashboards_tab)
        
        # Groupe pour les tableaux de bord
        dashboards_group = QGroupBox("Tableaux de bord personnalisables")
        dashboards_inner_layout = QVBoxLayout()
        
        # Liste des tableaux de bord
        self.dashboards_list = QListWidget()
        self.update_dashboards_list()
        dashboards_inner_layout.addWidget(self.dashboards_list)
        
        # Boutons pour gérer les tableaux de bord
        dashboards_buttons_layout = QHBoxLayout()
        
        self.new_dashboard_button = QPushButton("Nouveau")
        self.new_dashboard_button.clicked.connect(self.on_new_dashboard)
        dashboards_buttons_layout.addWidget(self.new_dashboard_button)
        
        self.edit_dashboard_button = QPushButton("Modifier")
        self.edit_dashboard_button.clicked.connect(self.on_edit_dashboard)
        dashboards_buttons_layout.addWidget(self.edit_dashboard_button)
        
        self.delete_dashboard_button = QPushButton("Supprimer")
        self.delete_dashboard_button.clicked.connect(self.on_delete_dashboard)
        dashboards_buttons_layout.addWidget(self.delete_dashboard_button)
        
        self.activate_dashboard_button = QPushButton("Activer")
        self.activate_dashboard_button.clicked.connect(self.on_activate_dashboard)
        dashboards_buttons_layout.addWidget(self.activate_dashboard_button)
        
        dashboards_inner_layout.addLayout(dashboards_buttons_layout)
        
        dashboards_group.setLayout(dashboards_inner_layout)
        dashboards_layout.addWidget(dashboards_group)
        
        dashboards_layout.addStretch()
        
        self.settings_tabs.addTab(dashboards_tab, "Tableaux de bord")
        
        # Onglet de performance
        performance_tab = QWidget()
        performance_layout = QVBoxLayout(performance_tab)
        
        # Groupe pour le cache
        cache_group = QGroupBox("Cache de données")
        cache_layout = QVBoxLayout()
        
        # Statistiques du cache
        cache_stats_layout = QFormLayout()
        
        self.cache_entries_label = QLabel("0")
        cache_stats_layout.addRow("Entrées en cache:", self.cache_entries_label)
        
        self.cache_hits_label = QLabel("0")
        cache_stats_layout.addRow("Hits:", self.cache_hits_label)
        
        self.cache_misses_label = QLabel("0")
        cache_stats_layout.addRow("Misses:", self.cache_misses_label)
        
        self.cache_hit_ratio_label = QLabel("0%")
        cache_stats_layout.addRow("Ratio de hits:", self.cache_hit_ratio_label)
        
        cache_layout.addLayout(cache_stats_layout)
        
        # Boutons pour gérer le cache
        cache_buttons_layout = QHBoxLayout()
        
        self.clear_cache_button = QPushButton("Vider le cache")
        self.clear_cache_button.clicked.connect(self.on_clear_cache)
        cache_buttons_layout.addWidget(self.clear_cache_button)
        
        self.clean_cache_button = QPushButton("Nettoyer le cache expiré")
        self.clean_cache_button.clicked.connect(self.on_clean_cache)
        cache_buttons_layout.addWidget(self.clean_cache_button)
        
        self.refresh_cache_stats_button = QPushButton("Rafraîchir les statistiques")
        self.refresh_cache_stats_button.clicked.connect(self.update_cache_stats)
        cache_buttons_layout.addWidget(self.refresh_cache_stats_button)
        
        cache_layout.addLayout(cache_buttons_layout)
        
        cache_group.setLayout(cache_layout)
        performance_layout.addWidget(cache_group)
        
        # Groupe pour les options de performance
        performance_options_group = QGroupBox("Options de performance")
        performance_options_layout = QVBoxLayout()
        
        # Option pour activer/désactiver le cache
        self.cache_enabled_checkbox = QCheckBox("Activer le cache de données")
        self.cache_enabled_checkbox.setChecked(True)
        performance_options_layout.addWidget(self.cache_enabled_checkbox)
        
        # Durée de vie du cache
        cache_ttl_layout = QHBoxLayout()
        cache_ttl_layout.addWidget(QLabel("Durée de vie du cache:"))
        
        self.cache_ttl_spinbox = QSpinBox()
        self.cache_ttl_spinbox.setMinimum(1)
        self.cache_ttl_spinbox.setMaximum(3600)
        self.cache_ttl_spinbox.setValue(300)
        self.cache_ttl_spinbox.setSuffix(" secondes")
        cache_ttl_layout.addWidget(self.cache_ttl_spinbox)
        
        performance_options_layout.addLayout(cache_ttl_layout)
        
        performance_options_group.setLayout(performance_options_layout)
        performance_layout.addWidget(performance_options_group)
        
        performance_layout.addStretch()
        
        self.settings_tabs.addTab(performance_tab, "Performance")
        
        # Onglet de maintenance
        maintenance_tab = QWidget()
        maintenance_layout = QVBoxLayout(maintenance_tab)
        
        # Groupe pour la base de données
        database_group = QGroupBox("Base de données")
        database_layout = QVBoxLayout()
        
        # Bouton pour réinitialiser la base de données
        reset_db_button = QPushButton("Réinitialiser la base de données")
        reset_db_button.setStyleSheet("QPushButton { background-color: #d9534f; color: white; }")
        reset_db_button.clicked.connect(self.on_reset_database)
        database_layout.addWidget(reset_db_button)
        
        # Avertissement
        warning_label = QLabel("Attention: La réinitialisation de la base de données supprimera toutes les données existantes.")
        warning_label.setStyleSheet("QLabel { color: #d9534f; font-weight: bold; }")
        database_layout.addWidget(warning_label)
        
        database_group.setLayout(database_layout)
        maintenance_layout.addWidget(database_group)
        maintenance_layout.addStretch()
        
        self.settings_tabs.addTab(maintenance_tab, "Maintenance")
        
        main_layout.addWidget(self.settings_tabs)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("Appliquer")
        self.apply_button.clicked.connect(self.on_apply)
        buttons_layout.addWidget(self.apply_button)
        
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.clicked.connect(self.on_reset)
        buttons_layout.addWidget(self.reset_button)
        
        main_layout.addLayout(buttons_layout)
        
        # Initialiser les statistiques du cache
        self.update_cache_stats()
    
    def update_theme_combo(self) -> None:
        """Met à jour la liste déroulante des thèmes."""
        self.theme_combo.clear()
        
        themes = self.theme_manager.get_all_themes()
        current_theme = self.theme_manager.current_theme
        
        for theme_id, theme_data in themes.items():
            self.theme_combo.addItem(theme_data["name"], theme_id)
            
            # Sélectionner le thème actuel
            if theme_id == current_theme:
                self.theme_combo.setCurrentIndex(self.theme_combo.count() - 1)
    
    def update_dashboards_list(self) -> None:
        """Met à jour la liste des tableaux de bord."""
        self.dashboards_list.clear()
        
        dashboards = self.dashboard_config.get_all_dashboards()
        active_dashboard = self.dashboard_config.config.get("active_dashboard")
        
        for dashboard in dashboards:
            item = QListWidgetItem(dashboard["name"])
            item.setData(Qt.UserRole, dashboard["id"])
            
            # Marquer le tableau de bord actif
            if dashboard["id"] == active_dashboard:
                font = item.font()
                font.setBold(True)
                item.setFont(font)
            
            self.dashboards_list.addItem(item)
    
    def update_cache_stats(self) -> None:
        """Met à jour les statistiques du cache."""
        stats = get_cache_stats()
        
        self.cache_entries_label.setText(str(stats["entries"]))
        self.cache_hits_label.setText(str(stats["hits"]))
        self.cache_misses_label.setText(str(stats["misses"]))
        
        hit_ratio = stats["hit_ratio"] * 100
        self.cache_hit_ratio_label.setText(f"{hit_ratio:.2f}%")
    
    def on_theme_changed(self, index: int) -> None:
        """
        Gère le changement de thème.
        
        Args:
            index: Index du thème sélectionné
        """
        theme_id = self.theme_combo.itemData(index)
        if theme_id:
            self.theme_manager.set_theme(theme_id)
            
            # Appliquer le thème à l'application
            app = QApplication.instance()
            if app:
                apply_theme(app)
    
    def on_new_theme(self) -> None:
        """Gère la création d'un nouveau thème."""
        # Créer un nouveau thème basé sur le thème actuel
        current_theme = self.theme_manager.get_theme()
        new_theme_data = {
            "name": "Nouveau thème",
            "description": "Thème personnalisé",
            "styles": current_theme["styles"].copy()
        }
        
        # Ouvrir la boîte de dialogue d'édition de thème
        dialog = ThemeEditorDialog("new_theme", new_theme_data, self)
        if dialog.exec_():
            theme_data = dialog.get_theme_data()
            
            # Générer un ID unique pour le thème
            import uuid
            theme_id = str(uuid.uuid4())
            
            # Ajouter le thème
            self.theme_manager.add_custom_theme(theme_id, theme_data)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
    
    def on_edit_theme(self) -> None:
        """Gère la modification d'un thème."""
        theme_id = self.theme_combo.currentData()
        if not theme_id:
            return
        
        # Vérifier si c'est un thème prédéfini
        if theme_id in self.theme_manager.THEMES:
            QMessageBox.warning(
                self, "Avertissement",
                "Les thèmes prédéfinis ne peuvent pas être modifiés. Créez un nouveau thème basé sur celui-ci."
            )
            return
        
        # Récupérer les données du thème
        theme_data = self.theme_manager.get_theme(theme_id)
        
        # Ouvrir la boîte de dialogue d'édition de thème
        dialog = ThemeEditorDialog(theme_id, theme_data, self)
        if dialog.exec_():
            theme_data = dialog.get_theme_data()
            
            # Mettre à jour le thème
            self.theme_manager.update_custom_theme(theme_id, theme_data)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
            
            # Appliquer le thème si c'est le thème actuel
            if theme_id == self.theme_manager.current_theme:
                app = QApplication.instance()
                if app:
                    apply_theme(app)
    
    def on_delete_theme(self) -> None:
        """Gère la suppression d'un thème."""
        theme_id = self.theme_combo.currentData()
        if not theme_id:
            return
        
        # Vérifier si c'est un thème prédéfini
        if theme_id in self.theme_manager.THEMES:
            QMessageBox.warning(
                self, "Avertissement",
                "Les thèmes prédéfinis ne peuvent pas être supprimés."
            )
            return
        
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment supprimer ce thème ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Supprimer le thème
            self.theme_manager.delete_custom_theme(theme_id)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
            
            # Appliquer le thème actuel
            app = QApplication.instance()
            if app:
                apply_theme(app)
    
    def on_new_dashboard(self) -> None:
        """Gère la création d'un nouveau tableau de bord."""
        # Créer un nouveau tableau de bord vide
        new_dashboard_data = {
            "name": "Nouveau tableau de bord",
            "widgets": []
        }
        
        # Ouvrir la boîte de dialogue d'édition de tableau de bord
        dialog = DashboardEditorDialog("new_dashboard", new_dashboard_data, self)
        if dialog.exec_():
            dashboard_data = dialog.get_dashboard_data()
            
            # Ajouter le tableau de bord
            dashboard_id = self.dashboard_config.add_dashboard(dashboard_data["name"])
            
            # Mettre à jour les widgets
            self.dashboard_config.update_dashboard(dashboard_id, dashboard_data)
            
            # Mettre à jour la liste des tableaux de bord
            self.update_dashboards_list()
    
    def on_edit_dashboard(self) -> None:
        """Gère la modification d'un tableau de bord."""
        current_item = self.dashboards_list.currentItem()
        if not current_item:
            return
        
        dashboard_id = current_item.data(Qt.UserRole)
        
        # Récupérer les données du tableau de bord
        dashboard_data = self.dashboard_config.get_dashboard(dashboard_id)
        
        # Ouvrir la boîte de dialogue d'édition de tableau de bord
        dialog = DashboardEditorDialog(dashboard_id, dashboard_data, self)
        if dialog.exec_():
            dashboard_data = dialog.get_dashboard_data()
            
            # Mettre à jour le tableau de bord
            self.dashboard_config.update_dashboard(dashboard_id, dashboard_data)
            
            # Mettre à jour la liste des tableaux de bord
            self.update_dashboards_list()
    
    def on_delete_dashboard(self) -> None:
        """Gère la suppression d'un tableau de bord."""
        current_item = self.dashboards_list.currentItem()
        if not current_item:
            return
        
        dashboard_id = current_item.data(Qt.UserRole)
        
        # Vérifier si c'est le seul tableau de bord
        if len(self.dashboard_config.get_all_dashboards()) <= 1:
            QMessageBox.warning(
                self, "Avertissement",
                "Vous ne pouvez pas supprimer le dernier tableau de bord."
            )
            return
        
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment supprimer ce tableau de bord ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Supprimer le tableau de bord
            self.dashboard_config.delete_dashboard(dashboard_id)
            
            # Mettre à jour la liste des tableaux de bord
            self.update_dashboards_list()
    
    def on_activate_dashboard(self) -> None:
        """Gère l'activation d'un tableau de bord."""
        current_item = self.dashboards_list.currentItem()
        if not current_item:
            return
        
        dashboard_id = current_item.data(Qt.UserRole)
        
        # Activer le tableau de bord
        self.dashboard_config.set_active_dashboard(dashboard_id)
        
        # Mettre à jour la liste des tableaux de bord
        self.update_dashboards_list()
        
        QMessageBox.information(
            self, "Information",
            "Le tableau de bord a été activé. Veuillez rafraîchir l'onglet des rapports pour voir les changements."
        )
    
    def on_clear_cache(self) -> None:
        """Gère le vidage du cache."""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment vider le cache ? Cela peut ralentir temporairement l'application.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            clear_cache()
            self.update_cache_stats()
            
            QMessageBox.information(
                self, "Information",
                "Le cache a été vidé."
            )
    
    def on_clean_cache(self) -> None:
        """Gère le nettoyage du cache expiré."""
        cleaned = clean_expired_cache()
        self.update_cache_stats()
        
        QMessageBox.information(
            self, "Information",
            f"{cleaned} entrées expirées ont été supprimées du cache."
        )
    
    def on_apply(self) -> None:
        """Gère l'application des paramètres."""
        # Appliquer les paramètres
        
        # Thème
        theme_id = self.theme_combo.currentData()
        if theme_id:
            self.theme_manager.set_theme(theme_id)
            
            # Appliquer le thème à l'application
            app = QApplication.instance()
            if app:
                apply_theme(app)
        
        # Options d'interface
        # TODO: Implémenter l'application des options d'interface
        
        # Options de performance
        # TODO: Implémenter l'application des options de performance
        
        QMessageBox.information(
            self, "Information",
            "Les paramètres ont été appliqués."
        )
    
    def on_reset(self) -> None:
        """Gère la réinitialisation des paramètres."""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment réinitialiser tous les paramètres ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Réinitialiser les paramètres
            
            # Thème
            self.theme_manager.set_theme("light")
            
            # Appliquer le thème à l'application
            app = QApplication.instance()
            if app:
                apply_theme(app)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
            
            # Options d'interface
            self.responsive_checkbox.setChecked(True)
            self.animations_checkbox.setChecked(True)
            self.tooltips_checkbox.setChecked(True)
            
            # Options de performance
            self.cache_enabled_checkbox.setChecked(True)
            self.cache_ttl_spinbox.setValue(300)
            
            QMessageBox.information(
                self, "Information",
                "Les paramètres ont été réinitialisés."
            )
    
    def refresh_tab(self) -> None:
        """Rafraîchit l'onglet."""
        self.update_theme_combo()
        self.update_dashboards_list()
        self.update_cache_stats()
        
    def on_reset_database(self) -> None:
        """Gère la réinitialisation de la base de données."""
        # Demander le mot de passe
        password, ok = QInputDialog.getText(
            self, 
            "Authentification requise", 
            "Entrez le mot de passe pour réinitialiser la base de données:",
            QLineEdit.Password
        )
        
        if not ok:
            return
        
        # Vérifier le mot de passe
        if password != "sotramine":
            QMessageBox.critical(
                self, 
                "Erreur d'authentification", 
                "Mot de passe incorrect. Opération annulée."
            )
            return
        
        # Demander confirmation
        reply = QMessageBox.warning(
            self, 
            "Confirmation de réinitialisation", 
            "ATTENTION: Vous êtes sur le point de réinitialiser la base de données.\n\n"
            "Toutes les données seront définitivement perdues.\n\n"
            "Êtes-vous absolument sûr de vouloir continuer?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        try:
            # Fermer la connexion à la base de données
            if hasattr(self.db_manager, 'engine'):
                self.db_manager.engine.dispose()
            
            # Supprimer le fichier de base de données
            # Extraire le nom de la base de données à partir de l'URL de connexion
            if hasattr(self.db_manager, 'engine'):
                db_url = str(self.db_manager.engine.url)
                if "sqlite:///" in db_url:
                    db_path = db_url.split("sqlite:///")[1]
                    logging.info(f"Suppression de la base de données: {db_path}")
                    if os.path.exists(db_path):
                        os.remove(db_path)
                        logging.info(f"Base de données supprimée: {db_path}")
                    else:
                        logging.warning(f"Fichier de base de données non trouvé: {db_path}")
                
            # Vérifier aussi les noms possibles de la base de données
            for possible_db in ["phosphate.db", "production.db"]:
                if os.path.exists(possible_db):
                    logging.info(f"Suppression de la base de données alternative: {possible_db}")
                    os.remove(possible_db)
                    logging.info(f"Base de données alternative supprimée: {possible_db}")
                
            # Réinitialiser la base de données
            if self.db_manager:
                self.db_manager.initialize_database()
                
            QMessageBox.information(
                self, 
                "Réinitialisation terminée", 
                "La base de données a été réinitialisée avec succès.\n\n"
                "L'application va maintenant redémarrer pour appliquer les changements."
            )
            
            # Redémarrer l'application
            QApplication.instance().quit()
            
        except Exception as e:
            logging.error(f"Erreur lors de la réinitialisation de la base de données: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self, 
                "Erreur", 
                f"Une erreur est survenue lors de la réinitialisation de la base de données:\n{str(e)}"
            )