#!/usr/bin/env python3
"""
Test pour démontrer et corriger la double comptabilisation entre concassage et laverie.
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_double_comptabilisation():
    """Teste la double comptabilisation."""
    print("🔍 Test de la double comptabilisation concassage → laverie")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Nettoyer les données existantes
        session = db_manager.Session()
        try:
            session.execute("DELETE FROM stock_movements")
            session.execute("DELETE FROM productions")
            session.commit()
            print("✅ Base de données nettoyée")
        except Exception as e:
            session.rollback()
            print(f"❌ Erreur nettoyage: {str(e)}")
        finally:
            session.close()
        
        print("\n📊 État initial:")
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f}T")
        
        # Étape 1: Ajouter une réception
        print("\n1️⃣ Ajout réception: 1000T")
        reception_id = db_manager.add_reception(
            quantity=1000.0,
            quality=28.0,
            reception_date=datetime.now()
        )
        
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        print(f"   Stock réception: {reception_stock:.1f}T")
        
        # Étape 2: Production concassage
        print("\n2️⃣ Production concassage: 800T produits, 900T consommés")
        concassage_id = db_manager.add_production(
            step=ProcessStepEnum.CONCASSAGE,
            quantity=800.0,  # Produit 800T
            quality=28.0,
            quantity_used=900.0,  # Consomme 900T de la réception
            production_hours=8.0
        )
        
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        print(f"   Stock réception après concassage: {reception_stock:.1f}T")
        print(f"   Stock concassage: {concassage_stock:.1f}T")
        
        # Étape 3: Production laverie
        print("\n3️⃣ Production laverie: 600T produits, 700T consommés")
        laverie_id = db_manager.add_production(
            step=ProcessStepEnum.LAVERIE,
            quantity=600.0,  # Produit 600T
            quality=32.0,
            quantity_used=700.0,  # Consomme 700T du concassage
            production_hours=10.0
        )
        
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        laverie_stock = db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        stockage_stock = db_manager.get_current_stock(ProcessStepEnum.STOCKAGE_FINAL)
        
        print(f"   Stock réception: {reception_stock:.1f}T")
        print(f"   Stock concassage: {concassage_stock:.1f}T")
        print(f"   Stock laverie: {laverie_stock:.1f}T")
        print(f"   Stock stockage final: {stockage_stock:.1f}T")
        
        # Analyse du problème
        print("\n🔍 ANALYSE DU PROBLÈME:")
        
        # Vérifier les mouvements de stock
        print("\n📋 Mouvements de stock concassage:")
        concassage_movements = db_manager.get_stock_movements(ProcessStepEnum.CONCASSAGE)
        for movement in concassage_movements:
            print(f"   {movement['movement_type']}: {movement['quantity']:+.1f}T")
        
        print("\n📋 Mouvements de stock stockage final:")
        stockage_movements = db_manager.get_stock_movements(ProcessStepEnum.STOCKAGE_FINAL)
        for movement in stockage_movements:
            print(f"   {movement['movement_type']}: {movement['quantity']:+.1f}T")
        
        # Calculer le bilan théorique
        print("\n📊 BILAN THÉORIQUE:")
        print(f"   Entrée totale: 1000.0T")
        print(f"   Production concassage: 800.0T (consomme 900T)")
        print(f"   Production laverie: 600.0T (consomme 700T)")
        print(f"   Pertes totales: {900 - 800 + 700 - 600:.1f}T")
        
        expected_reception = 1000 - 900  # 100T
        expected_concassage = 800 - 700  # 100T
        expected_laverie = 0  # Pas de stock
        expected_stockage = 600  # Production laverie
        
        print(f"\n📊 STOCKS ATTENDUS:")
        print(f"   Réception: {expected_reception:.1f}T")
        print(f"   Concassage: {expected_concassage:.1f}T")
        print(f"   Laverie: {expected_laverie:.1f}T")
        print(f"   Stockage final: {expected_stockage:.1f}T")
        
        print(f"\n📊 STOCKS RÉELS:")
        print(f"   Réception: {reception_stock:.1f}T")
        print(f"   Concassage: {concassage_stock:.1f}T")
        print(f"   Laverie: {laverie_stock:.1f}T")
        print(f"   Stockage final: {stockage_stock:.1f}T")
        
        # Identifier les problèmes
        problems = []
        
        if abs(reception_stock - expected_reception) > 0.1:
            problems.append(f"Réception: {reception_stock:.1f}T au lieu de {expected_reception:.1f}T")
        
        if abs(concassage_stock - expected_concassage) > 0.1:
            problems.append(f"Concassage: {concassage_stock:.1f}T au lieu de {expected_concassage:.1f}T")
        
        if abs(laverie_stock - expected_laverie) > 0.1:
            problems.append(f"Laverie: {laverie_stock:.1f}T au lieu de {expected_laverie:.1f}T")
        
        if abs(stockage_stock - expected_stockage) > 0.1:
            problems.append(f"Stockage: {stockage_stock:.1f}T au lieu de {expected_stockage:.1f}T")
        
        if problems:
            print(f"\n🚨 PROBLÈMES DÉTECTÉS:")
            for problem in problems:
                print(f"   ❌ {problem}")
            return False
        else:
            print(f"\n✅ AUCUN PROBLÈME DÉTECTÉ")
            return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_movements():
    """Analyse détaillée des mouvements."""
    print("\n🔍 Analyse détaillée des mouvements de stock")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        db_manager = DatabaseManager()
        
        # Analyser tous les mouvements
        session = db_manager.Session()
        try:
            from models.database_models import StockMovement
            movements = session.query(StockMovement).all()
            
            print(f"\n📋 Tous les mouvements ({len(movements)}):")
            for movement in movements:
                source = movement.source_step.name if movement.source_step else "N/A"
                dest = movement.destination_step.name if movement.destination_step else "N/A"
                step = movement.step.name if movement.step else "N/A"
                
                print(f"   {movement.movement_type.value}: {movement.quantity:+.1f}T")
                print(f"     Étape: {step}")
                print(f"     Source: {source} → Destination: {dest}")
                print(f"     Référence: {movement.reference_id}")
                print()
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Erreur analyse: {str(e)}")

def main():
    """Fonction principale."""
    print("🚀 Test de la double comptabilisation concassage → laverie")
    print("=" * 70)
    
    success = test_double_comptabilisation()
    analyze_movements()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ Aucune double comptabilisation détectée")
        return 0
    else:
        print("❌ Double comptabilisation confirmée - correction nécessaire")
        return 1

if __name__ == "__main__":
    sys.exit(main())
