"""
Onglet concassage compact avec indicateurs de performance bien dimensionnés et lisibles.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QSlider,
                            QSpinBox, QCheckBox, QGroupBox, QButtonGroup, QFileDialog,
                            QTabWidget, QWidget, QSplitter, QFrame, QProgressBar)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from datetime import datetime, timedelta
import logging
import pandas as pd

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import ActionButton
from ui.widgets.compact_kpi_widgets import (CompactKPICard, CompactKPISection, 
                                          CompactIndicator, KPITabWidget)
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class CompactCrushingTab(StandardizedBaseTab):
    """Onglet concassage compact avec indicateurs de performance bien dimensionnés et lisibles."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🔨 Concassage & Broyage", parent)
        self.is_running = False
        self.current_rate = 0
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface optimisée."""
        # Remplacer le layout KPI standard par notre section KPI optimisée
        self.kpi_group.setTitle("📊 Performance de Production")
        
        # Supprimer le layout existant et créer un nouveau
        self.kpi_layout.removeItem(self.kpi_cards_layout)
        
        # Créer un widget avec onglets pour les différentes catégories de KPI
        self.kpi_tabs = QTabWidget()
        self.kpi_tabs.setTabPosition(QTabWidget.North)
        self.kpi_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: transparent;
            }
            QTabBar::tab {
                padding: 5px 10px;
                margin-right: 2px;
                font-size: 9pt;
            }
        """)
        
        # Onglet Production
        self.production_tab = KPITabWidget()
        
        # Section Stocks
        stocks_section = self.production_tab.add_section("Stocks")
        self.stock_input_card = stocks_section.add_kpi_card(
            "Stock Entrée", "0", "T", None, "Entrée", "📦", SotramineTheme.PRIMARY)
        self.output_card = stocks_section.add_kpi_card(
            "Stock Sortie", "0", "T", None, "Sortie", "📦", SotramineTheme.PRIMARY)
        
        # Section Production
        production_section = self.production_tab.add_section("Production")
        self.production_card = production_section.add_kpi_card(
            "Production Jour", "0", "T", None, "Jour", "⚙️", SotramineTheme.SECONDARY)
        self.week_production_card = production_section.add_kpi_card(
            "Prod. Semaine", "0", "T", None, "Semaine", "⚙️", SotramineTheme.SECONDARY)
        self.month_production_card = production_section.add_kpi_card(
            "Prod. Mois", "0", "T", None, "Mois", "⚙️", SotramineTheme.SECONDARY)
        self.hourly_rate_card = production_section.add_kpi_card(
            "Rendement", "0", "T/h", None, "Efficacité", "⏱️", SotramineTheme.ACCENT)
        
        # Section Efficacité
        efficiency_section = self.production_tab.add_section("Efficacité")
        self.efficiency_card = efficiency_section.add_kpi_card(
            "Efficacité", "0", "%", None, "Ratio", "📈", SotramineTheme.ACCENT)
        self.uptime_card = efficiency_section.add_kpi_card(
            "Temps Opérationnel", "0", "%", None, "Temps", "⏱️", SotramineTheme.ACCENT)
        
        # Onglet Qualité
        self.quality_tab = KPITabWidget()
        
        # Section Indicateurs de qualité
        quality_indicators = self.quality_tab.add_indicators_section("Indicateurs de Qualité")
        
        # Indicateur de qualité P2O5
        self.p2o5_indicator = CompactIndicator(
            "Qualité P2O5", 0, 0, 40, 30, "%", SotramineTheme.PRIMARY)
        quality_indicators.addWidget(self.p2o5_indicator)
        
        # Indicateur de granulométrie
        self.granulometry_indicator = CompactIndicator(
            "Granulométrie", 0, 0, 100, 80, "%", SotramineTheme.SECONDARY)
        quality_indicators.addWidget(self.granulometry_indicator)
        
        # Indicateur d'humidité
        self.humidity_indicator = CompactIndicator(
            "Humidité", 0, 0, 20, 5, "%", SotramineTheme.ACCENT)
        quality_indicators.addWidget(self.humidity_indicator)
        
        # Section Statistiques de qualité
        quality_stats = self.quality_tab.add_section("Statistiques de Qualité")
        self.avg_quality_card = quality_stats.add_kpi_card(
            "Qualité Moyenne", "0", "%", None, "P2O5", "🔍", SotramineTheme.PRIMARY)
        self.quality_conformity_card = quality_stats.add_kpi_card(
            "Conformité", "0", "%", None, "Standard", "✓", SotramineTheme.SECONDARY)
        
        # Onglet Maintenance
        self.maintenance_tab = KPITabWidget()
        
        # Section Fiabilité
        reliability_section = self.maintenance_tab.add_section("Fiabilité")
        self.mtbf_card = reliability_section.add_kpi_card(
            "MTBF", "0", "h", None, "Fiabilité", "🔧", SotramineTheme.PRIMARY)
        self.mttr_card = reliability_section.add_kpi_card(
            "MTTR", "0", "h", None, "Fiabilité", "🔧", SotramineTheme.PRIMARY)
        self.availability_card = reliability_section.add_kpi_card(
            "Disponibilité", "0", "%", None, "Fiabilité", "✓", SotramineTheme.SECONDARY)
        
        # Section Arrêts
        downtime_section = self.maintenance_tab.add_section("Arrêts")
        self.downtime_card = downtime_section.add_kpi_card(
            "Temps d'Arrêt", "0", "h", None, "Total", "⚠️", SotramineTheme.DANGER)
        self.planned_downtime_card = downtime_section.add_kpi_card(
            "Arrêts Planifiés", "0", "h", None, "Planifié", "📅", SotramineTheme.ACCENT)
        self.unplanned_downtime_card = downtime_section.add_kpi_card(
            "Arrêts Non Planifiés", "0", "h", None, "Imprévu", "⚠️", SotramineTheme.DANGER)
        
        # Ajouter les onglets
        self.kpi_tabs.addTab(self.production_tab, "📊 Production")
        self.kpi_tabs.addTab(self.quality_tab, "🔍 Qualité")
        self.kpi_tabs.addTab(self.maintenance_tab, "🔧 Maintenance")
        
        self.kpi_layout.addWidget(self.kpi_tabs)
        
        # Section Saisie Manuelle (zone gauche)
        form_layout = self.add_form_section("📝 Saisie Production")
        
        fields = {
            'quantity_produced': {
                'label': 'Quantité Produite (T):',
                'type': 'number',
                'placeholder': 'Ex: 85.5',
                'category': 'Production'
            },
            'quantity_consumed': {
                'label': 'Quantité Consommée (T):',
                'type': 'number', 
                'placeholder': 'Ex: 100.0',
                'category': 'Production'
            },
            'production_hours': {
                'label': 'Heures de Production:',
                'type': 'number',
                'placeholder': 'Ex: 8.5',
                'category': 'Temps'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 30.5',
                'category': 'Qualité'
            },
            'granulometry': {
                'label': 'Granulométrie (%):',
                'type': 'number',
                'placeholder': 'Ex: 85.0',
                'category': 'Qualité'
            },
            'humidity': {
                'label': 'Humidité (%):',
                'type': 'number',
                'placeholder': 'Ex: 3.5',
                'category': 'Qualité'
            },
            'production_date': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'downtime': {
                'label': 'Temps d\'Arrêt (h):',
                'type': 'number',
                'placeholder': 'Ex: 0.5',
                'category': 'Maintenance'
            },
            'planned_maintenance': {
                'label': 'Maintenance Planifiée:',
                'type': 'checkbox',
                'default': False,
                'category': 'Maintenance'
            },
            'notes': {
                'label': 'Notes:',
                'type': 'text',
                'placeholder': 'Commentaires optionnels',
                'category': 'Autres'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_production)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche)
        actions_layout = self.add_actions_section("🔧 Actions Avancées")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Importer", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter Rapport", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau de production (zone droite)
        headers = ["Date/Heure", "Produit (T)", "Consommé (T)", "Heures", "Rendement (T/h)", 
                  "Efficacité (%)", "Qualité P2O5 (%)", "Granulométrie (%)", "Humidité (%)", "Actions"]
        self.data_table = self.add_data_table("📈 Historique de Production", headers, sortable=True, paginated=True)
        
        # Masquer certaines colonnes par défaut pour ne pas surcharger l'affichage
        self.data_table.setColumnHidden(7, True)  # Granulométrie
        self.data_table.setColumnHidden(8, True)  # Humidité
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les indicateurs de performance."""
        try:
            # Stock d'entrée (réception)
            input_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_input_card.update_value(f"{input_stock:.1f}")
            
            # Stock de sortie (concassage)
            output_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.output_card.update_value(f"{output_stock:.1f}")
            
            # Dates de référence
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Production du jour
            today_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=today
            )
            today_total = sum(p['quantity'] for p in today_productions)
            self.production_card.update_value(f"{today_total:.1f}")
            
            # Production de la semaine
            week_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_of_week
            )
            week_total = sum(p['quantity'] for p in week_productions)
            self.week_production_card.update_value(f"{week_total:.1f}")
            
            # Production du mois
            month_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_of_month
            )
            month_total = sum(p['quantity'] for p in month_productions)
            self.month_production_card.update_value(f"{month_total:.1f}")
            
            # Efficacité (production vs consommation)
            today_consumed = sum(p['quantity_used'] for p in today_productions if p['quantity_used'])
            efficiency = (today_total / today_consumed * 100) if today_consumed > 0 else 0
            self.efficiency_card.update_value(f"{efficiency:.1f}")
            
            # Rendement horaire moyen
            total_hours = sum(p['production_hours'] for p in today_productions if p['production_hours'])
            hourly_rate = (today_total / total_hours) if total_hours > 0 else 0
            self.hourly_rate_card.update_value(f"{hourly_rate:.1f}")
            
            # Temps opérationnel (simulé pour l'exemple)
            uptime = 92.5  # En pourcentage
            self.uptime_card.update_value(f"{uptime:.1f}")
            
            # Indicateurs de qualité (simulés pour l'exemple)
            p2o5_quality = 31.2  # Pourcentage P2O5
            granulometry = 87.5  # Pourcentage conforme
            humidity = 4.2  # Pourcentage d'humidité
            
            self.p2o5_indicator.update_value(p2o5_quality)
            self.granulometry_indicator.update_value(granulometry)
            self.humidity_indicator.update_value(humidity)
            
            # Statistiques de qualité
            self.avg_quality_card.update_value(f"{p2o5_quality:.1f}")
            self.quality_conformity_card.update_value(f"{granulometry:.1f}")
            
            # Indicateurs de maintenance (simulés pour l'exemple)
            mtbf = 120.5  # Heures
            mttr = 3.2  # Heures
            availability = 97.4  # Pourcentage
            downtime = 8.5  # Heures
            planned_downtime = 6.0  # Heures
            unplanned_downtime = 2.5  # Heures
            
            self.mtbf_card.update_value(f"{mtbf:.1f}")
            self.mttr_card.update_value(f"{mttr:.1f}")
            self.availability_card.update_value(f"{availability:.1f}")
            self.downtime_card.update_value(f"{downtime:.1f}")
            self.planned_downtime_card.update_value(f"{planned_downtime:.1f}")
            self.unplanned_downtime_card.update_value(f"{unplanned_downtime:.1f}")
            
            # Mettre à jour la barre d'état
            self.update_status(f"Dernière mise à jour: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI concassage: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données avec pagination."""
        try:
            self.update_status("Chargement des données...")
            
            # Récupérer les paramètres de pagination
            pagination = self.get_pagination_params()
            
            # Dernières productions (avec pagination)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 derniers jours par défaut
            
            # Récupérer le nombre total d'éléments pour la pagination
            total_count = self.db_manager.get_production_count(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date
            )
            self.total_items = total_count
            
            # Récupérer les données paginées
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date,
                limit=pagination['limit'],
                offset=pagination['offset'],
                order_by='production_date',
                order_direction='DESC'  # Plus récent en haut
            )
            
            self.populate_table(productions)
            self.update_status(f"Affichage de {len(productions)} enregistrements sur {total_count}")
            
        except Exception as e:
            logging.error(f"Erreur actualisation données concassage: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def populate_table(self, productions):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(productions))
        
        for row, production in enumerate(productions):
            # Date/Heure
            self.data_table.setItem(row, 0,
                self.data_table.itemClass()(production['production_date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité produite
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{production['quantity']:.2f}"))
            
            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))
            
            # Heures
            hours = production.get('production_hours', 0)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()(f"{hours:.2f}" if hours else "-"))
            
            # Rendement
            if hours and hours > 0:
                rendement = production['quantity'] / hours
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()(f"{rendement:.2f}"))
            else:
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()("-"))
            
            # Efficacité
            if consumed and consumed > 0:
                efficacity = (production['quantity'] / consumed) * 100
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()(f"{efficacity:.1f}%"))
            else:
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()("-"))
            
            # Qualité P2O5
            quality = production.get('quality', '')
            self.data_table.setItem(row, 6,
                self.data_table.itemClass()(quality if quality else "-"))
            
            # Granulométrie (simulée pour l'exemple)
            self.data_table.setItem(row, 7,
                self.data_table.itemClass()(f"{85 + row % 10:.1f}%"))
            
            # Humidité (simulée pour l'exemple)
            self.data_table.setItem(row, 8,
                self.data_table.itemClass()(f"{3 + (row % 5) / 2:.1f}%"))
            
            # Actions
            self.data_table.setItem(row, 9,
                self.data_table.itemClass()("📝 Modifier | 🗑️ Supprimer"))
                    
    def save_production(self, data):
        """Sauvegarde une production manuelle."""
        try:
            # Validation
            if not data.get('quantity_produced') or float(data['quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return
                
            quantity_produced = float(data['quantity_produced'])
            quantity_consumed = float(data.get('quantity_consumed', 0)) if data.get('quantity_consumed') else 0
            hours = float(data.get('production_hours', 0)) if data.get('production_hours') else 0
            quality = data.get('quality', '30.0')
            granulometry = data.get('granulometry', '')
            humidity = data.get('humidity', '')
            production_date = data.get('production_date', datetime.now())
            downtime = float(data.get('downtime', 0)) if data.get('downtime') else 0
            planned_maintenance = data.get('planned_maintenance', False)
            notes = data.get('notes', '')
            
            # Préparer les métadonnées
            metadata = {
                'granulometry': granulometry,
                'humidity': humidity,
                'downtime': downtime,
                'planned_maintenance': planned_maintenance,
                'notes': notes
            }
            
            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality}%" if quality else None,
                production_date=production_date,
                notes=notes,
                metadata=metadata
            )
            
            # Mouvements de stock
            if quantity_consumed > 0:
                # Consommer du stock de réception
                self.db_manager.add_stock_movement(
                    step=ProcessStepEnum.RECEPTION,
                    quantity=-quantity_consumed,
                    movement_type=StockMovementType.CONSUMPTION,
                    movement_date=production_date
                )
            
            # Ajouter au stock de concassage
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                movement_type=StockMovementType.PRODUCTION,
                movement_date=production_date,
                quality=float(quality) if quality else None,
                metadata={
                    'granulometry': granulometry,
                    'humidity': humidity
                }
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Succès",
                f"Production de {quantity_produced:.1f}T enregistrée")
                
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
            
    def import_from_excel(self):
        """Importe des données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "Importer fichier Excel", 
                "", 
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['Quantité', 'Date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "Erreur", 
                    f"Colonnes manquantes: {', '.join(missing_columns)}")
                return
                
            # Progress bar
            progress = QProgressBar()
            progress.setMaximum(len(df))
            progress.show()
            
            imported_count = 0
            
            for index, row in df.iterrows():
                try:
                    quantity = float(row['Quantité'])
                    date = pd.to_datetime(row['Date']).to_pydatetime()
                    quality = float(row.get('Qualité', 0)) if pd.notna(row.get('Qualité')) else None
                    consumed = float(row.get('Consommé', 0)) if pd.notna(row.get('Consommé')) else None
                    hours = float(row.get('Heures', 0)) if pd.notna(row.get('Heures')) else None
                    granulometry = float(row.get('Granulométrie', 0)) if pd.notna(row.get('Granulométrie')) else None
                    humidity = float(row.get('Humidité', 0)) if pd.notna(row.get('Humidité')) else None
                    
                    # Métadonnées
                    metadata = {}
                    if granulometry:
                        metadata['granulometry'] = granulometry
                    if humidity:
                        metadata['humidity'] = humidity
                    
                    # Enregistrer la production
                    self.db_manager.add_production(
                        step=ProcessStepEnum.CONCASSAGE,
                        quantity=quantity,
                        quantity_used=consumed,
                        production_hours=hours,
                        quality=f"{quality}%" if quality else None,
                        production_date=date,
                        metadata=metadata
                    )
                    
                    # Mouvements de stock
                    if consumed and consumed > 0:
                        self.db_manager.add_stock_movement(
                            step=ProcessStepEnum.RECEPTION,
                            quantity=-consumed,
                            movement_type=StockMovementType.CONSUMPTION,
                            movement_date=date
                        )
                    
                    self.db_manager.add_stock_movement(
                        step=ProcessStepEnum.CONCASSAGE,
                        quantity=quantity,
                        movement_type=StockMovementType.PRODUCTION,
                        movement_date=date,
                        quality=quality,
                        metadata=metadata
                    )
                    
                    imported_count += 1
                    progress.setValue(index + 1)
                    
                except Exception as e:
                    logging.warning(f"Erreur ligne {index + 1}: {str(e)}")
                    continue
                    
            progress.close()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Import Terminé", 
                f"{imported_count} productions importées avec succès")
                
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
            
    def export_detailed_report(self):
        """Exporte un rapport détaillé avec graphiques et analyses."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter Rapport Détaillé",
                f"rapport_concassage_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            self.update_status("Génération du rapport détaillé...")
            
            # Récupérer les données des 30 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date,
                limit=1000  # Nombre élevé pour avoir toutes les données
            )
            
            # Créer un DataFrame avec les données
            data = []
            for prod in productions:
                data.append({
                    'Date': prod['production_date'],
                    'Quantité Produite (T)': prod['quantity'],
                    'Quantité Consommée (T)': prod.get('quantity_used', 0) or 0,
                    'Heures de Production': prod.get('production_hours', 0) or 0,
                    'Rendement (T/h)': prod['quantity'] / prod['production_hours'] if prod.get('production_hours', 0) else 0,
                    'Efficacité (%)': (prod['quantity'] / prod['quantity_used'] * 100) if prod.get('quantity_used', 0) else 0,
                    'Qualité (%)': prod.get('quality', '').replace('%', '') if prod.get('quality') else ''
                })
                
            df = pd.DataFrame(data)
            
            # Créer un writer Excel avec xlsxwriter
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # Écrire les données brutes
            df.to_excel(writer, sheet_name='Données', index=False)
            
            # Créer une feuille de résumé
            summary = pd.DataFrame({
                'Métrique': [
                    'Total Produit (T)',
                    'Total Consommé (T)',
                    'Heures Totales',
                    'Rendement Moyen (T/h)',
                    'Efficacité Moyenne (%)',
                    'Qualité Moyenne (%)'
                ],
                'Valeur': [
                    df['Quantité Produite (T)'].sum(),
                    df['Quantité Consommée (T)'].sum(),
                    df['Heures de Production'].sum(),
                    df['Quantité Produite (T)'].sum() / df['Heures de Production'].sum() if df['Heures de Production'].sum() > 0 else 0,
                    df['Quantité Produite (T)'].sum() / df['Quantité Consommée (T)'].sum() * 100 if df['Quantité Consommée (T)'].sum() > 0 else 0,
                    pd.to_numeric(df['Qualité (%)'], errors='coerce').mean()
                ]
            })
            
            summary.to_excel(writer, sheet_name='Résumé', index=False)
            
            # Sauvegarder et fermer
            writer.save()
            
            self.update_status(f"Rapport détaillé exporté vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Rapport détaillé exporté vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export rapport: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export du rapport: {str(e)}")