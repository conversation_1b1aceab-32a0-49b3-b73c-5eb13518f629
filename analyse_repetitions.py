#!/usr/bin/env python3
"""
Script d'analyse globale pour identifier les répétitions dans l'application.
"""

import os
import sys
import ast
import re
from collections import defaultdict, Counter
from pathlib import Path

def analyze_imports():
    """Analyse les imports redondants."""
    print("🔍 Analyse des imports redondants...")
    
    imports_by_file = {}
    all_imports = defaultdict(list)
    
    for root, dirs, files in os.walk('.'):
        # Ignorer certains dossiers
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # Extraire les imports
                    imports = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith('import ') or line.startswith('from '):
                            imports.append(line)
                            all_imports[line].append(file_path)
                    
                    imports_by_file[file_path] = imports
                    
                except Exception as e:
                    continue
    
    # Identifier les imports les plus fréquents
    frequent_imports = {imp: files for imp, files in all_imports.items() if len(files) > 3}
    
    print(f"📊 Imports les plus fréquents:")
    for imp, files in sorted(frequent_imports.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
        print(f"  {imp} ({len(files)} fichiers)")
    
    return frequent_imports

def analyze_duplicate_classes():
    """Analyse les classes dupliquées ou similaires."""
    print("\n🔍 Analyse des classes dupliquées...")
    
    classes_by_name = defaultdict(list)
    
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Rechercher les définitions de classes
                    class_matches = re.findall(r'^class\s+(\w+)', content, re.MULTILINE)
                    for class_name in class_matches:
                        classes_by_name[class_name].append(file_path)
                        
                except Exception as e:
                    continue
    
    # Identifier les classes dupliquées
    duplicate_classes = {name: files for name, files in classes_by_name.items() if len(files) > 1}
    
    print(f"📊 Classes potentiellement dupliquées:")
    for class_name, files in sorted(duplicate_classes.items()):
        if len(files) > 1:
            print(f"  {class_name}:")
            for file in files:
                print(f"    - {file}")
    
    return duplicate_classes

def analyze_duplicate_methods():
    """Analyse les méthodes dupliquées."""
    print("\n🔍 Analyse des méthodes dupliquées...")
    
    methods_by_name = defaultdict(list)
    
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Rechercher les définitions de méthodes
                    method_matches = re.findall(r'^\s*def\s+(\w+)', content, re.MULTILINE)
                    for method_name in method_matches:
                        if not method_name.startswith('_'):  # Ignorer les méthodes privées
                            methods_by_name[method_name].append(file_path)
                        
                except Exception as e:
                    continue
    
    # Identifier les méthodes les plus fréquentes (potentiellement dupliquées)
    frequent_methods = {name: files for name, files in methods_by_name.items() 
                       if len(files) > 3 and name not in ['__init__', 'setup', 'update']}
    
    print(f"📊 Méthodes les plus fréquentes:")
    for method_name, files in sorted(frequent_methods.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
        print(f"  {method_name} ({len(files)} fichiers)")
    
    return frequent_methods

def analyze_ui_tabs():
    """Analyse spécifique des onglets UI pour identifier les répétitions."""
    print("\n🔍 Analyse des onglets UI...")
    
    tab_files = []
    ui_tabs_dir = Path('ui/tabs')
    
    if ui_tabs_dir.exists():
        for file in ui_tabs_dir.glob('*.py'):
            if file.name != '__init__.py':
                tab_files.append(str(file))
    
    print(f"📊 Onglets trouvés: {len(tab_files)}")
    
    # Analyser les patterns dans les noms de fichiers
    tab_patterns = defaultdict(list)
    for tab_file in tab_files:
        filename = Path(tab_file).stem
        
        # Identifier les patterns
        if 'reception' in filename:
            tab_patterns['reception'].append(filename)
        elif 'crushing' in filename or 'concassage' in filename:
            tab_patterns['crushing'].append(filename)
        elif 'laverie' in filename or 'washing' in filename:
            tab_patterns['laverie'].append(filename)
        elif 'stock' in filename:
            tab_patterns['stock'].append(filename)
        elif 'report' in filename:
            tab_patterns['reports'].append(filename)
    
    print("📊 Onglets par catégorie:")
    for category, files in tab_patterns.items():
        print(f"  {category}: {len(files)} fichiers")
        for file in files:
            print(f"    - {file}")
    
    return tab_patterns

def analyze_widgets():
    """Analyse les widgets pour identifier les répétitions."""
    print("\n🔍 Analyse des widgets...")
    
    widget_files = []
    ui_widgets_dir = Path('ui/widgets')
    
    if ui_widgets_dir.exists():
        for file in ui_widgets_dir.glob('*.py'):
            if file.name != '__init__.py':
                widget_files.append(str(file))
    
    print(f"📊 Fichiers de widgets trouvés: {len(widget_files)}")
    
    # Analyser les patterns dans les noms
    widget_patterns = defaultdict(list)
    for widget_file in widget_files:
        filename = Path(widget_file).stem
        
        if 'kpi' in filename:
            widget_patterns['kpi'].append(filename)
        elif 'professional' in filename:
            widget_patterns['professional'].append(filename)
        elif 'minimal' in filename:
            widget_patterns['minimal'].append(filename)
        elif 'compact' in filename:
            widget_patterns['compact'].append(filename)
    
    print("📊 Widgets par type:")
    for widget_type, files in widget_patterns.items():
        print(f"  {widget_type}: {len(files)} fichiers")
        for file in files:
            print(f"    - {file}")
    
    return widget_patterns

def analyze_database_managers():
    """Analyse les gestionnaires de base de données."""
    print("\n🔍 Analyse des gestionnaires de base de données...")
    
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if 'database' in file.lower() or 'db_manager' in file.lower():
                if file.endswith('.py'):
                    db_files.append(os.path.join(root, file))
    
    print(f"📊 Fichiers de base de données trouvés: {len(db_files)}")
    for file in db_files:
        print(f"  - {file}")
    
    return db_files

def analyze_test_files():
    """Analyse les fichiers de test pour identifier les répétitions."""
    print("\n🔍 Analyse des fichiers de test...")
    
    test_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.startswith('test_') and file.endswith('.py'):
                test_files.append(os.path.join(root, file))
    
    print(f"📊 Fichiers de test trouvés: {len(test_files)}")
    
    # Analyser les patterns de test
    test_patterns = defaultdict(list)
    for test_file in test_files:
        filename = Path(test_file).stem
        
        if 'flux' in filename:
            test_patterns['flux'].append(filename)
        elif 'correction' in filename:
            test_patterns['corrections'].append(filename)
        elif 'reception' in filename:
            test_patterns['reception'].append(filename)
    
    print("📊 Tests par catégorie:")
    for category, files in test_patterns.items():
        print(f"  {category}: {len(files)} fichiers")
        for file in files:
            print(f"    - {file}")
    
    return test_patterns

def main():
    """Fonction principale d'analyse."""
    print("🚀 Analyse globale des répétitions dans l'application")
    print("=" * 70)
    
    analyses = [
        ("Imports redondants", analyze_imports),
        ("Classes dupliquées", analyze_duplicate_classes),
        ("Méthodes dupliquées", analyze_duplicate_methods),
        ("Onglets UI", analyze_ui_tabs),
        ("Widgets", analyze_widgets),
        ("Gestionnaires DB", analyze_database_managers),
        ("Fichiers de test", analyze_test_files),
    ]
    
    results = {}
    
    for analysis_name, analysis_func in analyses:
        try:
            result = analysis_func()
            results[analysis_name] = result
        except Exception as e:
            print(f"❌ Erreur dans {analysis_name}: {str(e)}")
            results[analysis_name] = None
    
    # Résumé des problèmes identifiés
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES RÉPÉTITIONS IDENTIFIÉES")
    print("=" * 70)
    
    # Compter les problèmes
    total_issues = 0
    
    if results.get("Onglets UI"):
        ui_issues = sum(len(files) for files in results["Onglets UI"].values() if len(files) > 1)
        if ui_issues > 0:
            print(f"🔴 Onglets UI: {ui_issues} fichiers redondants détectés")
            total_issues += ui_issues
    
    if results.get("Widgets"):
        widget_issues = sum(len(files) for files in results["Widgets"].values() if len(files) > 1)
        if widget_issues > 0:
            print(f"🔴 Widgets: {widget_issues} fichiers redondants détectés")
            total_issues += widget_issues
    
    if results.get("Gestionnaires DB"):
        db_issues = len(results["Gestionnaires DB"]) if len(results["Gestionnaires DB"]) > 1 else 0
        if db_issues > 1:
            print(f"🔴 Gestionnaires DB: {db_issues} fichiers détectés")
            total_issues += db_issues - 1
    
    print(f"\n📈 Total des répétitions identifiées: {total_issues}")
    
    if total_issues > 0:
        print("⚠️  Des corrections sont recommandées pour éliminer les répétitions.")
        return 1
    else:
        print("✅ Aucune répétition majeure détectée.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
