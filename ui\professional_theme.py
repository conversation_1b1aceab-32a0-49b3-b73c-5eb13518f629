"""
Module contenant le thème professionnel SOTRAMINE PHOSPHATE.
"""

# Palette de couleurs SOTRAMINE PHOSPHATE
class SotramineTheme:
    """Thème professionnel pour SOTRAMINE PHOSPHATE"""
    
    # Couleurs principales
    PRIMARY = "#1E3A8A"        # Bleu foncé professionnel
    PRIMARY_LIGHT = "#3B82F6"  # Bleu moyen
    PRIMARY_DARK = "#1E40AF"   # Bleu très foncé
    
    SECONDARY = "#059669"      # Vert pour success/production
    SECONDARY_LIGHT = "#10B981" # Vert clair
    SECONDARY_DARK = "#047857"  # Vert foncé
    
    ACCENT = "#F59E0B"         # Orange pour warnings/attention
    ACCENT_LIGHT = "#FCD34D"   # Orange clair
    ACCENT_DARK = "#D97706"    # Orange foncé
    
    DANGER = "#DC2626"         # Rouge pour erreurs/arrêts
    DANGER_LIGHT = "#F87171"   # Rouge clair
    DANGER_DARK = "#B91C1C"    # Rouge foncé
    
    # Couleurs neutres
    WHITE = "#FFFFFF"
    GRAY_50 = "#F9FAFB"
    GRAY_100 = "#F3F4F6"
    GRAY_200 = "#E5E7EB"
    GRAY_300 = "#D1D5DB"
    GRAY_400 = "#9CA3AF"
    GRAY_500 = "#6B7280"
    GRAY_600 = "#4B5563"
    GRAY_700 = "#374151"
    GRAY_800 = "#1F2937"
    GRAY_900 = "#111827"
    
    # Couleurs de texte
    TEXT_PRIMARY = GRAY_900
    TEXT_SECONDARY = GRAY_600
    TEXT_MUTED = GRAY_500
    TEXT_WHITE = WHITE
    
    # Couleurs de fond
    BG_PRIMARY = WHITE
    BG_SECONDARY = GRAY_50
    BG_CARD = WHITE
    BG_SIDEBAR = GRAY_100

def get_professional_style():
    """Retourne le style CSS professionnel pour l'application."""
    
    return f"""
    /* Fenêtre principale */
    QMainWindow {{
        background-color: {SotramineTheme.BG_SECONDARY};
        color: {SotramineTheme.TEXT_PRIMARY};
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 9pt;
    }}
    
    /* Onglets */
    QTabWidget::pane {{
        border: 1px solid {SotramineTheme.GRAY_300};
        background-color: {SotramineTheme.BG_PRIMARY};
        border-radius: 8px;
        margin-top: 2px;
    }}
    
    QTabBar::tab {{
        background-color: {SotramineTheme.GRAY_100};
        border: 1px solid {SotramineTheme.GRAY_300};
        border-bottom: none;
        padding: 12px 20px;
        margin-right: 2px;
        font-weight: bold;
        color: {SotramineTheme.TEXT_SECONDARY};
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        min-width: 100px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {SotramineTheme.PRIMARY};
        color: {SotramineTheme.TEXT_WHITE};
        border-bottom: 3px solid {SotramineTheme.PRIMARY_LIGHT};
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {SotramineTheme.PRIMARY_LIGHT};
        color: {SotramineTheme.TEXT_WHITE};
    }}
    
    /* GroupBox */
    QGroupBox {{
        font-weight: bold;
        border: 2px solid {SotramineTheme.GRAY_300};
        border-radius: 8px;
        margin-top: 12px;
        padding-top: 15px;
        background-color: {SotramineTheme.BG_CARD};
        color: {SotramineTheme.TEXT_PRIMARY};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 5px 10px;
        background-color: {SotramineTheme.PRIMARY};
        color: {SotramineTheme.TEXT_WHITE};
        border-radius: 4px;
        font-size: 10pt;
        font-weight: bold;
    }}
    
    /* Boutons */
    QPushButton {{
        background-color: {SotramineTheme.PRIMARY};
        color: {SotramineTheme.TEXT_WHITE};
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 9pt;
        min-height: 32px;
    }}
    
    QPushButton:hover {{
        background-color: {SotramineTheme.PRIMARY_LIGHT};
        transform: translateY(-1px);
    }}
    
    QPushButton:pressed {{
        background-color: {SotramineTheme.PRIMARY_DARK};
        transform: translateY(1px);
    }}
    
    QPushButton:disabled {{
        background-color: {SotramineTheme.GRAY_300};
        color: {SotramineTheme.GRAY_500};
    }}
    
    /* Boutons de succès */
    QPushButton[class="success"] {{
        background-color: {SotramineTheme.SECONDARY};
    }}
    
    QPushButton[class="success"]:hover {{
        background-color: {SotramineTheme.SECONDARY_LIGHT};
    }}
    
    /* Boutons de danger */
    QPushButton[class="danger"] {{
        background-color: {SotramineTheme.DANGER};
    }}
    
    QPushButton[class="danger"]:hover {{
        background-color: {SotramineTheme.DANGER_LIGHT};
    }}
    
    /* Boutons d'attention */
    QPushButton[class="warning"] {{
        background-color: {SotramineTheme.ACCENT};
    }}
    
    QPushButton[class="warning"]:hover {{
        background-color: {SotramineTheme.ACCENT_LIGHT};
    }}
    
    /* Champs de saisie */
    QLineEdit, QDateEdit, QTimeEdit, QDateTimeEdit, QSpinBox, QDoubleSpinBox {{
        padding: 8px 12px;
        border: 2px solid {SotramineTheme.GRAY_300};
        border-radius: 6px;
        background-color: {SotramineTheme.BG_PRIMARY};
        color: {SotramineTheme.TEXT_PRIMARY};
        font-size: 9pt;
        min-height: 20px;
    }}
    
    QLineEdit:focus, QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
        border-color: {SotramineTheme.PRIMARY};
        outline: none;
    }}
    
    QLineEdit:disabled, QDateEdit:disabled, QTimeEdit:disabled, QDateTimeEdit:disabled {{
        background-color: {SotramineTheme.GRAY_100};
        color: {SotramineTheme.GRAY_500};
    }}
    
    /* Labels */
    QLabel {{
        color: {SotramineTheme.TEXT_PRIMARY};
        font-size: 9pt;
    }}
    
    QLabel[class="title"] {{
        font-size: 14pt;
        font-weight: bold;
        color: {SotramineTheme.PRIMARY};
    }}
    
    QLabel[class="subtitle"] {{
        font-size: 11pt;
        font-weight: bold;
        color: {SotramineTheme.TEXT_SECONDARY};
    }}
    
    QLabel[class="muted"] {{
        color: {SotramineTheme.TEXT_MUTED};
        font-size: 8pt;
    }}
    
    /* Tableaux */
    QTableWidget {{
        border: 1px solid {SotramineTheme.GRAY_300};
        gridline-color: {SotramineTheme.GRAY_200};
        background-color: {SotramineTheme.BG_PRIMARY};
        alternate-background-color: {SotramineTheme.GRAY_50};
        selection-background-color: {SotramineTheme.PRIMARY_LIGHT};
        selection-color: {SotramineTheme.TEXT_WHITE};
        border-radius: 6px;
    }}
    
    QTableWidget::item {{
        padding: 8px;
        border: none;
        color: {SotramineTheme.TEXT_PRIMARY};
        font-size: 9pt;
    }}
    
    QTableWidget::item:selected {{
        background-color: {SotramineTheme.PRIMARY_LIGHT};
        color: {SotramineTheme.TEXT_WHITE};
    }}
    
    QHeaderView::section {{
        background-color: {SotramineTheme.PRIMARY};
        color: {SotramineTheme.TEXT_WHITE};
        padding: 10px 8px;
        border: none;
        font-weight: bold;
        font-size: 9pt;
    }}
    
    QHeaderView::section:first {{
        border-top-left-radius: 6px;
    }}
    
    QHeaderView::section:last {{
        border-top-right-radius: 6px;
    }}
    
    /* ComboBox */
    QComboBox {{
        padding: 8px 12px;
        border: 2px solid {SotramineTheme.GRAY_300};
        border-radius: 6px;
        background-color: {SotramineTheme.BG_PRIMARY};
        color: {SotramineTheme.TEXT_PRIMARY};
        font-size: 9pt;
        min-height: 20px;
    }}
    
    QComboBox:focus {{
        border-color: {SotramineTheme.PRIMARY};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid {SotramineTheme.PRIMARY};
    }}
    
    /* Barres de défilement */
    QScrollBar:vertical {{
        background-color: {SotramineTheme.GRAY_100};
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {SotramineTheme.GRAY_400};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {SotramineTheme.PRIMARY};
    }}
    
    QScrollBar:horizontal {{
        background-color: {SotramineTheme.GRAY_100};
        height: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {SotramineTheme.GRAY_400};
        border-radius: 6px;
        min-width: 20px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {SotramineTheme.PRIMARY};
    }}
    
    /* Barre d'état */
    QStatusBar {{
        background-color: {SotramineTheme.PRIMARY_DARK};
        color: {SotramineTheme.TEXT_WHITE};
        border-top: 2px solid {SotramineTheme.PRIMARY};
        font-weight: bold;
    }}
    
    /* ToolButtons */
    QToolButton {{
        border: none;
        background-color: transparent;
        padding: 6px;
        border-radius: 4px;
    }}
    
    QToolButton:hover {{
        background-color: {SotramineTheme.GRAY_200};
    }}
    
    QToolButton:pressed {{
        background-color: {SotramineTheme.GRAY_300};
    }}
    
    /* Frames et sections */
    QFrame[class="card"] {{
        background-color: {SotramineTheme.BG_CARD};
        border: 1px solid {SotramineTheme.GRAY_200};
        border-radius: 8px;
        padding: 15px;
    }}
    
    QFrame[class="indicator"] {{
        background-color: {SotramineTheme.BG_CARD};
        border: 2px solid {SotramineTheme.PRIMARY_LIGHT};
        border-radius: 10px;
        padding: 10px;
    }}
    
    /* Progress Bar */
    QProgressBar {{
        border: 2px solid {SotramineTheme.GRAY_300};
        border-radius: 6px;
        text-align: center;
        font-weight: bold;
        color: {SotramineTheme.TEXT_PRIMARY};
        background-color: {SotramineTheme.GRAY_100};
    }}
    
    QProgressBar::chunk {{
        background-color: {SotramineTheme.PRIMARY};
        border-radius: 4px;
    }}
    """

def get_icon_style():
    """Style spécial pour les icônes et boutons avec icônes."""
    return """
    QPushButton[hasIcon="true"] {
        text-align: left;
        padding-left: 35px;
    }
    
    QPushButton[class="icon-only"] {
        min-width: 40px;
        max-width: 40px;
        min-height: 40px;
        max-height: 40px;
        padding: 8px;
    }
    """
