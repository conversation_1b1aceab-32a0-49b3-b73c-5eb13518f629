# Standard library imports
import logging
import os
from datetime import datetime, timedelta

# Third-party imports
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QLabel, QComboBox, QPushButton, QDateEdit, QScrollArea,
                             QSplitter, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QMessageBox, QFileDialog)

# Local imports
from core.services.report_service import ReportService
from models.enums import ProcessStepEnum, ResourceType
from ui.widgets.gauge_widget import GaugeWidget

class ReportsTab(QWidget):
    """Onglet des rapports et tableaux de bord."""
    
    def __init__(self, db_manager, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.db_manager = db_manager
        self.report_service = ReportService(db_manager)
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # En-tête avec filtres
        header_layout = QHBoxLayout()
        
        # Période
        header_layout.addWidget(QLabel("Période:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Cette année", "Personnalisée"])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        header_layout.addWidget(self.period_combo)
        
        # Dates personnalisées
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setEnabled(False)
        header_layout.addWidget(QLabel("Du:"))
        header_layout.addWidget(self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setEnabled(False)
        header_layout.addWidget(QLabel("Au:"))
        header_layout.addWidget(self.end_date)
        
        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.clicked.connect(self.refresh_reports)
        header_layout.addWidget(self.refresh_button)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        self.export_button.clicked.connect(self.export_reports)
        header_layout.addWidget(self.export_button)
        
        # Bouton de génération de rapport PDF
        self.pdf_button = QPushButton("Générer PDF")
        self.pdf_button.clicked.connect(self.generate_pdf_report)
        self.pdf_button.setIcon(QIcon("resources/icons/pdf.png"))
        header_layout.addWidget(self.pdf_button)
        
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Contenu principal avec défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # Tableau de bord
        dashboard_group = QGroupBox("Tableau de bord")
        dashboard_layout = QVBoxLayout()
        
        # Indicateurs clés de performance (KPI)
        kpi_layout = QHBoxLayout()
        
        # Jauges de stock
        self.reception_gauge = GaugeWidget(title="Stock Réception", unit="t")
        self.reception_gauge.set_range(0, 1000)
        kpi_layout.addWidget(self.reception_gauge)
        
        self.crushing_gauge = GaugeWidget(title="Disponible Laverie", unit="t")
        self.crushing_gauge.set_range(0, 500)
        kpi_layout.addWidget(self.crushing_gauge)
        
        self.laverie_gauge = GaugeWidget(title="Stock Laverie", unit="t")
        self.laverie_gauge.set_range(0, 300)
        kpi_layout.addWidget(self.laverie_gauge)
        
        dashboard_layout.addLayout(kpi_layout)
        
        # Résumé des consommations et productivité
        summary_layout = QHBoxLayout()
        
        # Créer un groupe pour les consommations
        consumption_group = QGroupBox("Résumé des consommations")
        consumption_inner_layout = QVBoxLayout()
        
        # Tableau des consommations
        self.consumption_summary_table = QTableWidget()
        self.consumption_summary_table.setColumnCount(4)
        self.consumption_summary_table.setHorizontalHeaderLabels([
            "Ressource", "Aujourd'hui", "Cette semaine", "Ce mois"
        ])
        self.consumption_summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.consumption_summary_table.setRowCount(3)
        
        # Lignes pour chaque type de ressource
        resource_types = ["Eau (m³)", "Énergie (kWh)", "Réactifs (kg)"]
        for i, resource in enumerate(resource_types):
            resource_item = QTableWidgetItem(resource)
            resource_item.setTextAlignment(Qt.AlignCenter)
            self.consumption_summary_table.setItem(i, 0, resource_item)
            
            # Valeurs initiales
            for j in range(1, 4):
                value_item = QTableWidgetItem("0.00")
                value_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_summary_table.setItem(i, j, value_item)
        
        consumption_inner_layout.addWidget(self.consumption_summary_table)
        consumption_group.setLayout(consumption_inner_layout)
        summary_layout.addWidget(consumption_group)
        
        # Créer un groupe pour la productivité
        productivity_group = QGroupBox("Productivité des unités")
        productivity_inner_layout = QVBoxLayout()
        
        # Tableau de productivité
        self.productivity_table = QTableWidget()
        self.productivity_table.setColumnCount(4)
        self.productivity_table.setHorizontalHeaderLabels([
            "Unité", "Production (t)", "Heures", "Rendement (t/h)"
        ])
        self.productivity_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.productivity_table.setRowCount(3)
        
        # Lignes pour chaque unité de production
        units = ["Réception", "Concassage", "Laverie"]
        for i, unit in enumerate(units):
            unit_item = QTableWidgetItem(unit)
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.productivity_table.setItem(i, 0, unit_item)
            
            # Valeurs initiales
            for j in range(1, 4):
                value_item = QTableWidgetItem("0.00")
                value_item.setTextAlignment(Qt.AlignCenter)
                self.productivity_table.setItem(i, j, value_item)
        
        productivity_inner_layout.addWidget(self.productivity_table)
        productivity_group.setLayout(productivity_inner_layout)
        summary_layout.addWidget(productivity_group)
        
        dashboard_layout.addLayout(summary_layout)
        
        # Résumé des arrêts
        downtime_summary_layout = QHBoxLayout()
        
        # Créer un groupe pour les arrêts
        downtime_group = QGroupBox("Résumé des arrêts")
        downtime_inner_layout = QVBoxLayout()
        
        # Tableau des arrêts
        self.downtime_summary_table = QTableWidget()
        self.downtime_summary_table.setColumnCount(4)
        self.downtime_summary_table.setHorizontalHeaderLabels([
            "Unité", "Aujourd'hui (h)", "Cette semaine (h)", "Ce mois (h)"
        ])
        self.downtime_summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.downtime_summary_table.setRowCount(3)
        
        # Lignes pour chaque unité
        for i, unit in enumerate(units):
            unit_item = QTableWidgetItem(unit)
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.downtime_summary_table.setItem(i, 0, unit_item)
            
            # Valeurs initiales
            for j in range(1, 4):
                value_item = QTableWidgetItem("0.00")
                value_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_summary_table.setItem(i, j, value_item)
        
        downtime_inner_layout.addWidget(self.downtime_summary_table)
        downtime_group.setLayout(downtime_inner_layout)
        downtime_summary_layout.addWidget(downtime_group)
        
        # Créer un groupe pour les KPI énergétiques
        energy_kpi_group = QGroupBox("KPI Énergétiques")
        energy_kpi_inner_layout = QVBoxLayout()
        
        # Tableau des KPI énergétiques
        self.energy_kpi_table = QTableWidget()
        self.energy_kpi_table.setColumnCount(4)
        self.energy_kpi_table.setHorizontalHeaderLabels([
            "Indicateur", "Aujourd'hui", "Cette semaine", "Ce mois"
        ])
        self.energy_kpi_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.energy_kpi_table.setRowCount(2)
        
        # Lignes pour chaque KPI
        kpi_types = ["Énergie (kWh/tonne lavée)", "Eau (m³/tonne lavée)"]
        for i, kpi in enumerate(kpi_types):
            kpi_item = QTableWidgetItem(kpi)
            kpi_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(i, 0, kpi_item)
            
            # Valeurs initiales
            for j in range(1, 4):
                value_item = QTableWidgetItem("0.00")
                value_item.setTextAlignment(Qt.AlignCenter)
                self.energy_kpi_table.setItem(i, j, value_item)
        
        energy_kpi_inner_layout.addWidget(self.energy_kpi_table)
        energy_kpi_group.setLayout(energy_kpi_inner_layout)
        downtime_summary_layout.addWidget(energy_kpi_group)
        
        dashboard_layout.addLayout(downtime_summary_layout)
        
        dashboard_group.setLayout(dashboard_layout)
        scroll_layout.addWidget(dashboard_group)
        
        # Rapports détaillés
        reports_group = QGroupBox("Rapports détaillés")
        reports_layout = QVBoxLayout()
        
        # Onglets de rapports
        self.report_tabs = QTabWidget()
        
        # Onglet de production
        production_tab = QWidget()
        production_layout = QVBoxLayout(production_tab)
        
        self.production_table = QTableWidget()
        self.production_table.setColumnCount(6)
        self.production_table.setHorizontalHeaderLabels([
            "Date", "Étape", "Quantité (t)", "Qualité", "Heures", "Rendement (t/h)"
        ])
        self.production_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        production_layout.addWidget(self.production_table)
        
        self.report_tabs.addTab(production_tab, "Production")
        
        # Onglet des arrêts
        downtime_tab = QWidget()
        downtime_layout = QVBoxLayout(downtime_tab)
        
        self.downtime_table = QTableWidget()
        self.downtime_table.setColumnCount(5)
        self.downtime_table.setHorizontalHeaderLabels([
            "Date", "Étape", "Raison", "Durée (h)", "Description"
        ])
        self.downtime_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        downtime_layout.addWidget(self.downtime_table)
        
        self.report_tabs.addTab(downtime_tab, "Arrêts")
        
        # Onglet des mouvements de stock
        stock_tab = QWidget()
        stock_layout = QVBoxLayout(stock_tab)
        
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(5)
        self.stock_table.setHorizontalHeaderLabels([
            "Date", "Étape", "Type", "Quantité (t)", "Qualité"
        ])
        self.stock_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        stock_layout.addWidget(self.stock_table)
        
        self.report_tabs.addTab(stock_tab, "Mouvements de stock")
        
        # Onglet des consommations
        consumption_tab = QWidget()
        consumption_layout = QVBoxLayout(consumption_tab)
        
        self.consumption_table = QTableWidget()
        self.consumption_table.setColumnCount(4)
        self.consumption_table.setHorizontalHeaderLabels([
            "Date", "Ressource", "Quantité", "Unité"
        ])
        self.consumption_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        consumption_layout.addWidget(self.consumption_table)
        
        self.report_tabs.addTab(consumption_tab, "Consommations")
        
        reports_layout.addWidget(self.report_tabs)
        reports_group.setLayout(reports_layout)
        scroll_layout.addWidget(reports_group)
        
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        
        self.setLayout(main_layout)
        
        # Charger les données initiales
        self.refresh_reports()
    
    def on_period_changed(self, period):
        """Gère le changement de période."""
        custom_period = period == "Personnalisée"
        self.start_date.setEnabled(custom_period)
        self.end_date.setEnabled(custom_period)
    
    def get_date_range(self):
        """Récupère la plage de dates sélectionnée."""
        period = self.period_combo.currentText()
        
        end_date = datetime.now()
        
        if period == "Aujourd'hui":
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "Cette semaine":
            start_date = end_date - timedelta(days=end_date.weekday())
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "Ce mois":
            start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == "Cette année":
            start_date = end_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == "Personnalisée":
            start_date = self.start_date.date().toPyDate()
            end_date = self.end_date.date().toPyDate()
            end_date = datetime.combine(end_date, datetime.max.time())
        else:
            start_date = end_date - timedelta(days=30)
        
        return start_date, end_date
    
    def refresh_reports(self):
        """Rafraîchit tous les rapports."""
        try:
            start_date, end_date = self.get_date_range()
            
            # Mettre à jour les jauges de stock
            self.update_stock_gauges()
            
            # Mettre à jour les résumés
            self.update_consumption_summary()
            self.update_productivity_summary(start_date, end_date)
            self.update_downtime_summary()
            self.update_energy_kpi()
            
            # Mettre à jour les tableaux
            self.update_production_table(start_date, end_date)
            self.update_downtime_table(start_date, end_date)
            self.update_stock_table(start_date, end_date)
            self.update_consumption_table(start_date, end_date)
            
        except Exception as e:
            logging.error(f"Erreur lors du rafraîchissement des rapports: {str(e)}")
    
    def update_stock_gauges(self):
        """Met à jour les jauges de stock."""
        try:
            # Stock de réception
            reception_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.reception_gauge.set_value(reception_stock)
            
            # Stock disponible pour laverie (minerai concassé)
            available_crushing_stock = self.db_manager.get_available_crushing_stock()
            self.crushing_gauge.set_value(available_crushing_stock)
            
            # Stock de laverie
            laverie_stock = self.db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
            self.laverie_gauge.set_value(laverie_stock)
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des jauges: {str(e)}")
    
    def update_consumption_summary(self):
        """Met à jour le résumé des consommations."""
        try:
            # Dates pour les périodes
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Ressources et unités
            resources = [ResourceType.WATER, ResourceType.ENERGY, ResourceType.REACTIVE]
            units = ["m³", "kWh", "kg"]
            
            for i, resource_type in enumerate(resources):
                # Consommation d'aujourd'hui
                today_consumptions = self.db_manager.get_resource_consumptions(
                    resource_type=resource_type,
                    start_date=today
                )
                today_total = sum(c['quantity'] for c in today_consumptions)
                today_item = QTableWidgetItem(f"{today_total:.2f}")
                today_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_summary_table.setItem(i, 1, today_item)
                
                # Consommation de la semaine
                week_consumptions = self.db_manager.get_resource_consumptions(
                    resource_type=resource_type,
                    start_date=start_of_week
                )
                week_total = sum(c['quantity'] for c in week_consumptions)
                week_item = QTableWidgetItem(f"{week_total:.2f}")
                week_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_summary_table.setItem(i, 2, week_item)
                
                # Consommation du mois
                month_consumptions = self.db_manager.get_resource_consumptions(
                    resource_type=resource_type,
                    start_date=start_of_month
                )
                month_total = sum(c['quantity'] for c in month_consumptions)
                month_item = QTableWidgetItem(f"{month_total:.2f}")
                month_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_summary_table.setItem(i, 3, month_item)
                
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du résumé des consommations: {str(e)}")
            
    def update_productivity_summary(self, start_date, end_date):
        """Met à jour le résumé de la productivité des unités."""
        try:
            # Unités de production
            steps = [ProcessStepEnum.RECEPTION, ProcessStepEnum.CONCASSAGE, ProcessStepEnum.LAVERIE]
            
            for i, step in enumerate(steps):
                # Récupérer les données de production
                productions = self.db_manager.get_production_summary(
                    step=step,
                    start_date=start_date,
                    end_date=end_date
                )
                
                # Calculer la production totale
                total_quantity = sum(p['quantity'] for p in productions)
                quantity_item = QTableWidgetItem(f"{total_quantity:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.productivity_table.setItem(i, 1, quantity_item)
                
                # Calculer les heures totales
                total_hours = sum(p['production_hours'] for p in productions if p['production_hours'])
                hours_item = QTableWidgetItem(f"{total_hours:.2f}")
                hours_item.setTextAlignment(Qt.AlignCenter)
                self.productivity_table.setItem(i, 2, hours_item)
                
                # Calculer le rendement
                if total_hours > 0:
                    rendement = total_quantity / total_hours
                    rendement_item = QTableWidgetItem(f"{rendement:.2f}")
                else:
                    rendement_item = QTableWidgetItem("0.00")
                rendement_item.setTextAlignment(Qt.AlignCenter)
                self.productivity_table.setItem(i, 3, rendement_item)
                
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du résumé de productivité: {str(e)}")
            
    def update_downtime_summary(self):
        """Met à jour le résumé des arrêts."""
        try:
            # Dates pour les périodes
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Unités de production
            steps = [ProcessStepEnum.RECEPTION, ProcessStepEnum.CONCASSAGE, ProcessStepEnum.LAVERIE]
            
            for i, step in enumerate(steps):
                # Arrêts d'aujourd'hui
                today_downtimes = self.db_manager.get_downtime_summary(
                    step=step,
                    start_date=today
                )
                today_total = sum(d['duration'] for d in today_downtimes)
                today_item = QTableWidgetItem(f"{today_total:.2f}")
                today_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_summary_table.setItem(i, 1, today_item)
                
                # Arrêts de la semaine
                week_downtimes = self.db_manager.get_downtime_summary(
                    step=step,
                    start_date=start_of_week
                )
                week_total = sum(d['duration'] for d in week_downtimes)
                week_item = QTableWidgetItem(f"{week_total:.2f}")
                week_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_summary_table.setItem(i, 2, week_item)
                
                # Arrêts du mois
                month_downtimes = self.db_manager.get_downtime_summary(
                    step=step,
                    start_date=start_of_month
                )
                month_total = sum(d['duration'] for d in month_downtimes)
                month_item = QTableWidgetItem(f"{month_total:.2f}")
                month_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_summary_table.setItem(i, 3, month_item)
                
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du résumé des arrêts: {str(e)}")
            
    def update_energy_kpi(self):
        """Met à jour les KPI énergétiques."""
        try:
            # Dates pour les périodes
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Étape de laverie (pour calculer les tonnes lavées)
            laverie_step = ProcessStepEnum.LAVERIE
            
            # Calcul pour aujourd'hui
            today_energy = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.ENERGY,
                start_date=today
            )
            today_water = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.WATER,
                start_date=today
            )
            today_production = self.db_manager.get_production_summary(
                step=laverie_step,
                start_date=today
            )
            
            today_energy_total = sum(c['quantity'] for c in today_energy)
            today_water_total = sum(c['quantity'] for c in today_water)
            today_production_total = sum(p['quantity'] for p in today_production)
            
            # Calcul pour la semaine
            week_energy = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.ENERGY,
                start_date=start_of_week
            )
            week_water = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.WATER,
                start_date=start_of_week
            )
            week_production = self.db_manager.get_production_summary(
                step=laverie_step,
                start_date=start_of_week
            )
            
            week_energy_total = sum(c['quantity'] for c in week_energy)
            week_water_total = sum(c['quantity'] for c in week_water)
            week_production_total = sum(p['quantity'] for p in week_production)
            
            # Calcul pour le mois
            month_energy = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.ENERGY,
                start_date=start_of_month
            )
            month_water = self.db_manager.get_resource_consumptions(
                resource_type=ResourceType.WATER,
                start_date=start_of_month
            )
            month_production = self.db_manager.get_production_summary(
                step=laverie_step,
                start_date=start_of_month
            )
            
            month_energy_total = sum(c['quantity'] for c in month_energy)
            month_water_total = sum(c['quantity'] for c in month_water)
            month_production_total = sum(p['quantity'] for p in month_production)
            
            # Calcul des KPI énergétiques (kWh/tonne)
            if today_production_total > 0:
                today_energy_kpi = today_energy_total / today_production_total
            else:
                today_energy_kpi = 0
                
            if week_production_total > 0:
                week_energy_kpi = week_energy_total / week_production_total
            else:
                week_energy_kpi = 0
                
            if month_production_total > 0:
                month_energy_kpi = month_energy_total / month_production_total
            else:
                month_energy_kpi = 0
            
            # Calcul des KPI d'eau (m³/tonne)
            if today_production_total > 0:
                today_water_kpi = today_water_total / today_production_total
            else:
                today_water_kpi = 0
                
            if week_production_total > 0:
                week_water_kpi = week_water_total / week_production_total
            else:
                week_water_kpi = 0
                
            if month_production_total > 0:
                month_water_kpi = month_water_total / month_production_total
            else:
                month_water_kpi = 0
            
            # Mise à jour du tableau des KPI énergétiques
            # Ligne 0: Énergie (kWh/tonne)
            today_energy_item = QTableWidgetItem(f"{today_energy_kpi:.2f}")
            today_energy_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(0, 1, today_energy_item)
            
            week_energy_item = QTableWidgetItem(f"{week_energy_kpi:.2f}")
            week_energy_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(0, 2, week_energy_item)
            
            month_energy_item = QTableWidgetItem(f"{month_energy_kpi:.2f}")
            month_energy_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(0, 3, month_energy_item)
            
            # Ligne 1: Eau (m³/tonne)
            today_water_item = QTableWidgetItem(f"{today_water_kpi:.2f}")
            today_water_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(1, 1, today_water_item)
            
            week_water_item = QTableWidgetItem(f"{week_water_kpi:.2f}")
            week_water_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(1, 2, week_water_item)
            
            month_water_item = QTableWidgetItem(f"{month_water_kpi:.2f}")
            month_water_item.setTextAlignment(Qt.AlignCenter)
            self.energy_kpi_table.setItem(1, 3, month_water_item)
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des KPI énergétiques: {str(e)}")
    
    def update_consumption_table(self, start_date, end_date):
        """Met à jour le tableau des consommations."""
        try:
            # Récupérer les données de consommation
            consumptions = self.db_manager.get_resource_consumptions(
                start_date=start_date,
                end_date=end_date
            )
            
            # Effacer le tableau
            self.consumption_table.setRowCount(0)
            
            # Définir les unités pour chaque type de ressource
            unit_map = {
                ResourceType.WATER.value: "m³",
                ResourceType.ENERGY.value: "kWh",
                ResourceType.REACTIVE.value: "kg"
            }
            
            # Remplir le tableau
            for consumption in consumptions:
                row_position = self.consumption_table.rowCount()
                self.consumption_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(consumption['consumption_date'].strftime("%d/%m/%Y"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 0, date_item)
                
                # Ressource
                resource_item = QTableWidgetItem(consumption['resource_type'])
                resource_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 1, resource_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{consumption['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 2, quantity_item)
                
                # Unité
                unit_item = QTableWidgetItem(unit_map.get(consumption['resource_type'], ""))
                unit_item.setTextAlignment(Qt.AlignCenter)
                self.consumption_table.setItem(row_position, 3, unit_item)
                
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du tableau des consommations: {str(e)}")
    
    def update_production_table(self, start_date, end_date):
        """Met à jour le tableau des productions."""
        try:
            # Récupérer les données de production
            productions = self.db_manager.get_production_summary(
                start_date=start_date,
                end_date=end_date
            )
            
            # Effacer le tableau
            self.production_table.setRowCount(0)
            
            # Remplir le tableau
            for production in productions:
                row_position = self.production_table.rowCount()
                self.production_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(production['production_date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 0, date_item)
                
                # Étape
                step_item = QTableWidgetItem(production['step'])
                step_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 1, step_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{production['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 2, quantity_item)
                
                # Qualité
                quality_item = QTableWidgetItem(production['quality'] if production['quality'] else "-")
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 3, quality_item)
                
                # Heures
                hours_item = QTableWidgetItem(f"{production['production_hours']:.2f}" if production['production_hours'] else "-")
                hours_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 4, hours_item)
                
                # Rendement
                if production['production_hours'] and production['production_hours'] > 0:
                    rendement = production['quantity'] / production['production_hours']
                    rendement_item = QTableWidgetItem(f"{rendement:.2f}")
                else:
                    rendement_item = QTableWidgetItem("-")
                rendement_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 5, rendement_item)
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du tableau des productions: {str(e)}")
    
    def update_downtime_table(self, start_date, end_date):
        """Met à jour le tableau des arrêts."""
        try:
            # Récupérer les données d'arrêt
            downtimes = self.db_manager.get_downtime_summary(
                start_date=start_date,
                end_date=end_date
            )
            
            # Effacer le tableau
            self.downtime_table.setRowCount(0)
            
            # Remplir le tableau
            for downtime in downtimes:
                row_position = self.downtime_table.rowCount()
                self.downtime_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(downtime['downtime_date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 0, date_item)
                
                # Étape
                step_item = QTableWidgetItem(downtime['step'])
                step_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 1, step_item)
                
                # Raison
                reason_item = QTableWidgetItem(downtime['reason'])
                reason_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 2, reason_item)
                
                # Durée
                duration_item = QTableWidgetItem(f"{downtime['duration']:.2f}")
                duration_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 3, duration_item)
                
                # Description
                description_item = QTableWidgetItem(downtime['description'] if downtime['description'] else "-")
                description_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 4, description_item)
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du tableau des arrêts: {str(e)}")
    
    def update_stock_table(self, start_date, end_date):
        """Met à jour le tableau des mouvements de stock."""
        try:
            # Récupérer les données de mouvement de stock
            movements = self.db_manager.get_stock_movements(
                start_date=start_date,
                end_date=end_date
            )
            
            # Effacer le tableau
            self.stock_table.setRowCount(0)
            
            # Remplir le tableau
            for movement in movements:
                row_position = self.stock_table.rowCount()
                self.stock_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(movement['date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.stock_table.setItem(row_position, 0, date_item)
                
                # Étape
                step_item = QTableWidgetItem(movement['step'])
                step_item.setTextAlignment(Qt.AlignCenter)
                self.stock_table.setItem(row_position, 1, step_item)
                
                # Type
                type_item = QTableWidgetItem(movement['movement_type'])
                type_item.setTextAlignment(Qt.AlignCenter)
                self.stock_table.setItem(row_position, 2, type_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{movement['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.stock_table.setItem(row_position, 3, quantity_item)
                
                # Qualité
                quality_item = QTableWidgetItem(f"{movement['quality']:.2f}" if movement['quality'] else "-")
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.stock_table.setItem(row_position, 4, quality_item)
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du tableau des mouvements de stock: {str(e)}")
    
    def export_reports(self):
        """Exporte les rapports au format Excel."""
        # TODO: Implémenter l'exportation des rapports
        QMessageBox.information(self, "Exportation", "Fonctionnalité d'exportation Excel en cours de développement.")
        
    def generate_pdf_report(self):
        """Génère un rapport PDF de production journalier."""
        try:
            # Récupérer la date sélectionnée
            if self.period_combo.currentText() == "Personnalisée":
                selected_date = self.start_date.date().toPyDate()
            else:
                selected_date = datetime.now()
            
            # Générer le rapport PDF
            pdf_path = self.report_service.generate_daily_production_pdf(date=selected_date)
            
            # Afficher un message de confirmation
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("Rapport PDF généré")
            msg_box.setText(f"Le rapport PDF a été généré avec succès :\n{pdf_path}")
            
            # Ajouter des boutons pour ouvrir le fichier ou le dossier
            open_file_button = msg_box.addButton("Ouvrir le fichier", QMessageBox.ActionRole)
            open_folder_button = msg_box.addButton("Ouvrir le dossier", QMessageBox.ActionRole)
            close_button = msg_box.addButton("Fermer", QMessageBox.RejectRole)
            
            msg_box.exec_()
            
            # Gérer les actions des boutons
            if msg_box.clickedButton() == open_file_button:
                # Ouvrir le fichier PDF avec l'application par défaut
                os.startfile(pdf_path)
            elif msg_box.clickedButton() == open_folder_button:
                # Ouvrir le dossier contenant le fichier
                os.startfile(os.path.dirname(pdf_path))
                
        except Exception as e:
            logging.error(f"Erreur lors de la génération du rapport PDF: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération du rapport PDF: {str(e)}")
