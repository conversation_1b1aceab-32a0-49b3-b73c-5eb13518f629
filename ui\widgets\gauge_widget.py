"""
Widget de jauge pour afficher les niveaux de stock ou autres métriques.
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtGui import QPainter, QColor, QFont, QPen, QBrush
from PyQt5.QtCore import Qt, QRectF, QPointF, pyqtProperty
import math

class GaugeWidget(QWidget):
    """Widget de jauge circulaire pour afficher une valeur en pourcentage."""
    
    def __init__(self, parent=None, min_value=0, max_value=100, value=0, title="", unit=""):
        """Initialise le widget de jauge."""
        super().__init__(parent)
        self._min_value = min_value
        self._max_value = max_value
        self._value = value
        self._title = title
        self._unit = unit
        
        # Couleurs
        self._background_color = QColor("#F5F5F5")
        self._track_color = QColor("#E0E0E0")
        self._progress_color = QColor("#1976D2")  # Bleu par défaut
        self._text_color = QColor("#212121")
        
        # Configuration
        self._start_angle = 135
        self._span_angle = 270
        self._line_width = 15
        
        # Seuils pour les couleurs
        self._low_threshold = 25
        self._medium_threshold = 75
        
        # Mise en page
        self._layout = QVBoxLayout(self)
        self._title_label = QLabel(self._title)
        self._title_label.setAlignment(Qt.AlignCenter)
        self._layout.addWidget(self._title_label)
        
        # Taille minimale
        self.setMinimumSize(150, 150)
        
        # Mise à jour des couleurs
        self._update_colors()
    
    def _update_colors(self):
        """Met à jour les couleurs en fonction de la valeur."""
        percentage = self.get_percentage()
        
        if percentage < self._low_threshold:
            self._progress_color = QColor("#D32F2F")  # Rouge pour niveau bas
        elif percentage < self._medium_threshold:
            self._progress_color = QColor("#F57C00")  # Orange pour niveau moyen
        else:
            self._progress_color = QColor("#388E3C")  # Vert pour niveau élevé
    
    def get_percentage(self):
        """Calcule le pourcentage de la valeur actuelle."""
        range_value = self._max_value - self._min_value
        if range_value == 0:
            return 0
        return ((self._value - self._min_value) / range_value) * 100
    
    def set_value(self, value):
        """Définit la valeur actuelle."""
        self._value = max(self._min_value, min(self._max_value, value))
        self._update_colors()
        self.update()
    
    def set_range(self, min_value, max_value):
        """Définit la plage de valeurs."""
        self._min_value = min_value
        self._max_value = max_value
        self._value = max(self._min_value, min(self._max_value, self._value))
        self._update_colors()
        self.update()
    
    def set_title(self, title):
        """Définit le titre de la jauge."""
        self._title = title
        self._title_label.setText(title)
    
    def set_unit(self, unit):
        """Définit l'unité de mesure."""
        self._unit = unit
        self.update()
    
    def set_thresholds(self, low, medium):
        """Définit les seuils pour les changements de couleur."""
        self._low_threshold = low
        self._medium_threshold = medium
        self._update_colors()
        self.update()
    
    def paintEvent(self, event):
        """Dessine la jauge."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Calculer le rectangle pour la jauge
        rect = self.rect()
        side = min(rect.width(), rect.height())
        margin = self._line_width + 10
        gauge_rect = QRectF(
            rect.x() + (rect.width() - side) / 2 + margin,
            rect.y() + (rect.height() - side) / 2 + margin,
            side - 2 * margin,
            side - 2 * margin
        )
        
        # Dessiner le fond
        painter.fillRect(rect, self._background_color)
        
        # Dessiner la piste
        painter.setPen(QPen(self._track_color, self._line_width, Qt.SolidLine))
        painter.drawArc(gauge_rect, int(self._start_angle * 16), int(self._span_angle * 16))
        
        # Dessiner la progression
        percentage = self.get_percentage()
        span = (percentage / 100) * self._span_angle
        painter.setPen(QPen(self._progress_color, self._line_width, Qt.SolidLine))
        painter.drawArc(gauge_rect, int(self._start_angle * 16), int(span * 16))
        
        # Dessiner la valeur
        painter.setPen(self._text_color)
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        painter.setFont(font)
        
        value_text = f"{self._value:.1f} {self._unit}"
        painter.drawText(gauge_rect, Qt.AlignCenter, value_text)
        
        # Dessiner les graduations
        self._draw_ticks(painter, gauge_rect)
    
    def _draw_ticks(self, painter, rect):
        """Dessine les graduations sur la jauge."""
        center = rect.center()
        outer_radius = rect.width() / 2
        inner_radius = outer_radius - self._line_width - 5
        
        painter.setPen(QPen(self._text_color, 1, Qt.SolidLine))
        
        # Dessiner les graduations principales
        for i in range(0, 101, 25):
            angle = self._start_angle + (i / 100) * self._span_angle
            rad_angle = math.radians(angle)
            
            # Coordonnées du point extérieur
            x1 = center.x() + inner_radius * math.cos(rad_angle)
            y1 = center.y() - inner_radius * math.sin(rad_angle)
            
            # Coordonnées du point intérieur
            x2 = center.x() + (inner_radius - 10) * math.cos(rad_angle)
            y2 = center.y() - (inner_radius - 10) * math.sin(rad_angle)
            
            painter.drawLine(QPointF(x1, y1), QPointF(x2, y2))
            
            # Dessiner les valeurs
            if i % 25 == 0:
                value = self._min_value + (i / 100) * (self._max_value - self._min_value)
                text = f"{value:.0f}"
                
                # Position du texte
                text_x = center.x() + (inner_radius - 20) * math.cos(rad_angle) - 10
                text_y = center.y() - (inner_radius - 20) * math.sin(rad_angle) + 5
                
                painter.drawText(QPointF(text_x, text_y), text)
    
    # Propriétés pour l'accès via Qt Designer
    value = pyqtProperty(float, lambda self: self._value, set_value)
    min_value = pyqtProperty(float, lambda self: self._min_value, lambda self, v: self.set_range(v, self._max_value))
    max_value = pyqtProperty(float, lambda self: self._max_value, lambda self, v: self.set_range(self._min_value, v))
    title = pyqtProperty(str, lambda self: self._title, set_title)
    unit = pyqtProperty(str, lambda self: self._unit, set_unit)