# Standard library imports
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

# Third-party imports
from PyQt5.QtCore import Qt, QDateTime, QTimer, QSize
from PyQt5.QtGui import QPalette, QColor, QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QGroupBox,
                             QMessageBox, QDateTimeEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QFileDialog, QTabWidget, QGridLayout, QSplitter,
                             QToolButton, QFrame)
from sqlalchemy import func

# Local imports
from models.database_models import ProcessStep, StockMovement
from models.enums import ProcessStepEnum, StockMovementType

class CollapsibleSection(QWidget):
    """Widget pour une section réductible/extensible."""
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.content = QWidget()
        self.content_layout = QVBoxLayout(self.content)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        self.init_ui()
        
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # En-tête avec bouton d'expansion/réduction
        header = QWidget()
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(5, 5, 5, 5)
        
        self.toggle_button = QToolButton()
        self.toggle_button.setArrowType(Qt.DownArrow)
        self.toggle_button.setCheckable(True)
        self.toggle_button.setChecked(True)
        self.toggle_button.clicked.connect(self.toggle_content)
        header_layout.addWidget(self.toggle_button)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        main_layout.addWidget(header)
        main_layout.addWidget(separator)
        main_layout.addWidget(self.content)
        
    def toggle_content(self):
        self.content.setVisible(self.toggle_button.isChecked())
        self.toggle_button.setArrowType(Qt.DownArrow if self.toggle_button.isChecked() else Qt.RightArrow)
        
    def add_widget(self, widget):
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        self.content_layout.addLayout(layout)

class IndicatorWidget(QFrame):
    """Widget pour afficher un indicateur avec titre et valeur."""
    
    def __init__(self, title, value="--", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        title_label = QLabel(self.title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; color: #555;")
        
        self.value_label = QLabel(self.value)
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d7;")
        
        layout.addWidget(title_label)
        layout.addWidget(self.value_label)
        
    def set_value(self, value):
        self.value = value
        self.value_label.setText(value)

class ReceptionTab(QWidget):
    """Onglet de gestion de la réception."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        
        # Initialiser les widgets
        self.reception_date = QDateTimeEdit()
        self.reception_date.setDateTime(QDateTime.currentDateTime())
        
        self.quantity_received_input = QLineEdit()
        self.quality_input = QLineEdit()
        self.stock_indicator = None
        self.today_indicator = None
        self.reception_table = QTableWidget()
        
        self.init_ui()
        self.setup_timers()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Indicateurs en haut
        indicators_group = QGroupBox("État des stocks")
        indicators_layout = QGridLayout(indicators_group)
        
        self.total_reception_indicator = IndicatorWidget("Total Réception")
        self.current_stock_indicator = IndicatorWidget("Stock Actuel")
        
        indicators_layout.addWidget(self.total_reception_indicator, 0, 0)
        indicators_layout.addWidget(self.current_stock_indicator, 0, 1)
        
        main_layout.addWidget(indicators_group)
        
        # Onglets pour Saisie et Historique
        tabs = QTabWidget()
        
        # Onglet Saisie
        input_tab = QWidget()
        input_layout = QVBoxLayout(input_tab)
        
        # Bouton d'importation Excel en haut
        buttons_layout = QHBoxLayout()
        import_button = QPushButton("Importer depuis Excel")
        import_button.clicked.connect(self.import_from_excel)
        import_button.setStyleSheet("QPushButton { padding: 5px; }")
        buttons_layout.addWidget(import_button)
        buttons_layout.addStretch()
        input_layout.addLayout(buttons_layout)
        
        # Formulaire de saisie des données de réception
        input_group = QGroupBox("Saisie des données de réception")
        form_layout = QFormLayout(input_group)
        form_layout.setSpacing(10)
        
        form_layout.addRow("Date de réception:", self.reception_date)
        
        self.quantity_received_input = QLineEdit()
        self.quantity_received_input.setPlaceholderText("Quantité reçue (tonnes)")
        form_layout.addRow("Quantité reçue:", self.quantity_received_input)
        
        self.quality_input = QLineEdit()
        self.quality_input.setPlaceholderText("Qualité P2O5 (%)")
        form_layout.addRow("Qualité P2O5 (%):", self.quality_input)
        
        add_button = QPushButton("Ajouter réception")
        add_button.clicked.connect(self.add_reception)
        form_layout.addRow("", add_button)
        
        input_layout.addWidget(input_group)
        input_layout.addStretch()
        
        # Onglet Historique
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Table des réceptions
        reception_table_section = CollapsibleSection("Historique des réceptions")
        self.reception_table = QTableWidget()
        self.reception_table.setColumnCount(4)
        self.reception_table.setHorizontalHeaderLabels(["Date", "Quantité (t)", "Qualité (%)", "Action"])
        self.reception_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.reception_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.reception_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        reception_table_section.add_widget(self.reception_table)
        
        history_layout.addWidget(reception_table_section)
        history_layout.addStretch()
        
        # Ajouter les onglets
        tabs.addTab(input_tab, "Saisie")
        tabs.addTab(history_tab, "Historique")
        
        main_layout.addWidget(tabs)
        
        self.setLayout(main_layout)
    
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour rafraîchir le stock toutes les 30 secondes
        self.stock_timer = QTimer()
        self.stock_timer.timeout.connect(self.update_available_stock)
        self.stock_timer.start(30000)  # 30 secondes
        
        # Timer pour la mise à jour automatique
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_tab)
        self.update_timer.start(60000)  # 1 minute
    
    def add_reception(self):
        """Ajoute une nouvelle réception."""
        try:
            # Récupérer et valider les valeurs
            date = self.reception_date.dateTime().toPyDateTime()
            quantity = self._get_float_value(self.quantity_received_input, "quantité reçue")
            quality = self._get_float_value(self.quality_input, "qualité")
            
            # Ajouter la réception via la méthode dédiée
            self.db_manager.add_reception(
                quantity=quantity,
                quality=quality,
                reception_date=date
            )
            
            QMessageBox.information(self, "Succès", "Réception ajoutée avec succès.")
            self.clear_inputs()
            self.refresh_tab()
            
        except ValueError as e:
            QMessageBox.warning(self, "Erreur de saisie", str(e))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la réception: {str(e)}")
    
    def _get_float_value(self, input_widget: QLineEdit, field_name: str) -> float:
        """Récupère et valide une valeur flottante depuis un widget de saisie."""
        value_str = input_widget.text().strip()
        if not value_str:
            raise ValueError(f"Le champ '{field_name}' est obligatoire")
        try:
            value = float(value_str)
            if value <= 0:
                raise ValueError(f"La valeur de '{field_name}' doit être positive")
            return value
        except ValueError:
            raise ValueError(f"La valeur de '{field_name}' doit être un nombre valide")
    
    def clear_inputs(self):
        """Efface les champs de saisie."""
        self.reception_date.setDateTime(QDateTime.currentDateTime())
        self.quantity_received_input.clear()
        self.quality_input.clear()
    
    def refresh_tab(self):
        """Rafraîchit toutes les données de l'onglet."""
        self.load_reception_data()
        self.update_indicators()
    
    def load_reception_data(self):
        """Charge les données de réception depuis la base de données."""
        try:
            self.reception_table.setRowCount(0)
            
            # Récupérer les mouvements de type RECEPTION avec source = Réception
            session = self.db_manager.Session()
            try:
                reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
                
                if reception_step:
                    # Requête pour obtenir tous les mouvements de type RECEPTION avec source = Réception
                    movements_query = session.query(StockMovement).filter(
                        StockMovement.movement_type == StockMovementType.RECEPTION,
                        StockMovement.source_step_id == reception_step.id
                    ).order_by(StockMovement.movement_date.desc())
                    
                    movements = movements_query.all()
                    
                    for movement in movements:
                        row_position = self.reception_table.rowCount()
                        self.reception_table.insertRow(row_position)
                        
                        # Date
                        date_item = QTableWidgetItem(movement.movement_date.strftime("%d/%m/%Y %H:%M"))
                        date_item.setTextAlignment(Qt.AlignCenter)
                        self.reception_table.setItem(row_position, 0, date_item)
                        
                        # Quantité
                        quantity_item = QTableWidgetItem(f"{movement.quantity:.2f}")
                        quantity_item.setTextAlignment(Qt.AlignCenter)
                        self.reception_table.setItem(row_position, 1, quantity_item)
                        
                        # Qualité
                        # Le champ quality n'existe pas dans StockMovement
                        quality_item = QTableWidgetItem("N/A")
                        quality_item.setTextAlignment(Qt.AlignCenter)
                        self.reception_table.setItem(row_position, 2, quality_item)
                        
                        # Bouton de suppression
                        delete_btn = QPushButton("Supprimer")
                        delete_btn.clicked.connect(lambda checked, row=row_position, id=movement.id: self.delete_reception_row(row, id))
                        self.reception_table.setCellWidget(row_position, 3, delete_btn)
                else:
                    logging.error("Étape Réception non trouvée")
            finally:
                session.close()
                
        except Exception as e:
            logging.error(f"Erreur lors du chargement des données de réception: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
    
    def update_indicators(self):
        """Met à jour les indicateurs."""
        try:
            # Mettre à jour le stock actuel de réception
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.current_stock_indicator.set_value(f"{current_stock:.1f} tonnes")
            
            # Colorer l'indicateur en fonction de la valeur
            if current_stock > 10000:
                self.current_stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #107c10;")
            elif current_stock > 5000:
                self.current_stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d7;")
            else:
                self.current_stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")
            
            # Calculer le total de toutes les réceptions
            session = self.db_manager.Session()
            try:
                reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
                
                if reception_step:
                    # Requête pour obtenir tous les mouvements de type RECEPTION avec source = Réception
                    movements_query = session.query(StockMovement).filter(
                        StockMovement.movement_type == StockMovementType.RECEPTION,
                        StockMovement.source_step_id == reception_step.id
                    )
                    
                    movements = movements_query.all()
                    total_reception = sum(m.quantity for m in movements)
                    self.total_reception_indicator.set_value(f"{total_reception:.1f} tonnes")
                    
                    # Colorer l'indicateur en fonction de la valeur
                    if total_reception > 10000:
                        self.total_reception_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #107c10;")
                    elif total_reception > 5000:
                        self.total_reception_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d7;")
                    else:
                        self.total_reception_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")
                else:
                    self.total_reception_indicator.set_value("Erreur")
            finally:
                session.close()
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des indicateurs: {str(e)}", exc_info=True)
            self.current_stock_indicator.set_value("Erreur")
            self.total_reception_indicator.set_value("Erreur")
    
    def delete_reception_row(self, row_idx: int, movement_id: int = None):
        """Supprime une ligne de réception."""
        try:
            date_item = self.reception_table.item(row_idx, 0)
            if not date_item:
                return
            
            date_str = date_item.text()
            reply = QMessageBox.question(
                self, 'Confirmation',
                f"Voulez-vous vraiment supprimer la réception du {date_str} ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if movement_id:
                    # Supprimer la réception directement avec l'ID
                    self.db_manager.delete_reception(movement_id)
                    self.refresh_tab()
                    QMessageBox.information(self, "Succès", "Réception supprimée avec succès.")
                else:
                    # Récupérer les mouvements de stock pour cette date
                    session = self.db_manager.Session()
                    try:
                        reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
                        
                        if reception_step:
                            # Convertir la date_str en objet datetime
                            try:
                                date_obj = datetime.strptime(date_str, "%d/%m/%Y %H:%M")
                                
                                # Trouver le mouvement correspondant à cette date
                                movement = session.query(StockMovement).filter(
                                    StockMovement.movement_type == StockMovementType.RECEPTION,
                                    StockMovement.source_step_id == reception_step.id,
                                    func.strftime("%d/%m/%Y %H:%M", StockMovement.movement_date) == date_str
                                ).first()
                                
                                if movement:
                                    # Supprimer la réception
                                    self.db_manager.delete_reception(movement.id)
                                    self.refresh_tab()
                                    QMessageBox.information(self, "Succès", "Réception supprimée avec succès.")
                                else:
                                    QMessageBox.warning(self, "Avertissement", "Réception non trouvée dans la base de données.")
                            except ValueError:
                                QMessageBox.warning(self, "Avertissement", "Format de date invalide.")
                        else:
                            QMessageBox.warning(self, "Avertissement", "Étape Réception non trouvée.")
                    finally:
                        session.close()
                
        except Exception as e:
            logging.error(f"Erreur lors de la suppression de la réception: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")
    
    def update_available_stock(self):
        """Met à jour l'affichage du stock disponible."""
        try:
            # Mettre à jour le stock disponible au concassage (car les réceptions vont directement au concassage)
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.stock_indicator.set_value(f"{current_stock:.1f} tonnes")
            
            # Colorer l'indicateur en fonction de la valeur
            if current_stock > 10000:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #107c10;")
            elif current_stock > 5000:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d7;")
            else:
                self.stock_indicator.value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du stock: {str(e)}", exc_info=True)
            self.stock_indicator.set_value("Erreur")
    
    def import_from_excel(self):
        """Importe les données de réception depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Sélectionner le fichier Excel",
                str(self.db_manager.engine.url).replace('sqlite:///', '').replace('production.db', 'templates'),  # Dossier templates par défaut
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Confirmer l'importation
            reply = QMessageBox.question(
                self,
                'Confirmation',
                'Voulez-vous importer les données de ce fichier Excel ?\n\n' +
                'Assurez-vous que le fichier contient les colonnes suivantes:\n' +
                '- Date (format: JJ/MM/AAAA HH:mm)\n' +
                '- Quantité (en tonnes)\n' +
                '- Qualité (pourcentage)',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    # Importer les données
                    imported_dates = self.db_manager.import_receptions_from_excel(file_path)
                    
                    # Mettre à jour l'affichage
                    self.refresh_tab()
                    self.update_indicators()
                    
                    QMessageBox.information(
                        self,
                        "Succès",
                        f"{len(imported_dates)} réceptions ont été importées avec succès."
                    )
                    
                except ValueError as e:  # Erreur de validation
                    QMessageBox.warning(
                        self,
                        "Erreur de validation",
                        str(e)
                    )
                except Exception as e:  # Autres erreurs
                    QMessageBox.critical(
                        self,
                        "Erreur",
                        f"Une erreur est survenue lors de l'importation:\n{str(e)}"
                    )
                    logging.error(f"Erreur lors de l'importation Excel: {str(e)}", exc_info=True)
                    
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur inattendue est survenue:\n{str(e)}"
            )
            logging.error(f"Erreur inattendue lors de l'importation Excel: {str(e)}", exc_info=True)