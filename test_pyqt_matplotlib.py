import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

class MplCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        print("Creating figure...")
        fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = fig.add_subplot(111)
        super(MplCanvas, self).__init__(fig)
        print("Canvas created")

class MainWindow(QMainWindow):
    def __init__(self):
        print("Initializing main window...")
        super().__init__()
        
        # Create the main widget
        print("Creating central widget...")
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        print("Creating layout...")
        layout = QVBoxLayout(central_widget)
        
        # Create canvas
        print("Creating canvas...")
        self.canvas = MplCanvas(self, width=5, height=4, dpi=100)
        layout.addWidget(self.canvas)
        
        # Plot data
        print("Plotting data...")
        self.plot()
        
        # Set window properties
        self.setWindowTitle("PyQt5 with Matplotlib")
        self.setGeometry(100, 100, 800, 600)
        print("Window initialized")
    
    def plot(self):
        print("Creating plot data...")
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        print("Drawing plot...")
        self.canvas.axes.plot(x, y)
        self.canvas.axes.set_title('Sine Wave')
        self.canvas.axes.set_xlabel('x')
        self.canvas.axes.set_ylabel('sin(x)')
        self.canvas.draw()
        print("Plot completed")

def main():
    try:
        print("Creating application...")
        app = QApplication(sys.argv)
        print("Application created")
        
        print("Creating main window...")
        window = MainWindow()
        print("Main window created")
        
        print("Showing window...")
        window.show()
        print("Window shown")
        
        print("Starting event loop...")
        return app.exec_()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("Starting PyQt5 with Matplotlib test...")
    try:
        exit_code = main()
        print(f"Application exited with code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        print(f"Unhandled exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)