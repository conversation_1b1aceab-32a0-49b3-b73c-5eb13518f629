"""
Migration script to add source_step_id column to stock_movements table.
"""

from alembic import op
import sqlalchemy as sa

# Revision identifiers, used by Alembic.
revision = 'add_source_step_id_to_stock_movements'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """Add source_step_id column to stock_movements table."""
    op.add_column('stock_movements', sa.Column('source_step_id', sa.Integer(), nullable=True))

def downgrade():
    """Remove source_step_id column from stock_movements table."""
    op.drop_column('stock_movements', 'source_step_id')
