"""
Widget de tableau de bord personnalisable.
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QMenu, QAction, QDialog,
                             QComboBox, QFormLayout, QLineEdit, QSpinBox,
                             QMessageBox, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QMimeData, QByteArray
from PyQt5.QtGui import QIcon, QDrag, QPixmap, QPainter, QColor
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable

from config.dashboard_config import DashboardConfig
from ui.responsive_layout import ResponsiveWidget

class WidgetHeader(QWidget):
    """En-tête d'un widget de tableau de bord."""
    
    # Signaux
    close_clicked = pyqtSignal()
    settings_clicked = pyqtSignal()
    move_started = pyqtSignal(QWidget)
    
    def __init__(self, title: str, parent: Optional[QWidget] = None):
        """
        Initialise l'en-tête du widget.
        
        Args:
            title: Titre du widget
            parent: Widget parent
        """
        super().__init__(parent)
        self.title = title
        self.draggable = True
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Titre
        self.title_label = QLabel(self.title)
        self.title_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.title_label)
        
        # Espace flexible
        layout.addStretch()
        
        # Bouton de paramètres
        self.settings_button = QPushButton()
        self.settings_button.setIcon(QIcon("resources/icons/settings.png"))
        self.settings_button.setFlat(True)
        self.settings_button.setFixedSize(24, 24)
        self.settings_button.clicked.connect(self.settings_clicked)
        layout.addWidget(self.settings_button)
        
        # Bouton de fermeture
        self.close_button = QPushButton()
        self.close_button.setIcon(QIcon("resources/icons/close.png"))
        self.close_button.setFlat(True)
        self.close_button.setFixedSize(24, 24)
        self.close_button.clicked.connect(self.close_clicked)
        layout.addWidget(self.close_button)
        
        # Styliser l'en-tête
        self.setAutoFillBackground(True)
        self.setStyleSheet("""
            WidgetHeader {
                background-color: #f0f0f0;
                border-bottom: 1px solid #cccccc;
            }
        """)
    
    def set_title(self, title: str) -> None:
        """
        Définit le titre du widget.
        
        Args:
            title: Nouveau titre
        """
        self.title = title
        self.title_label.setText(title)
    
    def set_draggable(self, draggable: bool) -> None:
        """
        Définit si l'en-tête est déplaçable.
        
        Args:
            draggable: True pour permettre le déplacement, False sinon
        """
        self.draggable = draggable
    
    def mousePressEvent(self, event) -> None:
        """
        Gère l'événement de clic de souris.
        
        Args:
            event: Événement de clic de souris
        """
        if event.button() == Qt.LeftButton and self.draggable:
            self.drag_start_position = event.pos()
    
    def mouseMoveEvent(self, event) -> None:
        """
        Gère l'événement de déplacement de souris.
        
        Args:
            event: Événement de déplacement de souris
        """
        if not (event.buttons() & Qt.LeftButton) or not self.draggable:
            return
        
        # Vérifier si le déplacement est suffisant pour commencer un drag
        if (event.pos() - self.drag_start_position).manhattanLength() < QApplication.startDragDistance():
            return
        
        # Émettre le signal de début de déplacement
        self.move_started.emit(self.parent())

class DashboardWidget(QFrame):
    """Widget de tableau de bord personnalisable."""
    
    # Signaux
    closed = pyqtSignal(QWidget)
    moved = pyqtSignal(QWidget, int, int)
    resized = pyqtSignal(QWidget, QSize)
    
    def __init__(self, widget_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        Initialise le widget de tableau de bord.
        
        Args:
            widget_data: Données du widget
            parent: Widget parent
        """
        super().__init__(parent)
        self.widget_data = widget_data
        self.content_widget = None
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        self.header = WidgetHeader(self.widget_data.get("title", "Sans titre"), self)
        self.header.close_clicked.connect(self.on_close)
        self.header.settings_clicked.connect(self.on_settings)
        self.header.move_started.connect(self.on_move_started)
        layout.addWidget(self.header)
        
        # Contenu
        self.content_container = QWidget()
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        
        # Créer le widget de contenu en fonction du type
        self.create_content_widget()
        
        layout.addWidget(self.content_container)
        
        # Styliser le widget
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        self.setStyleSheet("""
            DashboardWidget {
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
        """)
        
        # Définir la politique de taille
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(200, 150)
    
    def create_content_widget(self) -> None:
        """Crée le widget de contenu en fonction du type."""
        widget_type = self.widget_data.get("type", "unknown")
        data_source = self.widget_data.get("data_source", "")
        
        if widget_type == "gauge":
            from ui.widgets.gauge_widget import GaugeWidget
            self.content_widget = GaugeWidget(
                title=self.widget_data.get("title", ""),
                unit=self.widget_data.get("unit", "")
            )
            self.content_widget.set_range(0, 100)
            self.content_widget.set_value(50)
        elif widget_type == "table":
            from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
            self.content_widget = QTableWidget()
            self.content_widget.setColumnCount(4)
            self.content_widget.setRowCount(3)
            self.content_widget.setHorizontalHeaderLabels(["Col 1", "Col 2", "Col 3", "Col 4"])
            self.content_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            
            # Remplir avec des données d'exemple
            for i in range(3):
                for j in range(4):
                    item = QTableWidgetItem(f"Item {i+1},{j+1}")
                    item.setTextAlignment(Qt.AlignCenter)
                    self.content_widget.setItem(i, j, item)
        elif widget_type == "chart":
            # TODO: Implémenter les graphiques
            self.content_widget = QLabel("Graphique (non implémenté)")
            self.content_widget.setAlignment(Qt.AlignCenter)
        else:
            self.content_widget = QLabel(f"Type de widget inconnu: {widget_type}")
            self.content_widget.setAlignment(Qt.AlignCenter)
        
        if self.content_widget:
            self.content_layout.addWidget(self.content_widget)
    
    def update_data(self, data: Any) -> None:
        """
        Met à jour les données du widget.
        
        Args:
            data: Nouvelles données
        """
        widget_type = self.widget_data.get("type", "unknown")
        
        if widget_type == "gauge" and hasattr(self.content_widget, "set_value"):
            self.content_widget.set_value(data)
        elif widget_type == "table" and isinstance(self.content_widget, QTableWidget):
            # TODO: Implémenter la mise à jour des tableaux
            pass
        elif widget_type == "chart":
            # TODO: Implémenter la mise à jour des graphiques
            pass
    
    def on_close(self) -> None:
        """Gère la fermeture du widget."""
        self.closed.emit(self)
    
    def on_settings(self) -> None:
        """Gère l'ouverture des paramètres du widget."""
        # TODO: Implémenter la boîte de dialogue des paramètres
        QMessageBox.information(self, "Information", "Paramètres du widget (non implémenté)")
    
    def on_move_started(self, widget) -> None:
        """
        Gère le début du déplacement du widget.
        
        Args:
            widget: Widget à déplacer
        """
        # Créer un QDrag
        drag = QDrag(self)
        
        # Créer une image du widget
        pixmap = QPixmap(self.size())
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setOpacity(0.7)
        self.render(painter)
        painter.end()
        
        # Définir l'image du drag
        drag.setPixmap(pixmap)
        drag.setHotSpot(self.header.drag_start_position)
        
        # Stocker les données du widget
        mime_data = QMimeData()
        mime_data.setText("dashboard_widget")
        mime_data.setData("application/x-dashboard-widget", QByteArray())
        drag.setMimeData(mime_data)
        
        # Exécuter le drag
        result = drag.exec_(Qt.MoveAction)
        
        # TODO: Gérer le résultat du drag

class CustomizableDashboard(ResponsiveWidget):
    """Tableau de bord personnalisable."""
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        Initialise le tableau de bord personnalisable.
        
        Args:
            parent: Widget parent
        """
        super().__init__(parent)
        self.dashboard_config = DashboardConfig()
        self.widgets = []
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        # Charger le tableau de bord actif
        dashboard = self.dashboard_config.get_dashboard()
        
        # Créer les widgets
        for widget_data in dashboard.get("widgets", []):
            self.add_widget(widget_data)
        
        # Accepter les drops
        self.setAcceptDrops(True)
    
    def add_widget(self, widget_data: Dict[str, Any]) -> None:
        """
        Ajoute un widget au tableau de bord.
        
        Args:
            widget_data: Données du widget
        """
        widget = DashboardWidget(widget_data, self)
        widget.closed.connect(self.on_widget_closed)
        widget.moved.connect(self.on_widget_moved)
        widget.resized.connect(self.on_widget_resized)
        
        # Ajouter le widget au layout responsive
        position = widget_data.get("position", [0, 0])
        col, row = position
        self.add_widget(widget, col, row)
        
        self.widgets.append(widget)
    
    def on_widget_closed(self, widget) -> None:
        """
        Gère la fermeture d'un widget.
        
        Args:
            widget: Widget fermé
        """
        # Trouver l'index du widget dans la liste
        for i, w in enumerate(self.widgets):
            if w == widget:
                # Supprimer le widget du layout
                self.responsive_layout.takeAt(i)
                
                # Supprimer le widget de la liste
                self.widgets.pop(i)
                
                # Supprimer le widget de la configuration
                dashboard = self.dashboard_config.get_dashboard()
                dashboard_id = dashboard["id"]
                dashboard["widgets"].pop(i)
                self.dashboard_config.update_dashboard(dashboard_id, dashboard)
                
                break
    
    def on_widget_moved(self, widget, col, row) -> None:
        """
        Gère le déplacement d'un widget.
        
        Args:
            widget: Widget déplacé
            col: Nouvelle colonne
            row: Nouvelle ligne
        """
        # Mettre à jour la position du widget dans le layout
        self.update_widget_position(widget, col, row)
        
        # Mettre à jour la position du widget dans la configuration
        for i, w in enumerate(self.widgets):
            if w == widget:
                dashboard = self.dashboard_config.get_dashboard()
                dashboard_id = dashboard["id"]
                dashboard["widgets"][i]["position"] = [col, row]
                self.dashboard_config.update_dashboard(dashboard_id, dashboard)
                break
    
    def on_widget_resized(self, widget, size) -> None:
        """
        Gère le redimensionnement d'un widget.
        
        Args:
            widget: Widget redimensionné
            size: Nouvelle taille
        """
        # TODO: Implémenter le redimensionnement des widgets
        pass
    
    def dragEnterEvent(self, event) -> None:
        """
        Gère l'événement d'entrée d'un drag.
        
        Args:
            event: Événement d'entrée d'un drag
        """
        if event.mimeData().hasText() and event.mimeData().text() == "dashboard_widget":
            event.acceptProposedAction()
    
    def dropEvent(self, event) -> None:
        """
        Gère l'événement de drop.
        
        Args:
            event: Événement de drop
        """
        if event.mimeData().hasText() and event.mimeData().text() == "dashboard_widget":
            # Calculer la position de drop
            pos = event.pos()
            
            # Convertir la position en coordonnées de grille
            col = pos.x() // 200  # Largeur approximative d'une colonne
            row = pos.y() // 150  # Hauteur approximative d'une ligne
            
            # Émettre le signal de déplacement pour le widget source
            source_widget = event.source()
            if source_widget in self.widgets:
                self.on_widget_moved(source_widget, col, row)
            
            event.acceptProposedAction()
    
    def refresh_dashboard(self) -> None:
        """Rafraîchit le tableau de bord."""
        # Supprimer tous les widgets
        for widget in self.widgets:
            widget.setParent(None)
            widget.deleteLater()
        
        self.widgets.clear()
        
        # Recharger le tableau de bord
        self.init_ui()