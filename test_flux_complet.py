#!/usr/bin/env python3
"""
Test complet du flux de matière corrigé avec nettoyage des données.
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def clean_database():
    """Nettoie la base de données pour un test propre."""
    print("🧹 Nettoyage de la base de données...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # Supprimer toutes les données de test
        session = db_manager.Session()
        try:
            # Supprimer tous les mouvements de stock
            session.execute("DELETE FROM stock_movements")
            session.execute("DELETE FROM productions")
            session.execute("DELETE FROM downtimes")
            session.execute("DELETE FROM resource_consumptions")
            session.execute("DELETE FROM unit_hour_counters")
            session.commit()
            print("✅ Base de données nettoyée")
            return True
        except Exception as e:
            session.rollback()
            print(f"❌ Erreur lors du nettoyage: {str(e)}")
            return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Erreur lors du nettoyage: {str(e)}")
        return False

def test_flux_complet():
    """Teste le flux complet de matière avec les corrections."""
    print("\n🔄 Test du flux complet de matière...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        db_manager = DatabaseManager()
        
        print("\n📊 État initial (après nettoyage):")
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f} tonnes")
        
        # Étape 1: Réception de phosphate brut
        print("\n1️⃣ RÉCEPTION - 500 tonnes de phosphate brut (28% P2O5)")
        reception_id = db_manager.add_reception(
            quantity=500.0,
            quality=28.0,
            reception_date=datetime.now()
        )
        
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        print(f"   ✅ Réception: {reception_stock:.1f} tonnes")
        
        # Étape 2: Concassage - consomme de la réception, produit pour la laverie
        print("\n2️⃣ CONCASSAGE - Traitement de 400 tonnes")
        concassage_id = db_manager.add_production(
            step=ProcessStepEnum.CONCASSAGE,
            quantity=400.0,  # Produit 400 tonnes
            quality=28.0,    # Même qualité
            quantity_used=450.0,  # Consomme 450 tonnes (perte de 50 tonnes)
            production_hours=6.0
        )
        
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        print(f"   ✅ Réception après concassage: {reception_stock:.1f} tonnes")
        print(f"   ✅ Concassage: {concassage_stock:.1f} tonnes")
        
        # Étape 3: Laverie - consomme du concassage, transfère au stockage final
        print("\n3️⃣ LAVERIE - Traitement de 300 tonnes")
        laverie_id = db_manager.add_production(
            step=ProcessStepEnum.LAVERIE,
            quantity=300.0,  # Produit 300 tonnes
            quality=32.0,    # Amélioration de la qualité
            quantity_used=350.0,  # Consomme 350 tonnes (perte de 50 tonnes)
            production_hours=8.0
        )
        
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        laverie_stock = db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        stockage_stock = db_manager.get_current_stock(ProcessStepEnum.STOCKAGE_FINAL)
        print(f"   ✅ Concassage après laverie: {concassage_stock:.1f} tonnes")
        print(f"   ✅ Laverie (doit être 0): {laverie_stock:.1f} tonnes")
        print(f"   ✅ Stockage final: {stockage_stock:.1f} tonnes")
        
        print("\n📊 État final:")
        total_input = 500.0
        total_output = stockage_stock
        total_remaining = reception_stock + concassage_stock + laverie_stock
        total_losses = total_input - total_output - total_remaining
        
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f} tonnes")
        
        print(f"\n📈 Bilan matière:")
        print(f"  Entrée totale: {total_input:.1f} tonnes")
        print(f"  Sortie finale: {total_output:.1f} tonnes")
        print(f"  Stock restant: {total_remaining:.1f} tonnes")
        print(f"  Pertes process: {total_losses:.1f} tonnes")
        print(f"  Rendement global: {(total_output/total_input)*100:.1f}%")
        
        # Vérifications
        if laverie_stock == 0.0:
            print("✅ Laverie ne stocke pas (correct)")
        else:
            print("❌ Laverie stocke encore (problème)")
            return False
            
        if stockage_stock > 0:
            print("✅ Stockage final reçoit la production")
        else:
            print("❌ Aucune production en stockage final")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_flux_qualite():
    """Teste le suivi de la qualité dans le flux."""
    print("\n🧪 Test du suivi de qualité...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        db_manager = DatabaseManager()
        
        # Vérifier la qualité du stockage final
        final_stock_data = db_manager.get_stock_with_quality(ProcessStepEnum.STOCKAGE_FINAL)
        
        print(f"Stock final: {final_stock_data['stock']:.1f} tonnes")
        if final_stock_data['has_quality_data']:
            print(f"Qualité P2O5: {final_stock_data['quality']:.1f}%")
            
            # La qualité devrait être autour de 32% (qualité de la laverie)
            if 30 <= final_stock_data['quality'] <= 35:
                print("✅ Qualité cohérente avec le traitement laverie")
                return True
            else:
                print("⚠️ Qualité inattendue")
                return True  # Pas critique
        else:
            print("⚠️ Pas de données de qualité")
            return True  # Pas critique
            
    except Exception as e:
        print(f"❌ Erreur lors du test qualité: {str(e)}")
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test complet du flux de matière corrigé")
    print("=" * 60)
    
    tests = [
        ("Nettoyage base de données", clean_database),
        ("Flux complet de matière", test_flux_complet),
        ("Suivi de qualité", test_flux_qualite),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Résumé des résultats
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ OK" if result else "❌ ÉCHEC"
        print(f"{test_name:<25} : {status}")
        if result:
            passed += 1
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Le flux de matière corrigé fonctionne parfaitement !")
        return 0
    else:
        print("⚠️ Des problèmes persistent dans le flux de matière.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
