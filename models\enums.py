from enum import Enum, auto

class ProcessStepEnum(Enum):
    """Énumération des étapes du processus de production."""
    RECEPTION = "Réception"
    CONCASSAGE = "Concassage"
    LAVERIE = "Laverie"
    STOCKAGE_FINAL = "Stockage Final"

class ResourceType(Enum):
    """Énumération des types de ressources."""
    ENERGY = "Énergie"
    WATER = "Eau"
    REACTIVE = "Réactif"
    RAW_MATERIAL = "Matière première"

class StockMovementType(Enum):
    """Énumération des types de mouvements de stock."""
    RECEPTION = "Réception"
    PRODUCTION = "Production"
    CONSUMPTION = "Consommation"
    TRANSFER = "Transfert"
    ADJUSTMENT = "Ajustement"

class QualityGrade(Enum):
    """Énumération des grades de qualité."""
    HIGH = "Haut"
    MEDIUM = "Moyen"
    LOW = "Bas"

class MovementTypeEnum(Enum):
    """Types de mouvements de stock."""
    ENTREE = "entree"
    SORTIE = "sortie"
    TRANSFERT = "transfert"

class QualityEnum(Enum):
    """Qualités de phosphate."""
    HAUTE = "haute"
    MOYENNE = "moyenne"
    BASSE = "basse" 