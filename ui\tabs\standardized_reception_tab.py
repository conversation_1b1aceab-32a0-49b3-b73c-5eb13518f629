"""
Onglet réception standardisé avec interface cohérente et optimisée.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QFileDialog, QProgressBar)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging
import pandas as pd

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import KPICard, StatusIndicator, ActionButton
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class StandardizedReceptionTab(StandardizedBaseTab):
    """Onglet réception standardisé avec structure cohérente."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📥 Réception Phosphate", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface standardisée."""
        # Section KPI (toujours visible en haut)
        self.stock_card = self.add_kpi_card("Stock Actuel", "0", "T")
        self.today_card = self.add_kpi_card("Reçu Aujourd'hui", "0", "T")
        self.week_card = self.add_kpi_card("Cette Semaine", "0", "T")
        self.month_card = self.add_kpi_card("Ce Mois", "0", "T")
        self.avg_quality_card = self.add_kpi_card("Qualité Moyenne", "0", "%")
        
        # Section Saisie Manuelle (zone gauche)
        form_layout = self.add_form_section("📝 Saisie Réception")
        
        fields = {
            'quantity': {
                'label': 'Quantité (T):',
                'type': 'number',
                'placeholder': 'Ex: 125.50',
                'category': 'Réception'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2',
                'category': 'Qualité'
            },
            'reception_date': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'supplier': {
                'label': 'Fournisseur:',
                'type': 'combo',
                'options': ['SOTRAMINE', 'Fournisseur A', 'Fournisseur B', 'Autre'],
                'category': 'Réception'
            },
            'truck_number': {
                'label': 'N° Camion:',
                'type': 'text',
                'placeholder': 'Ex: CAM-123',
                'category': 'Transport'
            },
            'driver': {
                'label': 'Chauffeur:',
                'type': 'text',
                'placeholder': 'Nom du chauffeur',
                'category': 'Transport'
            },
            'verified': {
                'label': 'Vérifié:',
                'type': 'checkbox',
                'default': False,
                'category': 'Validation'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_reception)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche)
        actions_layout = self.add_actions_section("🔧 Actions Avancées")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Importer", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter Rapport", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau des réceptions (zone droite)
        headers = ["Date/Heure", "Quantité (T)", "Qualité P2O5 (%)", "Fournisseur", "N° Camion", "Chauffeur", "Vérifié", "Actions"]
        self.data_table = self.add_data_table("📋 Historique des Réceptions", headers, sortable=True, paginated=True)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les indicateurs KPI."""
        try:
            # Stock actuel
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_card.update_value(f"{current_stock:.1f}")
            
            # Réception aujourd'hui
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=today
            )
            today_total = sum(m['quantity'] for m in today_movements)
            self.today_card.update_value(f"{today_total:.1f}")
            
            # Cette semaine
            start_of_week = today - timedelta(days=today.weekday())
            week_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_week
            )
            week_total = sum(m['quantity'] for m in week_movements)
            self.week_card.update_value(f"{week_total:.1f}")
            
            # Ce mois
            start_of_month = today.replace(day=1)
            month_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_month
            )
            month_total = sum(m['quantity'] for m in month_movements)
            self.month_card.update_value(f"{month_total:.1f}")
            
            # Qualité moyenne
            qualities = [m['quality'] for m in month_movements if m['quality']]
            avg_quality = sum(qualities) / len(qualities) if qualities else 0
            self.avg_quality_card.update_value(f"{avg_quality:.1f}")
            
            # Mettre à jour la barre d'état
            self.update_status(f"Dernière mise à jour: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI réception: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données avec pagination."""
        try:
            self.update_status("Chargement des données...")
            
            # Récupérer les paramètres de pagination
            pagination = self.get_pagination_params()
            
            # Dernières réceptions (avec pagination)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 derniers jours par défaut
            
            # Récupérer le nombre total d'éléments pour la pagination
            total_count = self.db_manager.get_stock_movements_count(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date
            )
            self.total_items = total_count
            
            # Récupérer les données paginées
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date,
                limit=pagination['limit'],
                offset=pagination['offset'],
                order_by='date',
                order_direction='DESC'  # Plus récent en haut
            )
            
            self.populate_table(movements)
            self.update_status(f"Affichage de {len(movements)} enregistrements sur {total_count}")
            
        except Exception as e:
            logging.error(f"Erreur actualisation données réception: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def populate_table(self, movements):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(movements))
        
        for row, movement in enumerate(movements):
            # Date/Heure
            self.data_table.setItem(row, 0, 
                self.data_table.itemClass()(movement['date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{movement['quantity']:.2f}"))
            
            # Qualité
            quality = f"{movement['quality']:.1f}" if movement['quality'] else "-"
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(quality))
            
            # Fournisseur (simulé pour l'exemple)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()("SOTRAMINE"))
            
            # N° Camion (simulé)
            self.data_table.setItem(row, 4,
                self.data_table.itemClass()(f"CAM-{row+100}"))
            
            # Chauffeur (simulé)
            self.data_table.setItem(row, 5,
                self.data_table.itemClass()("Chauffeur"))
            
            # Vérifié (simulé)
            self.data_table.setItem(row, 6,
                self.data_table.itemClass()("✓" if row % 2 == 0 else ""))
            
            # Actions
            self.data_table.setItem(row, 7,
                self.data_table.itemClass()("📝 Modifier | 🗑️ Supprimer"))
                
    def save_reception(self, data):
        """Sauvegarde une nouvelle réception."""
        try:
            # Validation des données
            if not data.get('quantity') or float(data['quantity']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive")
                return
                
            quantity = float(data['quantity'])
            quality = float(data.get('quality', 0)) if data.get('quality') else None
            reception_date = data.get('reception_date', datetime.now())
            supplier = data.get('supplier', 'SOTRAMINE')
            truck_number = data.get('truck_number', '')
            driver = data.get('driver', '')
            verified = data.get('verified', False)
            
            # Enregistrer la réception
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.RECEPTION,
                quantity=quantity,
                movement_type=StockMovementType.RECEPTION,
                movement_date=reception_date,
                quality=quality,
                metadata={
                    'supplier': supplier,
                    'truck_number': truck_number,
                    'driver': driver,
                    'verified': verified
                }
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            # Message de confirmation
            QMessageBox.information(self, "✅ Succès", 
                f"Réception de {quantity:.1f}T enregistrée avec succès")
                
        except ValueError as e:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
            
    def import_from_excel(self):
        """Importe des données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "Importer fichier Excel", 
                "", 
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['Quantité', 'Date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "Erreur", 
                    f"Colonnes manquantes: {', '.join(missing_columns)}")
                return
                
            # Progress bar
            progress = QProgressBar()
            progress.setMaximum(len(df))
            progress.show()
            
            imported_count = 0
            
            for index, row in df.iterrows():
                try:
                    quantity = float(row['Quantité'])
                    date = pd.to_datetime(row['Date']).to_pydatetime()
                    quality = float(row.get('Qualité', 0)) if pd.notna(row.get('Qualité')) else None
                    supplier = row.get('Fournisseur', 'SOTRAMINE')
                    truck_number = row.get('N° Camion', '')
                    driver = row.get('Chauffeur', '')
                    
                    metadata = {
                        'supplier': supplier,
                        'truck_number': truck_number,
                        'driver': driver
                    }
                    
                    self.db_manager.add_stock_movement(
                        step=ProcessStepEnum.RECEPTION,
                        quantity=quantity,
                        movement_type=StockMovementType.RECEPTION,
                        movement_date=date,
                        quality=quality,
                        metadata=metadata
                    )
                    
                    imported_count += 1
                    progress.setValue(index + 1)
                    
                except Exception as e:
                    logging.warning(f"Erreur ligne {index + 1}: {str(e)}")
                    continue
                    
            progress.close()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Import Terminé", 
                f"{imported_count} réceptions importées avec succès")
                
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
            
    def export_data(self):
        """Exporte les données du tableau vers Excel."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter vers Excel",
                f"receptions_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            # Récupérer les données actuelles du tableau
            data = []
            for row in range(self.data_table.rowCount()):
                row_data = {}
                for col in range(self.data_table.columnCount() - 1):  # Exclure la colonne Actions
                    header = self.data_table.horizontalHeaderItem(col).text()
                    item = self.data_table.item(row, col)
                    row_data[header] = item.text() if item else ""
                data.append(row_data)
                
            # Créer DataFrame
            df = pd.DataFrame(data)
            
            # Sauvegarder
            df.to_excel(file_path, index=False, sheet_name='Réceptions')
            
            self.update_status(f"Données exportées vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Données exportées vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")
            
    def export_detailed_report(self):
        """Exporte un rapport détaillé avec graphiques et analyses."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter Rapport Détaillé",
                f"rapport_receptions_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            self.update_status("Génération du rapport détaillé...")
            
            # Récupérer les données des 30 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date,
                limit=1000  # Nombre élevé pour avoir toutes les données
            )
            
            # Créer un DataFrame avec les données
            data = []
            for movement in movements:
                data.append({
                    'Date': movement['date'],
                    'Quantité (T)': movement['quantity'],
                    'Qualité P2O5 (%)': movement['quality'] if movement['quality'] else 0,
                    'Fournisseur': 'SOTRAMINE'  # Simulé
                })
                
            df = pd.DataFrame(data)
            
            # Créer un writer Excel avec xlsxwriter
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # Écrire les données brutes
            df.to_excel(writer, sheet_name='Données', index=False)
            
            # Créer une feuille de résumé
            summary = pd.DataFrame({
                'Métrique': [
                    'Total Reçu (T)',
                    'Qualité Moyenne (%)',
                    'Nombre de Réceptions',
                    'Réception Moyenne (T)',
                    'Réception Max (T)',
                    'Réception Min (T)'
                ],
                'Valeur': [
                    df['Quantité (T)'].sum(),
                    df['Qualité P2O5 (%)'].mean(),
                    len(df),
                    df['Quantité (T)'].mean(),
                    df['Quantité (T)'].max(),
                    df['Quantité (T)'].min()
                ]
            })
            
            summary.to_excel(writer, sheet_name='Résumé', index=False)
            
            # Sauvegarder et fermer
            writer.save()
            
            self.update_status(f"Rapport détaillé exporté vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Rapport détaillé exporté vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export rapport: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export du rapport: {str(e)}")
            
    def print_report(self):
        """Prépare et imprime un rapport optimisé pour l'impression."""
        try:
            self.update_status("Préparation de l'impression...")
            
            # Créer une mise en page spéciale pour l'impression
            # Cette fonction pourrait utiliser QPrinter et QPainter pour générer un rapport PDF
            # ou utiliser une bibliothèque comme ReportLab
            
            QMessageBox.information(self, "Impression", 
                "Fonctionnalité d'impression en cours de développement.\n"
                "Veuillez utiliser l'export Excel pour le moment.")
                
        except Exception as e:
            logging.error(f"Erreur impression: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la préparation de l'impression: {str(e)}")