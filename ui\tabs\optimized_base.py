"""
Classes de base et composants communs pour les onglets optimisés.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTableWidget, QSplitter,
                            QStackedWidget, QScrollArea, QFrame, QProgressBar,
                            QMessageBox, QDateTimeEdit, QLineEdit, QComboBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta
import logging

from ui.widgets.professional_widgets import KPICard, StatusIndicator, ProfessionalTable, ActionButton, SectionHeader
from ui.professional_theme import SotramineTheme
from ui.icon_manager import icon_manager

class BaseOptimizedTab(QWidget):
    """Classe de base pour tous les onglets optimisés."""
    
    def __init__(self, db_manager, tab_name, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.tab_name = tab_name
        self.is_initialized = False
        
        self.init_base_ui()
        
    def init_base_ui(self):
        """Initialise l'interface de base."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(20)
        
        # En-tête de section avec titre
        self.header = SectionHeader(self.tab_name, "Interface optimisée et moderne")
        self.main_layout.addWidget(self.header)
        
        # Conteneur principal avec splitter
        self.splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(self.splitter)
        
        # Zone gauche - Contrôles et KPI
        self.controls_widget = QWidget()
        self.controls_widget.setMaximumWidth(400)
        self.controls_widget.setMinimumWidth(350)
        self.controls_layout = QVBoxLayout(self.controls_widget)
        self.controls_layout.setContentsMargins(10, 10, 10, 10)
        self.controls_layout.setSpacing(15)
        
        # Zone droite - Données et tableaux
        self.data_widget = QWidget()
        self.data_layout = QVBoxLayout(self.data_widget)
        self.data_layout.setContentsMargins(10, 10, 10, 10)
        self.data_layout.setSpacing(15)
        
        self.splitter.addWidget(self.controls_widget)
        self.splitter.addWidget(self.data_widget)
        self.splitter.setSizes([350, 800])
        
    def add_kpi_section(self, title="Indicateurs Clés"):
        """Ajoute une section KPI à la zone de contrôles."""
        kpi_group = QGroupBox(title)
        kpi_layout = QVBoxLayout(kpi_group)
        kpi_layout.setSpacing(10)
        
        self.kpi_layout = QVBoxLayout()
        kpi_layout.addLayout(self.kpi_layout)
        
        self.controls_layout.addWidget(kpi_group)
        return self.kpi_layout
        
    def add_actions_section(self, title="Actions"):
        """Ajoute une section d'actions à la zone de contrôles."""
        actions_group = QGroupBox(title)
        actions_layout = QVBoxLayout(actions_group)
        actions_layout.setSpacing(10)
        
        self.actions_layout = QVBoxLayout()
        actions_layout.addLayout(self.actions_layout)
        
        self.controls_layout.addWidget(actions_group)
        return self.actions_layout
        
    def add_form_section(self, title="Saisie"):
        """Ajoute une section de formulaire à la zone de contrôles."""
        form_group = QGroupBox(title)
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(10)
        
        self.form_layout = QVBoxLayout()
        form_layout.addLayout(self.form_layout)
        
        self.controls_layout.addWidget(form_group)
        return self.form_layout
        
    def add_data_table(self, title, headers):
        """Ajoute un tableau de données à la zone de droite."""
        data_group = QGroupBox(title)
        data_layout = QVBoxLayout(data_group)
        
        # Barre d'actions pour le tableau
        table_actions = QHBoxLayout()
        
        refresh_btn = ActionButton("Actualiser", "Actualiser", "primary")
        refresh_btn.clicked.connect(self.refresh_data)
        table_actions.addWidget(refresh_btn)
        
        table_actions.addStretch()
        data_layout.addLayout(table_actions)
        
        # Tableau professionnel
        self.main_table = ProfessionalTable()
        self.main_table.setColumnCount(len(headers))
        self.main_table.setHorizontalHeaderLabels(headers)
        
        data_layout.addWidget(self.main_table)
        self.data_layout.addWidget(data_group)
        
        return self.main_table
        
    def add_stretch(self):
        """Ajoute un espaceur extensible aux contrôles."""
        self.controls_layout.addStretch()
        
    def refresh_data(self):
        """Méthode à surcharger pour actualiser les données."""
        pass

class QuickInputWidget(QFrame):
    """Widget de saisie rapide optimisé."""
    
    data_entered = pyqtSignal(dict)
    
    def __init__(self, fields, parent=None):
        super().__init__(parent)
        self.fields = fields
        self.inputs = {}
        
        self.setProperty("class", "card")
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface de saisie."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Titre
        title = QLabel("⚡ Saisie Rapide")
        title.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # Champs de saisie
        for field_name, field_config in self.fields.items():
            field_layout = QHBoxLayout()
            
            # Label
            label = QLabel(field_config['label'])
            label.setMinimumWidth(100)
            field_layout.addWidget(label)
            
            # Widget de saisie
            if field_config['type'] == 'text':
                widget = QLineEdit()
                widget.setPlaceholderText(field_config.get('placeholder', ''))
            elif field_config['type'] == 'number':
                widget = QLineEdit()
                widget.setPlaceholderText(field_config.get('placeholder', '0.00'))
            elif field_config['type'] == 'datetime':
                widget = QDateTimeEdit()
                widget.setDateTime(QDateTime.currentDateTime())
                widget.setCalendarPopup(True)
            elif field_config['type'] == 'combo':
                widget = QComboBox()
                widget.addItems(field_config.get('options', []))
            else:
                widget = QLineEdit()
                
            self.inputs[field_name] = widget
            field_layout.addWidget(widget)
            
            layout.addLayout(field_layout)
            
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        save_btn = ActionButton("💾 Enregistrer", "Enregistrer", "success")
        save_btn.clicked.connect(self.save_data)
        buttons_layout.addWidget(save_btn)
        
        clear_btn = ActionButton("🗑️ Effacer", "Annuler", "warning")
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
    def save_data(self):
        """Sauvegarde les données saisies."""
        data = {}
        for field_name, widget in self.inputs.items():
            if isinstance(widget, QLineEdit):
                data[field_name] = widget.text()
            elif isinstance(widget, QDateTimeEdit):
                data[field_name] = widget.dateTime().toPyDateTime()
            elif isinstance(widget, QComboBox):
                data[field_name] = widget.currentText()
                
        self.data_entered.emit(data)
        
    def clear_form(self):
        """Efface le formulaire."""
        for widget in self.inputs.values():
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, QDateTimeEdit):
                widget.setDateTime(QDateTime.currentDateTime())
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)

class DataLoader(QThread):
    """Thread générique pour charger des données."""
    
    data_loaded = pyqtSignal(object)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, load_function, *args, **kwargs):
        super().__init__()
        self.load_function = load_function
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        """Exécute la fonction de chargement."""
        try:
            self.progress_updated.emit(20)
            data = self.load_function(*self.args, **self.kwargs)
            self.progress_updated.emit(100)
            self.data_loaded.emit(data)
        except Exception as e:
            logging.error(f"Erreur lors du chargement des données: {str(e)}")
            self.error_occurred.emit(str(e))

class StatusWidget(QFrame):
    """Widget de statut avec indicateurs colorés."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setProperty("class", "card")
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface de statut."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Titre
        title = QLabel("📊 Statut Temps Réel")
        title.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # Zone des indicateurs
        self.indicators_layout = QVBoxLayout()
        layout.addLayout(self.indicators_layout)
        
        # Timer de mise à jour automatique - DÉSACTIVÉ
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        # self.update_timer.start(15000)  # DÉSACTIVÉ
        
    def add_indicator(self, title, status="OK"):
        """Ajoute un indicateur de statut."""
        indicator = StatusIndicator(title, status)
        self.indicators_layout.addWidget(indicator)
        return indicator
        
    def update_status(self):
        """Met à jour le statut (à surcharger)."""
        pass
