"""Add quality column to StockMovement

Revision ID: cf8d9575f00d
Revises: add_source_step_id_to_stock_movements
Create Date: 2025-06-12 23:14:39.451404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cf8d9575f00d'
down_revision: Union[str, None] = 'add_source_step_id_to_stock_movements'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('downtimes', 'reason',
    #            existing_type=sa.TEXT(),
    #            type_=sa.String(length=100),
    #            nullable=False)
    op.add_column('stock_movements', sa.Column('quality', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('stock_movements', 'quality')
    # op.alter_column('downtimes', 'reason',
    #            existing_type=sa.String(length=100),
    #            type_=sa.TEXT(),
    #            nullable=True)
    # ### end Alembic commands ###
