"""
Classe de base standardisée pour tous les onglets avec structure cohérente.
Implémente les recommandations d'interface utilisateur unifiée.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTableWidget, QSplitter,
                            QStackedWidget, QScrollArea, QFrame, QProgressBar,
                            QMessageBox, QDateTimeEdit, QLineEdit, QComboBox,
                            QCheckBox, QToolButton, QMenu, QAction, QSpinBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime, QSize
from PyQt5.QtGui import QFont, QIcon
from datetime import datetime, timedelta
import logging

from ui.widgets.professional_widgets import KPICard, StatusIndicator, ProfessionalTable, ActionButton, SectionHeader
from ui.professional_theme import SotramineTheme
from ui.icon_manager import icon_manager

class StandardizedBaseTab(QWidget):
    """
    Classe de base standardisée pour tous les onglets.
    Implémente une structure cohérente avec:
    - KPI en haut (toujours visible)
    - Formulaire de saisie
    - Tableau de données
    - Espacement standardisé
    - Palette de couleurs unifiée
    """
    
    def __init__(self, db_manager, tab_name, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.tab_name = tab_name
        self.is_initialized = False
        self.compact_mode = False
        self.kpi_cards = []
        
        # Initialiser l'interface
        self.init_base_ui()
        
    def init_base_ui(self):
        """Initialise l'interface de base standardisée."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(20)
        
        # En-tête avec titre et options
        self.header_layout = QHBoxLayout()
        
        # Titre de l'onglet
        self.header = SectionHeader(self.tab_name, "")
        self.header_layout.addWidget(self.header, 1)
        
        # Boutons d'options
        self.options_layout = QHBoxLayout()
        self.options_layout.setSpacing(10)
        
        # Bouton mode compact
        self.compact_btn = QToolButton()
        self.compact_btn.setIcon(icon_manager.get_action_icon("Compact"))
        self.compact_btn.setToolTip("Mode compact")
        self.compact_btn.clicked.connect(self.toggle_compact_mode)
        self.options_layout.addWidget(self.compact_btn)
        
        # Bouton d'impression
        self.print_btn = QToolButton()
        self.print_btn.setIcon(icon_manager.get_action_icon("Print"))
        self.print_btn.setToolTip("Imprimer")
        self.print_btn.clicked.connect(self.print_report)
        self.options_layout.addWidget(self.print_btn)
        
        # Bouton de personnalisation
        self.customize_btn = QToolButton()
        self.customize_btn.setIcon(icon_manager.get_action_icon("Settings"))
        self.customize_btn.setToolTip("Personnaliser l'affichage")
        self.customize_btn.clicked.connect(self.show_customize_menu)
        self.options_layout.addWidget(self.customize_btn)
        
        self.header_layout.addLayout(self.options_layout)
        self.main_layout.addLayout(self.header_layout)
        
        # Section KPI (toujours visible en haut)
        self.kpi_group = QGroupBox("📊 Indicateurs de Performance")
        self.kpi_layout = QVBoxLayout(self.kpi_group)
        self.kpi_layout.setSpacing(10)
        
        # Conteneur pour les cartes KPI
        self.kpi_cards_layout = QHBoxLayout()
        self.kpi_layout.addLayout(self.kpi_cards_layout)
        
        self.main_layout.addWidget(self.kpi_group)
        
        # Conteneur principal avec splitter
        self.splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(self.splitter, 1)  # Stretch pour prendre l'espace restant
        
        # Zone gauche - Formulaire de saisie
        self.form_widget = QWidget()
        self.form_widget.setMaximumWidth(400)
        self.form_widget.setMinimumWidth(350)
        self.form_layout = QVBoxLayout(self.form_widget)
        self.form_layout.setContentsMargins(10, 10, 10, 10)
        self.form_layout.setSpacing(15)
        
        # Zone droite - Tableau de données
        self.data_widget = QWidget()
        self.data_layout = QVBoxLayout(self.data_widget)
        self.data_layout.setContentsMargins(10, 10, 10, 10)
        self.data_layout.setSpacing(15)
        
        self.splitter.addWidget(self.form_widget)
        self.splitter.addWidget(self.data_widget)
        self.splitter.setSizes([350, 800])
        
        # Barre d'état en bas (optionnelle)
        self.status_bar = QFrame()
        self.status_bar.setFrameShape(QFrame.StyledPanel)
        self.status_bar.setMaximumHeight(30)
        self.status_bar_layout = QHBoxLayout(self.status_bar)
        self.status_bar_layout.setContentsMargins(10, 2, 10, 2)
        
        self.status_label = QLabel("Prêt")
        self.status_bar_layout.addWidget(self.status_label)
        
        self.main_layout.addWidget(self.status_bar)
        
    def add_kpi_card(self, title, value="0", unit="", trend=None):
        """Ajoute une carte KPI à la section KPI."""
        card = KPICard(title, value, unit, trend)
        self.kpi_cards.append(card)
        self.kpi_cards_layout.addWidget(card)
        return card
        
    def add_form_section(self, title="📝 Saisie des Données"):
        """Ajoute une section de formulaire standardisée."""
        form_group = QGroupBox(title)
        form_inner_layout = QVBoxLayout(form_group)
        form_inner_layout.setSpacing(10)
        
        self.input_layout = QVBoxLayout()
        form_inner_layout.addLayout(self.input_layout)
        
        self.form_layout.addWidget(form_group)
        return self.input_layout
        
    def add_actions_section(self, title="🔧 Actions"):
        """Ajoute une section d'actions standardisée."""
        actions_group = QGroupBox(title)
        actions_inner_layout = QVBoxLayout(actions_group)
        actions_inner_layout.setSpacing(10)
        
        self.actions_layout = QVBoxLayout()
        actions_inner_layout.addLayout(self.actions_layout)
        
        self.form_layout.addWidget(actions_group)
        return self.actions_layout
        
    def add_data_table(self, title="📋 Données", headers=None, sortable=True, paginated=True):
        """Ajoute un tableau de données standardisé avec options avancées."""
        if headers is None:
            headers = []
            
        data_group = QGroupBox(title)
        data_layout = QVBoxLayout(data_group)
        
        # Barre d'actions pour le tableau
        table_actions = QHBoxLayout()
        
        # Bouton d'actualisation
        refresh_btn = ActionButton("Actualiser", "Actualiser", "primary")
        refresh_btn.clicked.connect(self.refresh_data)
        table_actions.addWidget(refresh_btn)
        
        # Contrôles de pagination si activés
        if paginated:
            pagination_layout = QHBoxLayout()
            
            prev_btn = QToolButton()
            prev_btn.setIcon(icon_manager.get_action_icon("Previous"))
            prev_btn.clicked.connect(self.previous_page)
            pagination_layout.addWidget(prev_btn)
            
            self.page_label = QLabel("Page 1")
            pagination_layout.addWidget(self.page_label)
            
            next_btn = QToolButton()
            next_btn.setIcon(icon_manager.get_action_icon("Next"))
            next_btn.clicked.connect(self.next_page)
            pagination_layout.addWidget(next_btn)
            
            # Sélecteur de taille de page
            pagination_layout.addWidget(QLabel("Lignes:"))
            
            self.page_size_selector = QSpinBox()
            self.page_size_selector.setRange(5, 50)
            self.page_size_selector.setSingleStep(5)
            self.page_size_selector.setValue(15)
            self.page_size_selector.valueChanged.connect(self.change_page_size)
            pagination_layout.addWidget(self.page_size_selector)
            
            table_actions.addLayout(pagination_layout)
        
        table_actions.addStretch()
        
        # Bouton d'export
        export_btn = ActionButton("Exporter", "Export", "primary")
        export_btn.clicked.connect(self.export_data)
        table_actions.addWidget(export_btn)
        
        data_layout.addLayout(table_actions)
        
        # Tableau professionnel
        self.data_table = ProfessionalTable()
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)
        
        # Configuration du tri si activé
        if sortable:
            self.data_table.setSortingEnabled(True)
        
        data_layout.addWidget(self.data_table)
        self.data_layout.addWidget(data_group)
        
        # Initialiser les variables de pagination
        self.current_page = 1
        self.page_size = 15
        self.total_items = 0
        
        return self.data_table
        
    def add_stretch(self):
        """Ajoute un espaceur extensible au formulaire."""
        self.form_layout.addStretch()
        
    def refresh_data(self):
        """Méthode à surcharger pour actualiser les données."""
        self.update_status("Actualisation des données...")
        # À implémenter dans les classes dérivées
        self.update_status("Données actualisées")
        
    def update_kpi(self):
        """Méthode à surcharger pour mettre à jour les KPI."""
        # À implémenter dans les classes dérivées
        pass
        
    def update_status(self, message):
        """Met à jour le message de la barre d'état."""
        self.status_label.setText(message)
        
    def toggle_compact_mode(self):
        """Active/désactive le mode compact."""
        self.compact_mode = not self.compact_mode
        
        # Ajuster les marges et espacements
        if self.compact_mode:
            self.main_layout.setContentsMargins(5, 5, 5, 5)
            self.main_layout.setSpacing(5)
            self.form_layout.setContentsMargins(5, 5, 5, 5)
            self.form_layout.setSpacing(5)
            self.data_layout.setContentsMargins(5, 5, 5, 5)
            self.data_layout.setSpacing(5)
            
            # Réduire la hauteur des KPI
            for card in self.kpi_cards:
                card.setMinimumHeight(80)
                card.setMaximumHeight(100)
        else:
            self.main_layout.setContentsMargins(15, 15, 15, 15)
            self.main_layout.setSpacing(20)
            self.form_layout.setContentsMargins(10, 10, 10, 10)
            self.form_layout.setSpacing(15)
            self.data_layout.setContentsMargins(10, 10, 10, 10)
            self.data_layout.setSpacing(15)
            
            # Restaurer la hauteur des KPI
            for card in self.kpi_cards:
                card.setMinimumHeight(120)
                card.setMaximumHeight(150)
                
        self.update_status("Mode compact " + ("activé" if self.compact_mode else "désactivé"))
        
    def show_customize_menu(self):
        """Affiche le menu de personnalisation."""
        menu = QMenu(self)
        
        # Options de personnalisation des KPI
        kpi_menu = QMenu("Indicateurs KPI", menu)
        
        # Ajouter des options pour chaque KPI
        for i, card in enumerate(self.kpi_cards):
            action = QAction(card.title, kpi_menu)
            action.setCheckable(True)
            action.setChecked(card.isVisible())
            action.triggered.connect(lambda checked, idx=i: self.toggle_kpi_visibility(idx, checked))
            kpi_menu.addAction(action)
            
        menu.addMenu(kpi_menu)
        
        # Options de colonnes du tableau
        if hasattr(self, 'data_table'):
            columns_menu = QMenu("Colonnes du tableau", menu)
            
            for i in range(self.data_table.columnCount()):
                header_text = self.data_table.horizontalHeaderItem(i).text()
                action = QAction(header_text, columns_menu)
                action.setCheckable(True)
                action.setChecked(not self.data_table.isColumnHidden(i))
                action.triggered.connect(lambda checked, idx=i: self.toggle_column_visibility(idx, checked))
                columns_menu.addAction(action)
                
            menu.addMenu(columns_menu)
        
        # Afficher le menu
        menu.exec_(self.customize_btn.mapToGlobal(self.customize_btn.rect().bottomLeft()))
        
    def toggle_kpi_visibility(self, index, visible):
        """Active/désactive la visibilité d'un KPI."""
        if 0 <= index < len(self.kpi_cards):
            self.kpi_cards[index].setVisible(visible)
            
    def toggle_column_visibility(self, column, visible):
        """Active/désactive la visibilité d'une colonne."""
        if hasattr(self, 'data_table'):
            self.data_table.setColumnHidden(column, not visible)
            
    def print_report(self):
        """Prépare et imprime un rapport."""
        self.update_status("Préparation de l'impression...")
        # À implémenter dans les classes dérivées
        
    def export_data(self):
        """Exporte les données du tableau."""
        self.update_status("Exportation des données...")
        # À implémenter dans les classes dérivées
        
    def previous_page(self):
        """Passe à la page précédente."""
        if self.current_page > 1:
            self.current_page -= 1
            self.page_label.setText(f"Page {self.current_page}")
            self.refresh_data()
            
    def next_page(self):
        """Passe à la page suivante."""
        max_pages = max(1, (self.total_items + self.page_size - 1) // self.page_size)
        if self.current_page < max_pages:
            self.current_page += 1
            self.page_label.setText(f"Page {self.current_page}")
            self.refresh_data()
            
    def change_page_size(self, size):
        """Change le nombre d'éléments par page."""
        self.page_size = size
        self.current_page = 1
        self.page_label.setText(f"Page {self.current_page}")
        self.refresh_data()
        
    def get_pagination_params(self):
        """Retourne les paramètres de pagination pour les requêtes."""
        offset = (self.current_page - 1) * self.page_size
        return {
            'limit': self.page_size,
            'offset': offset
        }


class StandardizedQuickInputWidget(QFrame):
    """Widget de saisie rapide standardisé."""
    
    data_entered = pyqtSignal(dict)
    
    def __init__(self, fields, parent=None):
        super().__init__(parent)
        self.fields = fields
        self.inputs = {}
        
        self.setProperty("class", "card")
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface de saisie standardisée."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Titre
        title = QLabel("⚡ Saisie Rapide")
        title.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # Organiser les champs par catégories si spécifiées
        categories = {}
        
        for field_name, field_config in self.fields.items():
            category = field_config.get('category', 'Général')
            if category not in categories:
                categories[category] = []
            categories[category].append((field_name, field_config))
        
        # Créer les sections pour chaque catégorie
        for category, fields in categories.items():
            if category != 'Général' and len(categories) > 1:
                # Ajouter un séparateur avec titre de catégorie
                separator = QFrame()
                separator.setFrameShape(QFrame.HLine)
                separator.setStyleSheet(f"color: {SotramineTheme.GRAY_300};")
                layout.addWidget(separator)
                
                category_label = QLabel(category)
                category_label.setStyleSheet(f"""
                    font-size: 11pt;
                    font-weight: bold;
                    color: {SotramineTheme.PRIMARY};
                    margin-top: 5px;
                """)
                layout.addWidget(category_label)
            
            # Ajouter les champs de cette catégorie
            for field_name, field_config in fields:
                field_layout = QHBoxLayout()
                
                # Label
                label = QLabel(field_config['label'])
                label.setMinimumWidth(120)
                field_layout.addWidget(label)
                
                # Widget de saisie
                if field_config['type'] == 'text':
                    widget = QLineEdit()
                    widget.setPlaceholderText(field_config.get('placeholder', ''))
                elif field_config['type'] == 'number':
                    widget = QLineEdit()
                    widget.setPlaceholderText(field_config.get('placeholder', '0.00'))
                elif field_config['type'] == 'datetime':
                    widget = QDateTimeEdit()
                    widget.setDateTime(QDateTime.currentDateTime())
                    widget.setCalendarPopup(True)
                elif field_config['type'] == 'combo':
                    widget = QComboBox()
                    widget.addItems(field_config.get('options', []))
                elif field_config['type'] == 'checkbox':
                    widget = QCheckBox()
                    if field_config.get('default', False):
                        widget.setChecked(True)
                else:
                    widget = QLineEdit()
                    
                self.inputs[field_name] = widget
                field_layout.addWidget(widget)
                
                layout.addLayout(field_layout)
                
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        save_btn = ActionButton("💾 Enregistrer", "Enregistrer", "success")
        save_btn.clicked.connect(self.save_data)
        buttons_layout.addWidget(save_btn)
        
        clear_btn = ActionButton("🗑️ Effacer", "Annuler", "warning")
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
    def save_data(self):
        """Sauvegarde les données saisies."""
        data = {}
        for field_name, widget in self.inputs.items():
            if isinstance(widget, QLineEdit):
                data[field_name] = widget.text()
            elif isinstance(widget, QDateTimeEdit):
                data[field_name] = widget.dateTime().toPyDateTime()
            elif isinstance(widget, QComboBox):
                data[field_name] = widget.currentText()
            elif isinstance(widget, QCheckBox):
                data[field_name] = widget.isChecked()
                
        self.data_entered.emit(data)
        
    def clear_form(self):
        """Efface le formulaire."""
        for widget in self.inputs.values():
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, QDateTimeEdit):
                widget.setDateTime(QDateTime.currentDateTime())
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(False)