"""
Widgets KPI minimalistes pour une interface épurée et moins encombrée.
Ces widgets sont conçus pour afficher les informations essentielles avec un minimum d'éléments visuels.
"""

from PyQt5.QtWidgets import (QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
                            QGridLayout, QSizePolicy, QWidget, QGroupBox,
                            QScrollArea, QTabWidget)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPainterPath
from ui.professional_theme import SotramineTheme

class MinimalKPITile(QFrame):
    """Tuile KPI minimaliste avec affichage simplifié."""
    
    def __init__(self, title, value="--", unit="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.color = color or SotramineTheme.PRIMARY
        
        self.setProperty("class", "tile")
        self.setFixedHeight(70)  # Hauteur fixe très réduite
        self.setMinimumWidth(120)  # Largeur minimale réduite
        self.setMaximumWidth(180)  # Largeur maximale pour éviter l'étirement
        
        # Style minimaliste
        self.setStyleSheet(f"""
            QFrame[class="tile"] {{
                background-color: {SotramineTheme.BG_CARD};
                border-left: 3px solid {self.color};
                border-radius: 3px;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la tuile KPI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 5, 8, 5)  # Marges très réduites
        layout.setSpacing(1)  # Espacement minimal
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 8pt;
            font-weight: bold;
            color: {self.color};
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        layout.addWidget(title_label)
        
        # Valeur principale et unité sur la même ligne
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        value_layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 8pt;
                color: {SotramineTheme.TEXT_MUTED};
                margin-left: 2px;
            """)
            unit_label.setAlignment(Qt.AlignBottom | Qt.AlignLeft)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        layout.addLayout(value_layout)
    
    def update_value(self, value):
        """Met à jour la valeur."""
        self.value = value
        self.value_label.setText(str(value))


class MinimalKPIGrid(QWidget):
    """Grille de tuiles KPI minimalistes."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.kpi_tiles = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la grille KPI."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(5)  # Espacement minimal
        
        # Utiliser une grille pour les KPI
        self.grid_layout = QGridLayout()
        self.grid_layout.setHorizontalSpacing(5)  # Espacement horizontal minimal
        self.grid_layout.setVerticalSpacing(5)    # Espacement vertical minimal
        
        self.main_layout.addLayout(self.grid_layout)
    
    def add_kpi_tile(self, title, value="0", unit="", color=None):
        """Ajoute une tuile KPI à la grille."""
        # Créer la tuile KPI
        tile = MinimalKPITile(title, value, unit, color)
        self.kpi_tiles.append(tile)
        
        # Ajouter à la grille
        row, col = divmod(len(self.kpi_tiles) - 1, 5)  # 5 tuiles par ligne
        self.grid_layout.addWidget(tile, row, col)
        
        return tile


class MinimalProgressBar(QFrame):
    """Barre de progression minimaliste."""
    
    def __init__(self, title, value=0, min_value=0, max_value=100, unit="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.min_value = min_value
        self.max_value = max_value
        self.unit = unit
        self.color = color or SotramineTheme.PRIMARY
        
        self.setFixedHeight(50)  # Hauteur fixe très réduite
        self.setMinimumWidth(120)  # Largeur minimale réduite
        self.setProperty("class", "progress")
        
        self.setStyleSheet(f"""
            QFrame[class="progress"] {{
                background-color: {SotramineTheme.BG_CARD};
                border-radius: 3px;
            }}
        """)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur de la barre de progression."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 5, 8, 5)  # Marges très réduites
        layout.setSpacing(2)  # Espacement minimal
        
        # Titre et valeur sur la même ligne
        header_layout = QHBoxLayout()
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 8pt;
            font-weight: bold;
            color: {self.color};
        """)
        header_layout.addWidget(title_label)
        
        # Valeur et unité
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 10pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        value_layout.addWidget(self.value_label, 1)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 7pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        header_layout.addLayout(value_layout)
        layout.addLayout(header_layout)
        
        # Barre de progression (sera dessinée dans paintEvent)
        self.progress_bar = QFrame()
        self.progress_bar.setFixedHeight(10)  # Hauteur fixe très réduite
        self.progress_bar.paintEvent = self.paint_progress
        layout.addWidget(self.progress_bar)
    
    def paint_progress(self, event):
        """Dessine la barre de progression."""
        painter = QPainter(self.progress_bar)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.progress_bar.width()
        height = self.progress_bar.height()
        
        # Calculer le pourcentage de progression
        range_value = self.max_value - self.min_value
        if range_value <= 0:
            progress = 0
        else:
            progress = (self.value - self.min_value) / range_value
            progress = max(0, min(1, progress))  # Limiter entre 0 et 1
        
        # Dessiner l'arrière-plan
        bg_color = QColor(self.color)
        bg_color.setAlpha(30)
        painter.setBrush(QBrush(bg_color))
        painter.setPen(Qt.NoPen)
        
        bg_rect = QRect(0, 0, width, height)
        painter.drawRoundedRect(bg_rect, 3, 3)  # Rayon très réduit
        
        # Dessiner la progression
        if progress > 0:
            progress_width = int(width * progress)
            progress_rect = QRect(0, 0, progress_width, height)
            
            painter.setBrush(QBrush(QColor(self.color)))
            painter.drawRoundedRect(progress_rect, 3, 3)  # Rayon très réduit
    
    def update_value(self, value):
        """Met à jour la valeur de la barre de progression."""
        self.value = value
        self.value_label.setText(f"{self.value}")
        
        # Mettre à jour la barre de progression
        self.progress_bar.update()


class MinimalDashboard(QWidget):
    """Tableau de bord minimaliste avec disposition optimisée."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur du tableau de bord."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(10)
        
        # Créer un widget avec onglets pour les différentes catégories
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: transparent;
            }
            QTabBar::tab {
                padding: 4px 8px;
                margin-right: 1px;
                font-size: 8pt;
            }
        """)
        
        self.main_layout.addWidget(self.tabs)
    
    def add_tab(self, title, icon=""):
        """Ajoute un nouvel onglet au tableau de bord."""
        tab = QWidget()
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(5, 5, 5, 5)
        tab_layout.setSpacing(5)
        
        # Ajouter l'onglet
        self.tabs.addTab(tab, f"{icon} {title}" if icon else title)
        
        return tab, tab_layout
    
    def add_grid_to_tab(self, tab, tab_layout):
        """Ajoute une grille KPI à un onglet."""
        grid = MinimalKPIGrid()
        tab_layout.addWidget(grid)
        return grid
    
    def add_progress_bars_to_tab(self, tab, tab_layout, title=""):
        """Ajoute une section de barres de progression à un onglet."""
        # Créer un conteneur pour les barres de progression
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(5)
        
        # Ajouter un titre si fourni
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                font-size: 8pt;
                font-weight: bold;
                color: #666;
                margin-bottom: 2px;
            """)
            container_layout.addWidget(title_label)
        
        # Ajouter le conteneur à l'onglet
        tab_layout.addWidget(container)
        
        return container_layout