import sys
import os
import traceback

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

def run_main():
    try:
        print("Starting application...")
        import main
        print("Main module imported successfully")
        main.main()
        print("Application exited normally")
    except Exception as e:
        print(f"Error running application: {e}")
        traceback.print_exc()
        return 1
    return 0

if __name__ == "__main__":
    exit_code = run_main()
    print(f"Application exited with code: {exit_code}")
    sys.exit(exit_code)