"""
Service de génération des rapports.
"""

from datetime import datetime
import logging
from models.enums import ProcessStepEnum
from utils.pdf_generator import PDFGenerator

class ReportService:
    def __init__(self, db_manager: object):
        self.db_manager = db_manager
        self.pdf_generator = PDFGenerator()

    def generate_daily_production_report(self, step: ProcessStepEnum = None,
                                       start_date: datetime = None,
                                       end_date: datetime = None) -> dict:
        """Génère un rapport de production quotidien."""
        try:
            # Si aucune date n'est spécifiée, utiliser la date du jour
            if start_date is None:
                start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            if end_date is None:
                end_date = datetime.now()
                
            # Récupérer les données de production
            production_data = {}
            
            # Si une étape spécifique est demandée
            if step:
                report_data = self.db_manager.get_daily_production_report(step, start_date, end_date)
                production_data[step.value] = report_data
            else:
                # Sinon, récupérer pour toutes les étapes
                for step_enum in ProcessStepEnum:
                    report_data = self.db_manager.get_daily_production_report(step_enum, start_date, end_date)
                    production_data[step_enum.value] = report_data
            
            # Récupérer les données de stock
            stock_data = {}
            
            # Si une étape spécifique est demandée
            if step:
                stock_movements = self.db_manager.get_stock_movements_report(step, start_date, end_date)
                current_stock = self.db_manager.get_current_stock(step)
                
                # Calculer les entrées et sorties
                entrees = sum(m['quantity'] for m in stock_movements if m['movement_type'] == 'ENTREE')
                sorties = sum(m['quantity'] for m in stock_movements if m['movement_type'] == 'SORTIE')
                
                # Stock initial = stock final - entrées + sorties
                initial_stock = current_stock - entrees + sorties
                
                stock_data[step.value] = {
                    'initial': initial_stock,
                    'entrees': entrees,
                    'sorties': sorties,
                    'final': current_stock
                }
            else:
                # Sinon, récupérer pour toutes les étapes
                for step_enum in ProcessStepEnum:
                    stock_movements = self.db_manager.get_stock_movements_report(step_enum, start_date, end_date)
                    current_stock = self.db_manager.get_current_stock(step_enum)
                    
                    # Calculer les entrées et sorties
                    entrees = sum(m['quantity'] for m in stock_movements if m['movement_type'] == 'ENTREE')
                    sorties = sum(m['quantity'] for m in stock_movements if m['movement_type'] == 'SORTIE')
                    
                    # Stock initial = stock final - entrées + sorties
                    initial_stock = current_stock - entrees + sorties
                    
                    stock_data[step_enum.value] = {
                        'initial': initial_stock,
                        'entrees': entrees,
                        'sorties': sorties,
                        'final': current_stock
                    }
            
            # Assembler les données complètes du rapport
            report_data = {
                'production': production_data,
                'stocks': stock_data,
                'date': start_date
            }
            
            return report_data
            
        except Exception as e:
            logging.error(f"Erreur lors de la génération du rapport de production quotidien: {str(e)}")
            raise Exception(f"Erreur lors de la génération du rapport de production quotidien: {str(e)}")

    def generate_daily_production_pdf(self, step: ProcessStepEnum = None,
                                    date: datetime = None) -> str:
        """
        Génère un rapport PDF de production journalier.
        
        Args:
            step (ProcessStepEnum, optional): Étape spécifique ou None pour toutes les étapes
            date (datetime, optional): Date du rapport ou None pour aujourd'hui
            
        Returns:
            str: Chemin du fichier PDF généré
        """
        try:
            # Définir la plage de dates pour un jour
            if date is None:
                date = datetime.now()
                
            start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = date.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            # Récupérer les données du rapport
            report_data = self.generate_daily_production_report(step, start_date, end_date)
            
            # Générer le PDF
            pdf_path = self.pdf_generator.generate_daily_production_report(report_data, date)
            
            return pdf_path
            
        except Exception as e:
            logging.error(f"Erreur lors de la génération du PDF de production journalier: {str(e)}")
            raise Exception(f"Erreur lors de la génération du PDF de production journalier: {str(e)}")

    def generate_weekly_production_report(self, step: ProcessStepEnum,
                                        start_date: datetime,
                                        end_date: datetime) -> dict:
        """Génère un rapport de production hebdomadaire."""
        try:
            return self.db_manager.get_weekly_production_report(step, start_date, end_date)
        except Exception as e:
            raise Exception(f"Erreur lors de la génération du rapport de production hebdomadaire: {str(e)}")

    def generate_monthly_production_report(self, step: ProcessStepEnum,
                                         start_date: datetime,
                                         end_date: datetime) -> dict:
        """Génère un rapport de production mensuel."""
        try:
            return self.db_manager.get_monthly_production_report(step, start_date, end_date)
        except Exception as e:
            raise Exception(f"Erreur lors de la génération du rapport de production mensuel: {str(e)}")

    def generate_downtime_report(self, step: ProcessStepEnum,
                                start_date: datetime,
                                end_date: datetime) -> dict:
        """Génère un rapport des arrêts."""
        try:
            return self.db_manager.get_downtime_report(step, start_date, end_date)
        except Exception as e:
            raise Exception(f"Erreur lors de la génération du rapport des arrêts: {str(e)}")

    def generate_stock_movements_report(self, step: ProcessStepEnum,
                                      start_date: datetime,
                                      end_date: datetime) -> dict:
        """Génère un rapport des mouvements de stock."""
        try:
            return self.db_manager.get_stock_movements_report(step, start_date, end_date)
        except Exception as e:
            raise Exception(f"Erreur lors de la génération du rapport des mouvements de stock: {str(e)}")

    def generate_resource_consumption_report(self, step: ProcessStepEnum,
                                           start_date: datetime,
                                           end_date: datetime) -> dict:
        """Génère un rapport de consommation des ressources."""
        try:
            return self.db_manager.get_resource_consumption_report(step, start_date, end_date)
        except Exception as e:
            raise Exception(f"Erreur lors de la génération du rapport de consommation des ressources: {str(e)}") 