"""
Onglet réception minimaliste avec affichage simplifié et moins encombré.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QFileDialog, QProgressBar, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import ActionButton
from ui.widgets.minimal_kpi_widgets import (MinimalKPITile, MinimalKPIGrid, 
                                          MinimalProgressBar, MinimalDashboard)
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class MinimalReceptionTab(StandardizedBaseTab):
    """Onglet réception minimaliste avec affichage simplifié et moins encombré."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📥 Réception", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface minimaliste."""
        # Remplacer le layout KPI standard par notre dashboard minimaliste
        self.kpi_group.setTitle("📊 Performance")
        
        # Supprimer le layout existant et créer un nouveau
        self.kpi_layout.removeItem(self.kpi_cards_layout)
        
        # Créer un tableau de bord minimaliste
        self.dashboard = MinimalDashboard()
        
        # Onglet Volumes (limité aux indicateurs essentiels)
        volumes_tab, volumes_layout = self.dashboard.add_tab("Volumes", "📊")
        
        # Grille KPI pour les volumes
        volumes_grid = self.dashboard.add_grid_to_tab(volumes_tab, volumes_layout)
        
        # Ajouter seulement les KPI essentiels
        self.stock_tile = volumes_grid.add_kpi_tile(
            "Stock Actuel", "0", "T", SotramineTheme.PRIMARY)
        self.today_tile = volumes_grid.add_kpi_tile(
            "Reçu Aujourd'hui", "0", "T", SotramineTheme.SECONDARY)
        self.month_tile = volumes_grid.add_kpi_tile(
            "Ce Mois", "0", "T", SotramineTheme.SECONDARY)
        self.daily_avg_tile = volumes_grid.add_kpi_tile(
            "Moy. Journalière", "0", "T/j", SotramineTheme.ACCENT)
        self.target_tile = volumes_grid.add_kpi_tile(
            "Objectif Mensuel", "0", "%", SotramineTheme.ACCENT)
        
        # Onglet Qualité (simplifié)
        quality_tab, quality_layout = self.dashboard.add_tab("Qualité", "🔍")
        
        # Barres de progression pour la qualité
        quality_bars = self.dashboard.add_progress_bars_to_tab(quality_tab, quality_layout)
        
        # Indicateur de qualité P2O5
        self.p2o5_bar = MinimalProgressBar(
            "Qualité P2O5", 0, 0, 40, "%", SotramineTheme.PRIMARY)
        quality_bars.addWidget(self.p2o5_bar)
        
        # Indicateur de conformité
        self.conformity_bar = MinimalProgressBar(
            "Conformité", 0, 0, 100, "%", SotramineTheme.SECONDARY)
        quality_bars.addWidget(self.conformity_bar)
        
        # Grille KPI pour les statistiques de qualité
        quality_grid = self.dashboard.add_grid_to_tab(quality_tab, quality_layout)
        
        # Ajouter seulement les KPI essentiels de qualité
        self.avg_quality_tile = quality_grid.add_kpi_tile(
            "Qualité Moyenne", "0", "%", SotramineTheme.PRIMARY)
        self.min_quality_tile = quality_grid.add_kpi_tile(
            "Qualité Min", "0", "%", SotramineTheme.DANGER)
        self.max_quality_tile = quality_grid.add_kpi_tile(
            "Qualité Max", "0", "%", SotramineTheme.SECONDARY)
        
        # Onglet Logistique (très simplifié)
        logistics_tab, logistics_layout = self.dashboard.add_tab("Logistique", "🚚")
        
        # Grille KPI pour la logistique
        logistics_grid = self.dashboard.add_grid_to_tab(logistics_tab, logistics_layout)
        
        # Ajouter seulement les KPI essentiels de logistique
        self.trucks_tile = logistics_grid.add_kpi_tile(
            "Camions Aujourd'hui", "0", "", SotramineTheme.PRIMARY)
        self.avg_load_tile = logistics_grid.add_kpi_tile(
            "Charge Moyenne", "0", "T", SotramineTheme.PRIMARY)
        
        # Ajouter le dashboard au layout
        self.kpi_layout.addWidget(self.dashboard)
        
        # Section Saisie Manuelle (zone gauche) - Simplifiée
        form_layout = self.add_form_section("📝 Saisie")
        
        # Réduire le nombre de champs pour simplifier l'interface
        fields = {
            'quantity': {
                'label': 'Quantité (T):',
                'type': 'number',
                'placeholder': 'Ex: 125.50',
                'category': 'Réception'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2',
                'category': 'Qualité'
            },
            'reception_date': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'supplier': {
                'label': 'Fournisseur:',
                'type': 'combo',
                'options': ['SOTRAMINE', 'Fournisseur A', 'Fournisseur B', 'Autre'],
                'category': 'Réception'
            },
            'truck_number': {
                'label': 'N° Camion:',
                'type': 'text',
                'placeholder': 'Ex: CAM-123',
                'category': 'Transport'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_reception)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche) - Simplifiée
        actions_layout = self.add_actions_section("🔧 Actions")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Import", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau des réceptions (zone droite) - Colonnes réduites
        headers = ["Date/Heure", "Quantité (T)", "Qualité P2O5 (%)", "Fournisseur", "N° Camion", "Actions"]
        self.data_table = self.add_data_table("📋 Historique", headers, sortable=True, paginated=True)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les indicateurs de performance."""
        try:
            # Dates de référence
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Stock actuel
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_tile.update_value(f"{current_stock:.1f}")
            
            # Réception aujourd'hui
            today_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=today
            )
            today_total = sum(m['quantity'] for m in today_movements)
            self.today_tile.update_value(f"{today_total:.1f}")
            
            # Ce mois
            month_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_month
            )
            month_total = sum(m['quantity'] for m in month_movements)
            self.month_tile.update_value(f"{month_total:.1f}")
            
            # Moyenne journalière (sur le mois)
            days_in_month = (datetime.now() - start_of_month).days + 1
            daily_avg = month_total / days_in_month if days_in_month > 0 else 0
            self.daily_avg_tile.update_value(f"{daily_avg:.1f}")
            
            # Objectif mensuel (simulé pour l'exemple)
            monthly_target = 5000  # Tonnes
            target_completion = (month_total / monthly_target * 100) if monthly_target > 0 else 0
            self.target_tile.update_value(f"{target_completion:.1f}")
            
            # Qualité moyenne
            qualities = [m['quality'] for m in month_movements if m['quality']]
            avg_quality = sum(qualities) / len(qualities) if qualities else 0
            self.avg_quality_tile.update_value(f"{avg_quality:.1f}")
            
            # Qualité min/max
            min_quality = min(qualities) if qualities else 0
            max_quality = max(qualities) if qualities else 0
            self.min_quality_tile.update_value(f"{min_quality:.1f}")
            self.max_quality_tile.update_value(f"{max_quality:.1f}")
            
            # Indicateurs visuels de qualité
            self.p2o5_bar.update_value(avg_quality)
            
            # Conformité (simulée pour l'exemple)
            conformity = 96.5  # Pourcentage
            self.conformity_bar.update_value(conformity)
            
            # Statistiques de camions
            self.trucks_tile.update_value(f"{len(today_movements)}")
            
            # Charge moyenne
            avg_load = today_total / len(today_movements) if today_movements else 0
            self.avg_load_tile.update_value(f"{avg_load:.1f}")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des KPI: {str(e)}")
    
    def refresh_data(self):
        """Rafraîchit les données du tableau."""
        try:
            # Récupérer les données de réception
            receptions = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                limit=100  # Limiter à 100 entrées pour améliorer les performances
            )
            
            # Effacer le tableau
            self.data_table.setRowCount(0)
            
            # Remplir avec les nouvelles données
            for reception in receptions:
                row_position = self.data_table.rowCount()
                self.data_table.insertRow(row_position)
                
                # Date/Heure
                date_str = reception.get('timestamp', datetime.now()).strftime('%d/%m/%Y %H:%M')
                self.data_table.set_item_text(row_position, 0, date_str)
                
                # Quantité
                quantity = reception.get('quantity', 0)
                self.data_table.set_item_text(row_position, 1, f"{quantity:.1f}")
                
                # Qualité
                quality = reception.get('quality', 0)
                self.data_table.set_item_text(row_position, 2, f"{quality:.1f}")
                
                # Fournisseur
                supplier = reception.get('supplier', 'Inconnu')
                self.data_table.set_item_text(row_position, 3, supplier)
                
                # N° Camion
                truck = reception.get('truck_number', '-')
                self.data_table.set_item_text(row_position, 4, truck)
                
                # Actions
                self.data_table.add_action_buttons(row_position, 5, 
                                                 ["edit", "delete"], 
                                                 [self.edit_reception, self.delete_reception])
            
            # Mettre à jour les KPI
            self.update_kpi()
            
        except Exception as e:
            logging.error(f"Erreur lors du rafraîchissement des données: {str(e)}")
    
    def save_reception(self, data):
        """Enregistre une nouvelle réception."""
        try:
            # Préparer les données
            reception_data = {
                'step': ProcessStepEnum.RECEPTION,
                'movement_type': StockMovementType.RECEPTION,
                'quantity': float(data.get('quantity', 0)),
                'quality': float(data.get('quality', 0)),
                'timestamp': data.get('reception_date', datetime.now()),
                'supplier': data.get('supplier', 'SOTRAMINE'),
                'truck_number': data.get('truck_number', '')
            }
            
            # Enregistrer dans la base de données
            self.db_manager.add_stock_movement(**reception_data)
            
            # Rafraîchir les données
            self.refresh_data()
            
            # Message de confirmation
            QMessageBox.information(self, "Succès", "Réception enregistrée avec succès.")
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement de la réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'enregistrer la réception: {str(e)}")
    
    def edit_reception(self, row):
        """Édite une réception existante."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", f"Édition de la réception à la ligne {row+1}")
    
    def delete_reception(self, row):
        """Supprime une réception existante."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", f"Suppression de la réception à la ligne {row+1}")
    
    def import_from_excel(self):
        """Importe des réceptions depuis un fichier Excel."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", "Import Excel non implémenté")
    
    def export_detailed_report(self):
        """Exporte un rapport détaillé des réceptions."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", "Export de rapport non implémenté")