"""
Onglet réception compact avec indicateurs de performance bien dimensionnés et lisibles.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QFileDialog, QProgressBar, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging
import pandas as pd

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import ActionButton
from ui.widgets.compact_kpi_widgets import (CompactKPICard, CompactKPISection, 
                                          CompactIndicator, KPITabWidget)
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class CompactReceptionTab(StandardizedBaseTab):
    """Onglet réception compact avec indicateurs de performance bien dimensionnés et lisibles."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📥 Réception Phosphate", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface optimisée."""
        # Remplacer le layout KPI standard par notre section KPI optimisée
        self.kpi_group.setTitle("📊 Performance de Réception")
        
        # Supprimer le layout existant et créer un nouveau
        self.kpi_layout.removeItem(self.kpi_cards_layout)
        
        # Créer un widget avec onglets pour les différentes catégories de KPI
        self.kpi_tabs = QTabWidget()
        self.kpi_tabs.setTabPosition(QTabWidget.North)
        self.kpi_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: transparent;
            }
            QTabBar::tab {
                padding: 5px 10px;
                margin-right: 2px;
                font-size: 9pt;
            }
        """)
        
        # Onglet Volumes
        self.volumes_tab = KPITabWidget()
        
        # Section Stocks
        stocks_section = self.volumes_tab.add_section("Stocks")
        self.stock_card = stocks_section.add_kpi_card(
            "Stock Actuel", "0", "T", None, "Stock", "📦", SotramineTheme.PRIMARY)
        
        # Section Réceptions
        receptions_section = self.volumes_tab.add_section("Réceptions")
        self.today_card = receptions_section.add_kpi_card(
            "Reçu Aujourd'hui", "0", "T", None, "Jour", "📥", SotramineTheme.SECONDARY)
        self.week_card = receptions_section.add_kpi_card(
            "Cette Semaine", "0", "T", None, "Semaine", "📥", SotramineTheme.SECONDARY)
        self.month_card = receptions_section.add_kpi_card(
            "Ce Mois", "0", "T", None, "Mois", "📥", SotramineTheme.SECONDARY)
        
        # Section Tendances
        trends_section = self.volumes_tab.add_section("Tendances")
        self.daily_avg_card = trends_section.add_kpi_card(
            "Moyenne Journalière", "0", "T/j", None, "Moyenne", "📈", SotramineTheme.ACCENT)
        self.weekly_avg_card = trends_section.add_kpi_card(
            "Moyenne Hebdo", "0", "T/sem", None, "Moyenne", "📈", SotramineTheme.ACCENT)
        self.target_completion_card = trends_section.add_kpi_card(
            "Objectif Mensuel", "0", "%", None, "Objectif", "🎯", SotramineTheme.ACCENT)
        
        # Onglet Qualité
        self.quality_tab = KPITabWidget()
        
        # Section Indicateurs de qualité
        quality_indicators = self.quality_tab.add_indicators_section("Indicateurs de Qualité")
        
        # Indicateur de qualité P2O5
        self.p2o5_indicator = CompactIndicator(
            "Qualité P2O5", 0, 0, 40, 30, "%", SotramineTheme.PRIMARY)
        quality_indicators.addWidget(self.p2o5_indicator)
        
        # Indicateur de conformité
        self.conformity_indicator = CompactIndicator(
            "Conformité", 0, 0, 100, 95, "%", SotramineTheme.SECONDARY)
        quality_indicators.addWidget(self.conformity_indicator)
        
        # Section Statistiques de qualité
        quality_stats = self.quality_tab.add_section("Statistiques de Qualité")
        self.avg_quality_card = quality_stats.add_kpi_card(
            "Qualité Moyenne", "0", "%", None, "P2O5", "🔍", SotramineTheme.PRIMARY)
        self.min_quality_card = quality_stats.add_kpi_card(
            "Qualité Min", "0", "%", None, "P2O5", "⬇️", SotramineTheme.DANGER)
        self.max_quality_card = quality_stats.add_kpi_card(
            "Qualité Max", "0", "%", None, "P2O5", "⬆️", SotramineTheme.SECONDARY)
        self.std_dev_card = quality_stats.add_kpi_card(
            "Écart Type", "0", "", None, "Statistique", "📊", SotramineTheme.ACCENT)
        
        # Onglet Logistique
        self.logistics_tab = KPITabWidget()
        
        # Section Transport
        transport_section = self.logistics_tab.add_section("Transport")
        self.trucks_card = transport_section.add_kpi_card(
            "Camions Aujourd'hui", "0", "", None, "Camions", "🚚", SotramineTheme.PRIMARY)
        self.avg_load_card = transport_section.add_kpi_card(
            "Charge Moyenne", "0", "T", None, "Charge", "⚖️", SotramineTheme.PRIMARY)
        
        # Section Délais
        delays_section = self.logistics_tab.add_section("Délais")
        self.waiting_time_card = delays_section.add_kpi_card(
            "Temps d'Attente", "0", "min", None, "Attente", "⏱️", SotramineTheme.ACCENT)
        self.processing_time_card = delays_section.add_kpi_card(
            "Temps de Traitement", "0", "min", None, "Traitement", "⏱️", SotramineTheme.ACCENT)
        
        # Section Fournisseurs
        suppliers_section = self.logistics_tab.add_section("Fournisseurs")
        self.suppliers_card = suppliers_section.add_kpi_card(
            "Fournisseurs Actifs", "0", "", None, "Nombre", "🏭", SotramineTheme.SECONDARY)
        self.main_supplier_card = suppliers_section.add_kpi_card(
            "Fournisseur Principal", "SOTRAMINE", "%", None, "Principal", "🏭", SotramineTheme.SECONDARY)
        
        # Ajouter les onglets
        self.kpi_tabs.addTab(self.volumes_tab, "📊 Volumes")
        self.kpi_tabs.addTab(self.quality_tab, "🔍 Qualité")
        self.kpi_tabs.addTab(self.logistics_tab, "🚚 Logistique")
        
        self.kpi_layout.addWidget(self.kpi_tabs)
        
        # Section Saisie Manuelle (zone gauche)
        form_layout = self.add_form_section("📝 Saisie Réception")
        
        fields = {
            'quantity': {
                'label': 'Quantité (T):',
                'type': 'number',
                'placeholder': 'Ex: 125.50',
                'category': 'Réception'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2',
                'category': 'Qualité'
            },
            'reception_date': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'supplier': {
                'label': 'Fournisseur:',
                'type': 'combo',
                'options': ['SOTRAMINE', 'Fournisseur A', 'Fournisseur B', 'Autre'],
                'category': 'Réception'
            },
            'truck_number': {
                'label': 'N° Camion:',
                'type': 'text',
                'placeholder': 'Ex: CAM-123',
                'category': 'Transport'
            },
            'driver': {
                'label': 'Chauffeur:',
                'type': 'text',
                'placeholder': 'Nom du chauffeur',
                'category': 'Transport'
            },
            'arrival_time': {
                'label': 'Heure d\'arrivée:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'waiting_time': {
                'label': 'Temps d\'attente (min):',
                'type': 'number',
                'placeholder': 'Ex: 15',
                'category': 'Temps'
            },
            'verified': {
                'label': 'Vérifié:',
                'type': 'checkbox',
                'default': False,
                'category': 'Validation'
            },
            'notes': {
                'label': 'Notes:',
                'type': 'text',
                'placeholder': 'Commentaires optionnels',
                'category': 'Autres'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_reception)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche)
        actions_layout = self.add_actions_section("🔧 Actions Avancées")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Importer", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter Rapport", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau des réceptions (zone droite)
        headers = ["Date/Heure", "Quantité (T)", "Qualité P2O5 (%)", "Fournisseur", 
                  "N° Camion", "Chauffeur", "Temps d'attente", "Vérifié", "Actions"]
        self.data_table = self.add_data_table("📋 Historique des Réceptions", headers, sortable=True, paginated=True)
        
        # Masquer certaines colonnes par défaut pour ne pas surcharger l'affichage
        self.data_table.setColumnHidden(6, True)  # Temps d'attente
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les indicateurs de performance."""
        try:
            # Dates de référence
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_week = today - timedelta(days=today.weekday())
            start_of_month = today.replace(day=1)
            
            # Stock actuel
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_card.update_value(f"{current_stock:.1f}")
            
            # Réception aujourd'hui
            today_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=today
            )
            today_total = sum(m['quantity'] for m in today_movements)
            self.today_card.update_value(f"{today_total:.1f}")
            
            # Cette semaine
            week_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_week
            )
            week_total = sum(m['quantity'] for m in week_movements)
            self.week_card.update_value(f"{week_total:.1f}")
            
            # Ce mois
            month_movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_month
            )
            month_total = sum(m['quantity'] for m in month_movements)
            self.month_card.update_value(f"{month_total:.1f}")
            
            # Moyenne journalière (sur le mois)
            days_in_month = (datetime.now() - start_of_month).days + 1
            daily_avg = month_total / days_in_month if days_in_month > 0 else 0
            self.daily_avg_card.update_value(f"{daily_avg:.1f}")
            
            # Moyenne hebdomadaire (sur le mois)
            weeks_in_month = days_in_month / 7
            weekly_avg = month_total / weeks_in_month if weeks_in_month > 0 else 0
            self.weekly_avg_card.update_value(f"{weekly_avg:.1f}")
            
            # Objectif mensuel (simulé pour l'exemple)
            monthly_target = 5000  # Tonnes
            target_completion = (month_total / monthly_target * 100) if monthly_target > 0 else 0
            self.target_completion_card.update_value(f"{target_completion:.1f}")
            
            # Qualité moyenne
            qualities = [m['quality'] for m in month_movements if m['quality']]
            avg_quality = sum(qualities) / len(qualities) if qualities else 0
            self.avg_quality_card.update_value(f"{avg_quality:.1f}")
            
            # Qualité min/max
            min_quality = min(qualities) if qualities else 0
            max_quality = max(qualities) if qualities else 0
            self.min_quality_card.update_value(f"{min_quality:.1f}")
            self.max_quality_card.update_value(f"{max_quality:.1f}")
            
            # Écart type (simulé pour l'exemple)
            std_dev = 1.8
            self.std_dev_card.update_value(f"{std_dev:.1f}")
            
            # Indicateurs visuels de qualité
            self.p2o5_indicator.update_value(avg_quality)
            
            # Conformité (simulée pour l'exemple)
            conformity = 96.5  # Pourcentage
            self.conformity_indicator.update_value(conformity)
            
            # Indicateurs de logistique (simulés pour l'exemple)
            trucks_count = len(today_movements)
            self.trucks_card.update_value(f"{trucks_count}")
            
            avg_load = today_total / trucks_count if trucks_count > 0 else 0
            self.avg_load_card.update_value(f"{avg_load:.1f}")
            
            waiting_time = 22  # Minutes
            self.waiting_time_card.update_value(f"{waiting_time}")
            
            processing_time = 15  # Minutes
            self.processing_time_card.update_value(f"{processing_time}")
            
            suppliers_count = 3  # Nombre de fournisseurs
            self.suppliers_card.update_value(f"{suppliers_count}")
            
            main_supplier_percentage = 85  # Pourcentage
            self.main_supplier_card.update_value(f"SOTRAMINE", f"{main_supplier_percentage}")
            
            # Mettre à jour la barre d'état
            self.update_status(f"Dernière mise à jour: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI réception: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données avec pagination."""
        try:
            self.update_status("Chargement des données...")
            
            # Récupérer les paramètres de pagination
            pagination = self.get_pagination_params()
            
            # Dernières réceptions (avec pagination)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 derniers jours par défaut
            
            # Récupérer le nombre total d'éléments pour la pagination
            total_count = self.db_manager.get_stock_movements_count(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date
            )
            self.total_items = total_count
            
            # Récupérer les données paginées
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date,
                limit=pagination['limit'],
                offset=pagination['offset'],
                order_by='date',
                order_direction='DESC'  # Plus récent en haut
            )
            
            self.populate_table(movements)
            self.update_status(f"Affichage de {len(movements)} enregistrements sur {total_count}")
            
        except Exception as e:
            logging.error(f"Erreur actualisation données réception: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def populate_table(self, movements):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(movements))
        
        for row, movement in enumerate(movements):
            # Date/Heure
            self.data_table.setItem(row, 0, 
                self.data_table.itemClass()(movement['date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{movement['quantity']:.2f}"))
            
            # Qualité
            quality = f"{movement['quality']:.1f}" if movement['quality'] else "-"
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(quality))
            
            # Fournisseur (simulé pour l'exemple)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()("SOTRAMINE"))
            
            # N° Camion (simulé)
            self.data_table.setItem(row, 4,
                self.data_table.itemClass()(f"CAM-{row+100}"))
            
            # Chauffeur (simulé)
            self.data_table.setItem(row, 5,
                self.data_table.itemClass()("Chauffeur"))
            
            # Temps d'attente (simulé)
            self.data_table.setItem(row, 6,
                self.data_table.itemClass()(f"{10 + row % 30} min"))
            
            # Vérifié (simulé)
            self.data_table.setItem(row, 7,
                self.data_table.itemClass()("✓" if row % 2 == 0 else ""))
            
            # Actions
            self.data_table.setItem(row, 8,
                self.data_table.itemClass()("📝 Modifier | 🗑️ Supprimer"))
                
    def save_reception(self, data):
        """Sauvegarde une nouvelle réception."""
        try:
            # Validation des données
            if not data.get('quantity') or float(data['quantity']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive")
                return
                
            quantity = float(data['quantity'])
            quality = float(data.get('quality', 0)) if data.get('quality') else None
            reception_date = data.get('reception_date', datetime.now())
            supplier = data.get('supplier', 'SOTRAMINE')
            truck_number = data.get('truck_number', '')
            driver = data.get('driver', '')
            arrival_time = data.get('arrival_time', reception_date)
            waiting_time = float(data.get('waiting_time', 0)) if data.get('waiting_time') else 0
            verified = data.get('verified', False)
            notes = data.get('notes', '')
            
            # Préparer les métadonnées
            metadata = {
                'supplier': supplier,
                'truck_number': truck_number,
                'driver': driver,
                'arrival_time': arrival_time.isoformat() if isinstance(arrival_time, datetime) else arrival_time,
                'waiting_time': waiting_time,
                'verified': verified,
                'notes': notes
            }
            
            # Enregistrer la réception
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.RECEPTION,
                quantity=quantity,
                movement_type=StockMovementType.RECEPTION,
                movement_date=reception_date,
                quality=quality,
                metadata=metadata
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            # Message de confirmation
            QMessageBox.information(self, "✅ Succès", 
                f"Réception de {quantity:.1f}T enregistrée avec succès")
                
        except ValueError as e:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
            
    def import_from_excel(self):
        """Importe des données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "Importer fichier Excel", 
                "", 
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['Quantité', 'Date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "Erreur", 
                    f"Colonnes manquantes: {', '.join(missing_columns)}")
                return
                
            # Progress bar
            progress = QProgressBar()
            progress.setMaximum(len(df))
            progress.show()
            
            imported_count = 0
            
            for index, row in df.iterrows():
                try:
                    quantity = float(row['Quantité'])
                    date = pd.to_datetime(row['Date']).to_pydatetime()
                    quality = float(row.get('Qualité', 0)) if pd.notna(row.get('Qualité')) else None
                    supplier = row.get('Fournisseur', 'SOTRAMINE')
                    truck_number = row.get('N° Camion', '')
                    driver = row.get('Chauffeur', '')
                    
                    metadata = {
                        'supplier': supplier,
                        'truck_number': truck_number,
                        'driver': driver
                    }
                    
                    self.db_manager.add_stock_movement(
                        step=ProcessStepEnum.RECEPTION,
                        quantity=quantity,
                        movement_type=StockMovementType.RECEPTION,
                        movement_date=date,
                        quality=quality,
                        metadata=metadata
                    )
                    
                    imported_count += 1
                    progress.setValue(index + 1)
                    
                except Exception as e:
                    logging.warning(f"Erreur ligne {index + 1}: {str(e)}")
                    continue
                    
            progress.close()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Import Terminé", 
                f"{imported_count} réceptions importées avec succès")
                
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
            
    def export_detailed_report(self):
        """Exporte un rapport détaillé avec graphiques et analyses."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter Rapport Détaillé",
                f"rapport_receptions_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            self.update_status("Génération du rapport détaillé...")
            
            # Récupérer les données des 30 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date,
                limit=1000  # Nombre élevé pour avoir toutes les données
            )
            
            # Créer un DataFrame avec les données
            data = []
            for movement in movements:
                data.append({
                    'Date': movement['date'],
                    'Quantité (T)': movement['quantity'],
                    'Qualité P2O5 (%)': movement['quality'] if movement['quality'] else 0,
                    'Fournisseur': 'SOTRAMINE'  # Simulé
                })
                
            df = pd.DataFrame(data)
            
            # Créer un writer Excel avec xlsxwriter
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # Écrire les données brutes
            df.to_excel(writer, sheet_name='Données', index=False)
            
            # Créer une feuille de résumé
            summary = pd.DataFrame({
                'Métrique': [
                    'Total Reçu (T)',
                    'Qualité Moyenne (%)',
                    'Nombre de Réceptions',
                    'Réception Moyenne (T)',
                    'Réception Max (T)',
                    'Réception Min (T)'
                ],
                'Valeur': [
                    df['Quantité (T)'].sum(),
                    df['Qualité P2O5 (%)'].mean(),
                    len(df),
                    df['Quantité (T)'].mean(),
                    df['Quantité (T)'].max(),
                    df['Quantité (T)'].min()
                ]
            })
            
            summary.to_excel(writer, sheet_name='Résumé', index=False)
            
            # Sauvegarder et fermer
            writer.save()
            
            self.update_status(f"Rapport détaillé exporté vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Rapport détaillé exporté vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export rapport: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export du rapport: {str(e)}")