# Standard library imports
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

# Third-party imports
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPalette, QColor, QFont
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QGroupBox,
                             QMessageBox, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QComboBox, QFrame)

# Local imports
from models.enums import ProcessStepEnum, StockMovementType

# Constantes pour les seuils de stock
STOCK_LOW_THRESHOLD = 100
STOCK_CRITICAL_THRESHOLD = 0

class StockTab(QWidget):
    """Onglet de visualisation des stocks."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.setup_timers()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Groupe des stocks actuels avec mise en forme améliorée
        current_stock_group = QGroupBox("État des stocks actuels")
        current_stock_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        current_stock_layout = QGridLayout()
        current_stock_layout.setSpacing(20)
        
        # Titres des colonnes
        title_font = QFont()
        title_font.setBold(True)
        
        title_label = QLabel("Étape")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(title_label, 0, 0)
        
        title_label = QLabel("Stock actuel")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(title_label, 0, 1)
        
        title_label = QLabel("Qualité P2O5")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(title_label, 0, 2)
        
        title_label = QLabel("Description")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(title_label, 0, 3)
        
        # Ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        current_stock_layout.addWidget(line, 1, 0, 1, 4)
        
        # Stock de réception
        reception_label = QLabel("Réception")
        reception_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(reception_label, 2, 0)
        
        self.reception_stock_label = QLabel("0.00 tonnes")
        self.reception_stock_label.setObjectName("reception_stock_label")
        self.reception_stock_label.setAlignment(Qt.AlignCenter)
        self.reception_stock_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        current_stock_layout.addWidget(self.reception_stock_label, 2, 1)
        
        # Qualité P2O5 réception
        self.reception_quality_label = QLabel("-")
        self.reception_quality_label.setAlignment(Qt.AlignCenter)
        self.reception_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #0078d7;")
        current_stock_layout.addWidget(self.reception_quality_label, 2, 2)
        
        # Description du stock de réception
        reception_desc = QLabel("Phosphate brut (reste en réception)")
        reception_desc.setAlignment(Qt.AlignCenter)
        reception_desc.setStyleSheet("font-style: italic; color: #666;")
        current_stock_layout.addWidget(reception_desc, 2, 3)
        
        # Stock de concassage
        crushing_label = QLabel("Concassage")
        crushing_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(crushing_label, 3, 0)
        
        self.crushing_stock_label = QLabel("0.00 tonnes")
        self.crushing_stock_label.setObjectName("crushing_stock_label")
        self.crushing_stock_label.setAlignment(Qt.AlignCenter)
        self.crushing_stock_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        current_stock_layout.addWidget(self.crushing_stock_label, 3, 1)
        
        # Qualité P2O5 concassage
        self.crushing_quality_label = QLabel("-")
        self.crushing_quality_label.setAlignment(Qt.AlignCenter)
        self.crushing_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #0078d7;")
        current_stock_layout.addWidget(self.crushing_quality_label, 3, 2)
        
        # Description du stock de concassage
        crushing_desc = QLabel("Minerai concassé disponible pour laverie")
        crushing_desc.setAlignment(Qt.AlignCenter)
        crushing_desc.setStyleSheet("font-style: italic; color: #666;")
        current_stock_layout.addWidget(crushing_desc, 3, 3)
        
        # Stock total concassage (pour information)
        crushing_total_label = QLabel("Stock total concassage")
        crushing_total_label.setAlignment(Qt.AlignCenter)
        crushing_total_label.setStyleSheet("font-size: 12px; color: #888;")
        current_stock_layout.addWidget(crushing_total_label, 4, 0)
        
        self.crushing_total_stock_label = QLabel("0.00 tonnes")
        self.crushing_total_stock_label.setAlignment(Qt.AlignCenter)
        self.crushing_total_stock_label.setStyleSheet("font-size: 12px; color: #888;")
        current_stock_layout.addWidget(self.crushing_total_stock_label, 4, 1)
        
        # Qualité moyenne du stock total (vide pour cette ligne)
        crushing_total_quality = QLabel("-")
        crushing_total_quality.setAlignment(Qt.AlignCenter)
        crushing_total_quality.setStyleSheet("font-size: 12px; color: #888;")
        current_stock_layout.addWidget(crushing_total_quality, 4, 2)
        
        # Description du stock total
        crushing_total_desc = QLabel("(Brut + Concassé)")
        crushing_total_desc.setAlignment(Qt.AlignCenter)
        crushing_total_desc.setStyleSheet("font-style: italic; color: #888; font-size: 12px;")
        current_stock_layout.addWidget(crushing_total_desc, 4, 3)
        
        # Stock de laverie
        laverie_label = QLabel("Laverie")
        laverie_label.setAlignment(Qt.AlignCenter)
        current_stock_layout.addWidget(laverie_label, 5, 0)
        
        self.laverie_stock_label = QLabel("0.00 tonnes")
        self.laverie_stock_label.setObjectName("laverie_stock_label")
        self.laverie_stock_label.setAlignment(Qt.AlignCenter)
        self.laverie_stock_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        current_stock_layout.addWidget(self.laverie_stock_label, 5, 1)
        
        # Qualité P2O5 laverie
        self.laverie_quality_label = QLabel("-")
        self.laverie_quality_label.setAlignment(Qt.AlignCenter)
        self.laverie_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #0078d7;")
        current_stock_layout.addWidget(self.laverie_quality_label, 5, 2)
        
        # Description du stock de laverie
        laverie_desc = QLabel("Phosphate lavé (qualité P2O5)")
        laverie_desc.setAlignment(Qt.AlignCenter)
        laverie_desc.setStyleSheet("font-style: italic; color: #666;")
        current_stock_layout.addWidget(laverie_desc, 5, 3)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("Rafraîchir les stocks")
        refresh_button.clicked.connect(self.update_stocks)
        current_stock_layout.addWidget(refresh_button, 6, 0, 1, 4)
        
        current_stock_group.setLayout(current_stock_layout)
        main_layout.addWidget(current_stock_group)
        
        # Groupe des mouvements de stock
        history_group = QGroupBox("Historique des mouvements de stock")
        history_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        history_layout = QVBoxLayout()
        
        # Filtres
        filters_layout = QHBoxLayout()
        filters_layout.setSpacing(15)
        
        # Filtre par étape
        filters_layout.addWidget(QLabel("<b>Étape:</b>"))
        self.filter_step_combo = QComboBox()
        self.filter_step_combo.addItem("Toutes les étapes")
        self.filter_step_combo.addItems([step.value for step in ProcessStepEnum])
        self.filter_step_combo.currentTextChanged.connect(self.refresh_movements)
        self.filter_step_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.filter_step_combo)
        
        # Filtre par type
        filters_layout.addWidget(QLabel("<b>Type:</b>"))
        self.filter_type_combo = QComboBox()
        self.filter_type_combo.addItem("Tous les types")
        self.filter_type_combo.addItems([type.value for type in StockMovementType])
        self.filter_type_combo.currentTextChanged.connect(self.refresh_movements)
        self.filter_type_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.filter_type_combo)
        
        # Filtre par date
        filters_layout.addWidget(QLabel("<b>Période:</b>"))
        self.filter_date_combo = QComboBox()
        self.filter_date_combo.addItems([
            "Aujourd'hui",
            "Cette semaine",
            "Ce mois",
            "Toutes les dates"
        ])
        self.filter_date_combo.currentTextChanged.connect(self.refresh_movements)
        self.filter_date_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.filter_date_combo)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("Rafraîchir")
        refresh_button.clicked.connect(self.refresh_movements)
        filters_layout.addWidget(refresh_button)
        
        filters_layout.addStretch()
        history_layout.addLayout(filters_layout)
        
        # Ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        history_layout.addWidget(line)
        
        # Table des mouvements
        self.movement_table = QTableWidget()
        self.movement_table.setColumnCount(5)
        self.movement_table.setHorizontalHeaderLabels([
            "Date", "Étape", "Type", "Quantité (t)", "Qualité (%)"
        ])
        self.movement_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.movement_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.movement_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.movement_table.setAlternatingRowColors(True)
        self.movement_table.verticalHeader().setVisible(False)
        self.movement_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #e0e0ff;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                font-weight: bold;
                border: 1px solid #d0d0d0;
            }
        """)
        history_layout.addWidget(self.movement_table)
        
        history_group.setLayout(history_layout)
        main_layout.addWidget(history_group)
        
        self.setLayout(main_layout)
        
        # Charger les données initiales
        self.update_stocks()
        self.refresh_movements()
    
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques - DÉSACTIVÉ."""
        # Timer pour rafraîchir les stocks - DÉSACTIVÉ
        self.stock_timer = QTimer()
        self.stock_timer.timeout.connect(self.update_stocks)
        # self.stock_timer.start(30000)  # DÉSACTIVÉ
    
    def refresh_movements(self):
        """Rafraîchit la table des mouvements de stock."""
        try:
            self.movement_table.setRowCount(0)
            
            # Récupérer les filtres
            step = self.filter_step_combo.currentText()
            movement_type = self.filter_type_combo.currentText()
            period = self.filter_date_combo.currentText()
            
            # Calculer la date de début selon la période
            start_date = None
            if period == "Aujourd'hui":
                start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "Cette semaine":
                start_date = datetime.now() - timedelta(days=datetime.now().weekday())
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "Ce mois":
                start_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # Récupérer les mouvements
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum(step) if step != "Toutes les étapes" else None,
                movement_type=StockMovementType(movement_type) if movement_type != "Tous les types" else None,
                start_date=start_date
            )
            
            # Définir des couleurs pour les différents types de mouvements
            color_map = {
                "Réception": QColor(230, 255, 230),  # Vert clair
                "Production": QColor(230, 230, 255),  # Bleu clair
                "Consommation": QColor(255, 230, 230),  # Rouge clair
                "Transfert": QColor(255, 255, 230),  # Jaune clair
                "Ajustement": QColor(255, 230, 255)   # Violet clair
            }
            
            for movement in movements:
                row_position = self.movement_table.rowCount()
                self.movement_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(movement['date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setItem(row_position, 0, date_item)
                
                # Étape
                step_item = QTableWidgetItem(movement['step'])
                step_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setItem(row_position, 1, step_item)
                
                # Type
                type_item = QTableWidgetItem(movement['movement_type'])
                type_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setItem(row_position, 2, type_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{movement['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setItem(row_position, 3, quantity_item)
                
                # Qualité
                quality_value = movement.get('quality', 0)
                quality_text = f"{quality_value:.2f}" if quality_value else "-"
                quality_item = QTableWidgetItem(quality_text)
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setItem(row_position, 4, quality_item)
                
                # Appliquer une couleur de fond selon le type de mouvement
                if movement['movement_type'] in color_map:
                    for col in range(5):
                        item = self.movement_table.item(row_position, col)
                        if item:
                            item.setBackground(color_map[movement['movement_type']])
                
            # Afficher un message si aucun mouvement n'est trouvé
            if self.movement_table.rowCount() == 0:
                self.movement_table.setRowCount(1)
                no_data_item = QTableWidgetItem("Aucun mouvement de stock trouvé pour les critères sélectionnés")
                no_data_item.setTextAlignment(Qt.AlignCenter)
                self.movement_table.setSpan(0, 0, 1, 5)
                self.movement_table.setItem(0, 0, no_data_item)
                
        except Exception as e:
            logging.error(f"Erreur lors du chargement des mouvements: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
    
    def update_stocks(self):
        """Met à jour l'affichage des stocks actuels."""
        try:
            # Stock de réception avec qualité
            reception_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.RECEPTION)
            reception_stock = reception_data['stock']
            reception_quality = reception_data['quality']
            
            self.reception_stock_label.setText(f"{reception_stock:.1f} tonnes")
            
            # Afficher la qualité P2O5 pour réception
            if reception_quality is not None:
                self.reception_quality_label.setText(f"{reception_quality:.1f}%")
            else:
                self.reception_quality_label.setText("-")
            
            # Appliquer une couleur selon le niveau de stock
            if reception_stock <= STOCK_CRITICAL_THRESHOLD:
                self.reception_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: red;")
            elif reception_stock < STOCK_LOW_THRESHOLD:
                self.reception_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: orange;")
            else:
                self.reception_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: green;")
            
            # Stock de concassage avec qualité
            crushing_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.CONCASSAGE)
            crushing_stock = crushing_data['stock']
            crushing_quality = crushing_data['quality']
            
            # Pour l'affichage du stock disponible pour laverie, on utilise encore l'ancienne méthode
            crushing_available = self.db_manager.get_available_crushing_stock()
            self.crushing_stock_label.setText(f"{crushing_available:.1f} tonnes")
            
            # Afficher la qualité P2O5 pour concassage
            if crushing_quality is not None:
                self.crushing_quality_label.setText(f"{crushing_quality:.1f}%")
            else:
                self.crushing_quality_label.setText("-")
            
            # Appliquer une couleur selon le niveau de stock
            if crushing_available <= STOCK_CRITICAL_THRESHOLD:
                self.crushing_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: red;")
            elif crushing_available < STOCK_LOW_THRESHOLD:
                self.crushing_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: orange;")
            else:
                self.crushing_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: green;")
            
            # Stock total concassage (pour information)
            self.crushing_total_stock_label.setText(f"{crushing_stock:.1f} tonnes")
            
            # Stock de laverie avec qualité
            laverie_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.LAVERIE)
            laverie_stock = laverie_data['stock']
            laverie_quality = laverie_data['quality']
            
            self.laverie_stock_label.setText(f"{laverie_stock:.1f} tonnes")
            
            # Afficher la qualité P2O5 pour laverie
            if laverie_quality is not None:
                self.laverie_quality_label.setText(f"{laverie_quality:.1f}%")
                # Colorer selon la qualité
                if laverie_quality >= 30:
                    self.laverie_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: green;")
                elif laverie_quality >= 25:
                    self.laverie_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: orange;")
                else:
                    self.laverie_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: red;")
            else:
                self.laverie_quality_label.setText("-")
                self.laverie_quality_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #888;")
            
            # Appliquer une couleur selon le niveau de stock
            if laverie_stock <= STOCK_CRITICAL_THRESHOLD:
                self.laverie_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: red;")
            elif laverie_stock < STOCK_LOW_THRESHOLD:
                self.laverie_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: orange;")
            else:
                self.laverie_stock_label.setStyleSheet("font-weight: bold; font-size: 16px; color: green;")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des stocks: {str(e)}")
            self.reception_stock_label.setText("Erreur")
            self.reception_quality_label.setText("Erreur")
            self.crushing_stock_label.setText("Erreur")
            self.crushing_quality_label.setText("Erreur")
            self.laverie_stock_label.setText("Erreur")
            self.laverie_quality_label.setText("Erreur")
            
    def refresh_tab(self):
        """Rafraîchit l'onglet lorsqu'il est sélectionné."""
        self.update_stocks()
        self.refresh_movements()