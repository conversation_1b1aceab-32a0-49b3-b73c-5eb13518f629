# Standard library imports
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

# Third-party imports
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QGroupBox,
                             QMessageBox, QDateTimeEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView)

# Local imports
from models.enums import ProcessStepEnum

class CrushingTab(QWidget):
    """Onglet de gestion du concassage."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.setup_timers()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Groupe de saisie des données de production
        input_group = QGroupBox("Saisie des données de production")
        input_layout = QFormLayout()
        input_layout.setSpacing(10)
        
        # Date de production
        self.production_date = QDateTimeEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDateTime(QDateTime.currentDateTime())
        input_layout.addRow("Date de production:", self.production_date)
        
        # Heures de production
        self.production_hours_input = QLineEdit()
        self.production_hours_input.setPlaceholderText("Heures de production")
        input_layout.addRow("Heures de production:", self.production_hours_input)
        
        # Quantité produite
        self.quantity_produced_input = QLineEdit()
        self.quantity_produced_input.setPlaceholderText("Quantité produite (tonnes)")
        input_layout.addRow("Quantité produite:", self.quantity_produced_input)
        
        # Qualité
        self.quality_input = QLineEdit()
        self.quality_input.setPlaceholderText("Qualité P2O5 (%)")
        input_layout.addRow("Qualité P2O5 (%):", self.quality_input)
        
        # Quantité utilisée
        self.quantity_used_input = QLineEdit()
        self.quantity_used_input.setPlaceholderText("Quantité utilisée (tonnes)")
        input_layout.addRow("Quantité utilisée:", self.quantity_used_input)
        
        # Bouton d'ajout
        add_button = QPushButton("Ajouter production")
        add_button.clicked.connect(self.add_production)
        input_layout.addRow("", add_button)
        
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)
        
        # Groupe de saisie des arrêts
        downtime_group = QGroupBox("Saisie des arrêts")
        downtime_layout = QFormLayout()
        downtime_layout.setSpacing(10)
        
        # Date d'arrêt
        self.downtime_date = QDateTimeEdit()
        self.downtime_date.setCalendarPopup(True)
        self.downtime_date.setDateTime(QDateTime.currentDateTime())
        downtime_layout.addRow("Date d'arrêt:", self.downtime_date)
        
        # Durée de l'arrêt
        self.downtime_duration = QLineEdit()
        self.downtime_duration.setPlaceholderText("Durée en minutes")
        downtime_layout.addRow("Durée (min):", self.downtime_duration)
        
        # Raison de l'arrêt
        self.downtime_reason = QLineEdit()
        self.downtime_reason.setPlaceholderText("Raison de l'arrêt")
        downtime_layout.addRow("Raison:", self.downtime_reason)
        
        # Bouton d'ajout d'arrêt
        add_downtime_button = QPushButton("Ajouter arrêt")
        add_downtime_button.clicked.connect(self.add_downtime)
        downtime_layout.addRow("", add_downtime_button)
        
        downtime_group.setLayout(downtime_layout)
        main_layout.addWidget(downtime_group)
        
        # Groupe des indicateurs
        indicators_group = QGroupBox("Indicateurs")
        indicators_layout = QHBoxLayout()
        
        # Stock disponible
        self.available_stock_label = QLabel("Stock disponible: 0.00 tonnes")
        self.available_stock_label.setObjectName("available_stock_label")
        indicators_layout.addWidget(self.available_stock_label)
        
        # Productivité
        self.productivity_label = QLabel("Productivité: 0.00 t/h")
        self.productivity_label.setObjectName("productivity_label")
        indicators_layout.addWidget(self.productivity_label)
        
        # Rendement
        self.yield_label = QLabel("Rendement: 0.00%")
        self.yield_label.setObjectName("yield_label")
        indicators_layout.addWidget(self.yield_label)
        
        # Disponibilité
        self.availability_label = QLabel("Disponibilité: 0.00%")
        self.availability_label.setObjectName("availability_label")
        indicators_layout.addWidget(self.availability_label)
        
        indicators_group.setLayout(indicators_layout)
        main_layout.addWidget(indicators_group)
        
        # Tables de données
        tables_group = QGroupBox("Données")
        tables_layout = QVBoxLayout()
        
        # Table de production
        self.production_table = QTableWidget()
        self.production_table.setColumnCount(5)
        self.production_table.setHorizontalHeaderLabels(["Date", "Heures", "Quantité (t)", "Qualité (%)", "Action"])
        self.production_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.production_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.production_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        tables_layout.addWidget(self.production_table)
        
        # Table d'arrêts
        self.downtime_table = QTableWidget()
        self.downtime_table.setColumnCount(4)
        self.downtime_table.setHorizontalHeaderLabels(["Date", "Durée (min)", "Raison", "Action"])
        self.downtime_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.downtime_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.downtime_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        tables_layout.addWidget(self.downtime_table)
        
        tables_group.setLayout(tables_layout)
        main_layout.addWidget(tables_group)
        
        self.setLayout(main_layout)
    
    def setup_timers(self):
        """Configure les timers pour la mise à jour automatique."""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_tab)
        self.update_timer.start(10000)  # Mise à jour toutes les 10 secondes
    
    def add_production(self):
        """Ajoute une nouvelle production."""
        try:
            # Récupérer et valider les valeurs
            date = self.production_date.dateTime().toPyDateTime()
            hours = self._get_float_value(self.production_hours_input, "heures de production")
            quantity = self._get_float_value(self.quantity_produced_input, "quantité produite")
            quality = self._get_float_value(self.quality_input, "qualité")
            quantity_used = self._get_float_value(self.quantity_used_input, "quantité utilisée")
            
            # Ajouter la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity,
                quality=quality,
                quantity_used=quantity_used,
                production_hours=hours,
                production_date=date
            )
            
            QMessageBox.information(self, "Succès", "Production ajoutée avec succès.")
            self.clear_production_inputs()
            self.refresh_tab()
            
        except ValueError as e:
            QMessageBox.warning(self, "Erreur de saisie", str(e))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la production: {str(e)}")
    
    def add_downtime(self):
        """Ajoute un nouvel arrêt de production."""
        try:
            # Récupérer et valider les valeurs
            date = self.downtime_date.dateTime().toPyDateTime()
            duration = self._get_float_value(self.downtime_duration, "durée")
            reason = self.downtime_reason.text().strip()
            
            if not reason:
                raise ValueError("La raison de l'arrêt est obligatoire")
            
            # Ajouter l'arrêt
            self.db_manager.add_downtime(
                step=ProcessStepEnum.CONCASSAGE,
                reason=reason,
                duration=duration,
                downtime_date=date
            )
            
            QMessageBox.information(self, "Succès", "Arrêt ajouté avec succès.")
            self.clear_downtime_inputs()
            self.refresh_tab()
            
        except ValueError as e:
            QMessageBox.warning(self, "Erreur de saisie", str(e))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de l'arrêt: {str(e)}")
    
    def _get_float_value(self, input_widget: QLineEdit, field_name: str) -> float:
        """Récupère et valide une valeur flottante depuis un widget de saisie."""
        value_str = input_widget.text().strip()
        if not value_str:
            raise ValueError(f"Le champ '{field_name}' est obligatoire")
        try:
            value = float(value_str)
            if value <= 0:
                raise ValueError(f"La valeur de '{field_name}' doit être positive")
            return value
        except ValueError:
            raise ValueError(f"La valeur de '{field_name}' doit être un nombre valide")
    
    def clear_production_inputs(self):
        """Efface les champs de saisie de production."""
        self.production_date.setDateTime(QDateTime.currentDateTime())
        self.production_hours_input.clear()
        self.quantity_produced_input.clear()
        self.quality_input.clear()
        self.quantity_used_input.clear()
    
    def clear_downtime_inputs(self):
        """Efface les champs de saisie d'arrêt."""
        self.downtime_date.setDateTime(QDateTime.currentDateTime())
        self.downtime_duration.clear()
        self.downtime_reason.clear()
    
    def refresh_tab(self):
        """Rafraîchit toutes les données de l'onglet."""
        try:
            self.load_production_data()
            self.load_downtime_data()
            self.update_indicators()
        except Exception as e:
            logging.error(f"Erreur lors du rafraîchissement de l'onglet: {str(e)}")

    def load_production_data(self):
        """Charge les données de production depuis la base de données."""
        try:
            self.production_table.setRowCount(0)
            productions = self.db_manager.get_production_summary(ProcessStepEnum.CONCASSAGE)
            
            for production in productions:
                row_position = self.production_table.rowCount()
                self.production_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(production['production_date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 0, date_item)
                
                # Heures
                hours_value = production['production_hours']
                hours_item = QTableWidgetItem(f"{hours_value:.2f}" if hours_value is not None else "N/A")
                hours_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 1, hours_item)
                
                # Quantité
                quantity_item = QTableWidgetItem(f"{production['quantity']:.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 2, quantity_item)
                
                # Qualité
                quality_value = production['quality']
                quality_item = QTableWidgetItem(quality_value if quality_value else "N/A")
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.production_table.setItem(row_position, 3, quality_item)
                
                # Bouton de suppression
                delete_btn = QPushButton("Supprimer")
                delete_btn.clicked.connect(lambda checked, prod_id=production['id']: self.delete_production_row(prod_id))
                self.production_table.setCellWidget(row_position, 4, delete_btn)
                
        except Exception as e:
            logging.error(f"Erreur lors du chargement des données de production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
    
    def load_downtime_data(self):
        """Charge les données d'arrêt depuis la base de données."""
        try:
            self.downtime_table.setRowCount(0)
            downtimes = self.db_manager.get_downtime_summary(ProcessStepEnum.CONCASSAGE)
            
            for downtime in downtimes:
                row_position = self.downtime_table.rowCount()
                self.downtime_table.insertRow(row_position)
                
                # Date
                date_item = QTableWidgetItem(downtime['downtime_date'].strftime("%d/%m/%Y %H:%M"))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 0, date_item)
                
                # Durée
                duration_item = QTableWidgetItem(f"{downtime['duration']:.0f}")
                duration_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 1, duration_item)
                
                # Raison
                reason_item = QTableWidgetItem(downtime['reason'])
                reason_item.setTextAlignment(Qt.AlignCenter)
                self.downtime_table.setItem(row_position, 2, reason_item)
                
                # Bouton de suppression
                delete_btn = QPushButton("Supprimer")
                delete_btn.clicked.connect(lambda checked, dt_id=downtime['id']: self.delete_downtime_row(dt_id))
                self.downtime_table.setCellWidget(row_position, 3, delete_btn)
                
        except Exception as e:
            logging.error(f"Erreur lors du chargement des données d'arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
    
    def update_indicators(self):
        """Met à jour les indicateurs de performance."""
        try:
            # Mettre à jour le stock disponible en premier
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.available_stock_label.setText(f"Stock disponible: {current_stock:.2f} tonnes")
            
            # Récupérer les autres données
            production_data = self.db_manager.get_production_summary(ProcessStepEnum.CONCASSAGE)
            downtime_data = self.db_manager.get_downtime_summary(ProcessStepEnum.CONCASSAGE)
            
            # Calculer la disponibilité
            total_hours = 8.0  # Heures de travail par jour
            total_stops = sum(d['duration'] for d in downtime_data) / 60 if downtime_data else 0
            availability = ((total_hours - total_stops) / total_hours) * 100 if total_hours > 0 else 0
            
            # Calculer le rendement
            total_produced = sum(p['quantity'] for p in production_data) if production_data else 0
            total_used = sum(p['quantity_used'] for p in production_data if p['quantity_used'] is not None) if production_data else 0
            yield_value = (total_produced / total_used * 100) if total_used > 0 else 0
            
            # Calculer la productivité (tonnes par heure)
            total_production_hours = sum(p['production_hours'] for p in production_data if p['production_hours'] is not None) if production_data else 0
            productivity = (total_produced / total_production_hours) if total_production_hours > 0 else 0
            
            # Mettre à jour les labels
            self.availability_label.setText(f"Disponibilité: {availability:.1f}%")
            self.yield_label.setText(f"Rendement: {yield_value:.1f}%")
            self.productivity_label.setText(f"Productivité: {productivity:.2f} t/h")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des indicateurs: {str(e)}")
            self.availability_label.setText("Erreur")
            self.productivity_label.setText("Erreur")
            self.available_stock_label.setText("Erreur")
    
    def _confirm_deletion(self, message: str) -> bool:
        """Affiche une boîte de dialogue de confirmation de suppression."""
        reply = QMessageBox.question(
            self, 'Confirmation', message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes

    def _handle_deletion_error(self, error: Exception, action: str):
        """Gère les erreurs de suppression."""
        logging.error(f"Erreur lors de {action}: {str(error)}")
        QMessageBox.critical(self, "Erreur", f"Erreur lors de {action}: {str(error)}")

    def delete_production_row(self, production_id: int):
        """Supprime une production par son ID."""
        try:
            if self._confirm_deletion("Voulez-vous vraiment supprimer cette production ?"):
                self.db_manager.delete_production(production_id)
                self.refresh_tab()
                QMessageBox.information(self, "Succès", "Production supprimée avec succès.")
        except Exception as e:
            self._handle_deletion_error(e, "la suppression de la production")
    
    def delete_downtime_row(self, downtime_id: int):
        """Supprime un arrêt par son ID."""
        try:
            if self._confirm_deletion("Voulez-vous vraiment supprimer cet arrêt ?"):
                self.db_manager.delete_downtime(downtime_id)
                self.refresh_tab()
                QMessageBox.information(self, "Succès", "Arrêt supprimé avec succès.")
        except Exception as e:
            self._handle_deletion_error(e, "la suppression de l'arrêt")
    
    def update_available_stock(self):
        """Met à jour l'affichage du stock disponible."""
        try:
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.available_stock_label.setText(f"{current_stock:.1f} tonnes")
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du stock: {str(e)}")
            self.available_stock_label.setText("Erreur")