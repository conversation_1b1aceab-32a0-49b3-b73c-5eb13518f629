# Phosphate Production Tracking App

## Build/Run Commands
- **Run app**: `python main.py` (main entry point with PyQt5 GUI)
- **Run tests**: No test runner configured - individual test files like `test_*.py` exist
- **Dependencies**: PyQt5, SQLAlchemy, pandas, matplotlib (no requirements.txt found)

## Architecture
- **Database**: SQLite (`production.db`) with SQLAlchemy ORM
- **UI**: PyQt5 desktop application with tabbed interface
- **Core**: MVC pattern with interfaces, models, database manager
- **Key directories**: `models/` (SQLAlchemy models), `ui/` (PyQt5 tabs), `database/` (DatabaseManager), `core/` (interfaces)

## Database Schema
- ProcessStep, Production, Downtime, StockMovement, ResourceConsumption, UnitHourCounter
- Process steps: RECEPTION, CONCASSAGE, LAVERIE, etc.
- Stock movements track material flow between process steps

## Code Style
- **Imports**: Group by type (datetime, typing, sqlalchemy, etc.), then local imports
- **Naming**: snake_case for variables/functions, PascalCase for classes
- **Error handling**: SQLAlchemy sessions with rollback in except blocks
- **Logging**: Use Python logging module extensively
- **Types**: Type hints using typing module (List, Optional, Dict, Any)
- **Docstrings**: French docstrings for methods and classes
