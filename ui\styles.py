"""
Module contenant les styles et thèmes pour l'application.
"""

# Palette de couleurs principale
PRIMARY_COLOR = "#1976D2"  # Bleu principal
SECONDARY_COLOR = "#388E3C"  # Vert pour les actions positives
ACCENT_COLOR = "#F57C00"  # Orange pour les accents
DANGER_COLOR = "#D32F2F"  # Rouge pour les alertes
BACKGROUND_COLOR = "#F5F5F5"  # Gris clair pour le fond
CARD_COLOR = "#FFFFFF"  # Blanc pour les cartes
TEXT_COLOR = "#212121"  # Gris foncé pour le texte
TEXT_SECONDARY_COLOR = "#757575"  # Gris moyen pour le texte secondaire

# Style principal de l'application (DEPRECATED - Utiliser professional_theme.py)
MAIN_STYLE = """
    /* Style legacy - Utiliser professional_theme.py pour le nouveau design */
    QMainWindow {
        background-color: #f0f0f0;
    }
    QTabWidget::pane {
        border: 1px solid #cccccc;
        background-color: white;
    }
    QTabBar::tab {
        background-color: #e0e0e0;
        border: 1px solid #cccccc;
        padding: 8px 16px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: white;
        border-bottom-color: white;
    }
    QGroupBox {
        font-weight: bold;
        border: 1px solid #cccccc;
        border-radius: 5px;
        margin-top: 1em;
        padding-top: 10px;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 0 5px;
    }
    QPushButton {
        background-color: #0078d7;
        color: white;
        border: none;
        padding: 5px 15px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #106ebe;
    }
    QPushButton:pressed {
        background-color: #005a9e;
    }
    QLineEdit, QDateEdit, QTimeEdit {
        padding: 5px;
        border: 1px solid #cccccc;
        border-radius: 3px;
    }
    QTableWidget {
        border: 1px solid #cccccc;
        gridline-color: #e0e0e0;
    }
    QTableWidget::item {
        padding: 5px;
    }
    QHeaderView::section {
        background-color: #f0f0f0;
        padding: 5px;
        border: 1px solid #cccccc;
    }
"""

# Couleurs pour les graphiques
CHART_COLORS = [
    "#1976D2",  # Bleu
    "#388E3C",  # Vert
    "#F57C00",  # Orange
    "#9C27B0",  # Violet
    "#00ACC1",  # Cyan
    "#FF5722",  # Orange foncé
    "#607D8B",  # Bleu gris
    "#8BC34A",  # Vert clair
]

# Style pour les graphiques
CHART_STYLE = {
    "figure.facecolor": "#FFFFFF",
    "axes.facecolor": "#FFFFFF",
    "axes.edgecolor": "#757575",
    "axes.labelcolor": "#212121",
    "axes.grid": True,
    "grid.color": "#EEEEEE",
    "text.color": "#212121",
    "xtick.color": "#757575",
    "ytick.color": "#757575",
    "lines.linewidth": 2.5,
    "font.size": 10,
    "axes.titlesize": 14,
    "axes.labelsize": 12,
}