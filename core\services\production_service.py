"""
Service de gestion de la production.
"""

from datetime import datetime
from models.enums import ProcessStepEnum
from models.database_models import Production

class ProductionService:
    def __init__(self, db_manager: object):
        self.db_manager = db_manager

    def add_production(self, quantity: float, quality: float, production_hours: float,
                      step: ProcessStepEnum, production_date: datetime, quantity_used: float = 0) -> None:
        """Ajoute une production."""
        try:
            self.db_manager.add_production(
                quantity=quantity,
                quality=quality,
                production_hours=production_hours,
                step=step,
                production_date=production_date,
                quantity_used=quantity_used
            )
        except Exception as e:
            raise Exception(f"Erreur lors de l'ajout de la production: {str(e)}")

    def get_production_summary(self, step: ProcessStepEnum) -> list:
        """Récupère le résumé des productions pour une étape donnée."""
        try:
            return self.db_manager.get_production_summary(step)
        except Exception as e:
            raise Exception(f"Erreur lors de la récupération du résumé des productions: {str(e)}")

    def delete_production(self, date: str) -> None:
        """Supprime une production pour une date donnée."""
        try:
            self.db_manager.delete_production_data(date)
        except Exception as e:
            raise Exception(f"Erreur lors de la suppression de la production: {str(e)}") 