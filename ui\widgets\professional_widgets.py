"""
Widgets professionnels réutilisables pour l'application SOTRAMINE PHOSPHATE.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QPushButton, QTableWidget, QHeaderView,
                            QGroupBox, QGridLayout, QScrollArea, QSplitter,
                            QProgressBar, QSizePolicy, QTableWidgetItem)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
from ui.professional_theme import SotramineTheme
from ui.icon_manager import icon_manager

class KPICard(QFrame):
    """Carte d'indicateur KPI professionnelle."""
    
    def __init__(self, title, value="--", unit="", subtitle=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.subtitle = subtitle
        self.trend = None
        
        self.setProperty("class", "card")
        self.setMinimumHeight(120)
        self.setMaximumHeight(150)
        
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setProperty("class", "subtitle")
        title_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(title_label)
        
        # Valeur principale
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 24pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
        """)
        self.value_label.setAlignment(Qt.AlignLeft)
        value_layout.addWidget(self.value_label)
        
        # Unité et sous-titre
        if self.unit:
            unit_text = self.unit
            if self.subtitle:
                unit_text = f"{self.unit} {self.subtitle}"
                
            unit_label = QLabel(unit_text)
            unit_label.setProperty("class", "muted")
            unit_label.setStyleSheet("font-size: 12pt; margin-left: 5px;")
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        layout.addLayout(value_layout)
        
        # Tendance (si disponible)
        if self.trend is not None:
            trend_label = QLabel()
            if self.trend > 0:
                trend_label.setText(f"↗ +{self.trend:.1f}%")
                trend_label.setStyleSheet(f"color: {SotramineTheme.SECONDARY}; font-weight: bold;")
            elif self.trend < 0:
                trend_label.setText(f"↘ {self.trend:.1f}%")
                trend_label.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-weight: bold;")
            else:
                trend_label.setText("→ 0.0%")
                trend_label.setStyleSheet(f"color: {SotramineTheme.TEXT_MUTED}; font-weight: bold;")
            
            trend_label.setAlignment(Qt.AlignRight)
            layout.addWidget(trend_label)
        
        layout.addStretch()
        
    def update_value(self, value, unit="", trend=None):
        """Met à jour la valeur affichée."""
        self.value = value
        if unit:
            self.unit = unit
        self.trend = trend
        
        self.value_label.setText(str(value))
        # Mettre à jour la tendance si nécessaire
        
    def update_subtitle(self, subtitle):
        """Met à jour le sous-titre (généralement affiché à côté de l'unité)."""
        self.subtitle = subtitle
        
        # Chercher le label d'unité et mettre à jour son texte
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.layout():
                for j in range(item.layout().count()):
                    widget = item.layout().itemAt(j).widget()
                    if isinstance(widget, QLabel) and widget != self.value_label:
                        widget.setText(f"{self.unit} {subtitle}")
                        return
                        
    def set_value_color(self, color):
        """Change la couleur de la valeur affichée."""
        self.value_label.setStyleSheet(f"""
            font-size: 24pt;
            font-weight: bold;
            color: {color};
        """)

class StatusIndicator(QFrame):
    """Indicateur de statut avec couleur."""
    
    def __init__(self, title, status="OK", parent=None):
        super().__init__(parent)
        self.title = title
        self.status = status
        
        self.setProperty("class", "indicator")
        self.setMinimumHeight(60)
        
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Icône de statut
        self.status_icon = QLabel()
        self.status_icon.setFixedSize(24, 24)
        layout.addWidget(self.status_icon)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # Label de statut
        self.status_label = QLabel(self.status)
        self.status_label.setStyleSheet("font-weight: bold; font-size: 9pt;")
        layout.addWidget(self.status_label)
        
        self.update_status(self.status)
        
    def update_status(self, status, message=None):
        """Met à jour le statut et la couleur."""
        self.status = status
        
        # Mettre à jour le texte du statut
        if message:
            self.status_label.setText(f"{status}: {message}")
        else:
            self.status_label.setText(status)
        
        # Mettre à jour l'icône
        icon = icon_manager.get_status_icon(status)
        pixmap = icon.pixmap(24, 24)
        self.status_icon.setPixmap(pixmap)
        
        # Mettre à jour les couleurs
        if status in ["En cours", "OK"]:
            color = SotramineTheme.SECONDARY
            bg_color = f"{SotramineTheme.SECONDARY}20"
        elif status in ["Attention", "Maintenance"]:
            color = SotramineTheme.ACCENT
            bg_color = f"{SotramineTheme.ACCENT}20"
        elif status in ["Arrêté", "Erreur"]:
            color = SotramineTheme.DANGER
            bg_color = f"{SotramineTheme.DANGER}20"
        else:
            color = SotramineTheme.TEXT_MUTED
            bg_color = f"{SotramineTheme.GRAY_300}20"
            
        self.setStyleSheet(f"""
            QFrame[class="indicator"] {{
                background-color: {bg_color};
                border: 2px solid {color};
                border-radius: 8px;
            }}
        """)
        
        self.status_label.setStyleSheet(f"""
            color: {color};
            font-weight: bold;
            font-size: 9pt;
        """)

class ProfessionalTable(QTableWidget):
    """Tableau avec style professionnel."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
        
    def itemClass(self):
        """Retourne la classe d'item par défaut."""
        return QTableWidgetItem
        
    def setup_style(self):
        """Configure le style du tableau."""
        # Configuration générale
        self.setAlternatingRowColors(True)
        self.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 8px;
                background-color: {SotramineTheme.BG_PRIMARY};
                gridline-color: {SotramineTheme.GRAY_200};
                selection-background-color: {SotramineTheme.PRIMARY_LIGHT};
                selection-color: {SotramineTheme.TEXT_WHITE};
            }}
            
            QTableWidget::item {{
                border: none;
                padding: 8px;
                font-size: 9pt;
            }}
            
            QTableWidget::item:selected {{
                background-color: {SotramineTheme.PRIMARY_LIGHT};
                color: {SotramineTheme.TEXT_WHITE};
            }}
            
            QHeaderView::section {{
                background-color: {SotramineTheme.PRIMARY};
                color: {SotramineTheme.TEXT_WHITE};
                border: none;
                padding: 12px 8px;
                font-weight: bold;
                font-size: 9pt;
            }}
        """)
        
        # Configuration des en-têtes
        header = self.horizontalHeader()
        header.setDefaultSectionSize(120)
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # Configuration verticale
        v_header = self.verticalHeader()
        v_header.setVisible(False)
        v_header.setDefaultSectionSize(35)

class ActionButton(QPushButton):
    """Bouton d'action avec icône."""
    
    def __init__(self, text, action_name="", button_class="primary", parent=None):
        super().__init__(text, parent)
        self.action_name = action_name
        self.button_class = button_class
        
        self.setup_button()
        
    def setup_button(self):
        """Configure le bouton."""
        # Ajouter l'icône si disponible
        if self.action_name:
            icon = icon_manager.get_action_icon(self.action_name)
            self.setIcon(icon)
            self.setProperty("hasIcon", True)
        
        # Appliquer la classe CSS
        self.setProperty("class", self.button_class)
        
        # Style spécifique selon la classe
        if self.button_class == "success":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SotramineTheme.SECONDARY};
                    color: {SotramineTheme.TEXT_WHITE};
                }}
                QPushButton:hover {{
                    background-color: {SotramineTheme.SECONDARY_LIGHT};
                }}
            """)
        elif self.button_class == "danger":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SotramineTheme.DANGER};
                    color: {SotramineTheme.TEXT_WHITE};
                }}
                QPushButton:hover {{
                    background-color: {SotramineTheme.DANGER_LIGHT};
                }}
            """)
        elif self.button_class == "warning":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SotramineTheme.ACCENT};
                    color: {SotramineTheme.TEXT_WHITE};
                }}
                QPushButton:hover {{
                    background-color: {SotramineTheme.ACCENT_LIGHT};
                }}
            """)

class SectionHeader(QFrame):
    """En-tête de section professionnel."""
    
    def __init__(self, title, subtitle="", parent=None):
        super().__init__(parent)
        self.title = title
        self.subtitle = subtitle
        
        self.setMaximumHeight(80)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 10)
        layout.setSpacing(2)
        
        # Titre principal
        title_label = QLabel(self.title)
        title_label.setProperty("class", "title")
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
        """)
        layout.addWidget(title_label)
        
        # Sous-titre si disponible
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setProperty("class", "muted")
            subtitle_label.setStyleSheet(f"""
                font-size: 10pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            layout.addWidget(subtitle_label)
        
        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"color: {SotramineTheme.GRAY_300};")
        layout.addWidget(separator)

class DashboardWidget(QWidget):
    """Widget de tableau de bord avec KPI."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.kpi_cards = []
        self.status_indicators = []
        
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # En-tête du tableau de bord
        header = SectionHeader("Tableau de Bord", "Indicateurs de performance en temps réel")
        layout.addWidget(header)
        
        # Zone de KPI
        self.kpi_layout = QGridLayout()
        self.kpi_layout.setSpacing(15)
        layout.addLayout(self.kpi_layout)
        
        # Zone d'indicateurs de statut
        self.status_layout = QVBoxLayout()
        self.status_layout.setSpacing(8)
        layout.addLayout(self.status_layout)
        
        layout.addStretch()
        
    def add_kpi_card(self, title, value="--", unit="", trend=None, row=0, col=0):
        """Ajoute une carte KPI."""
        card = KPICard(title, value, unit, trend)
        self.kpi_cards.append(card)
        self.kpi_layout.addWidget(card, row, col)
        return card
        
    def add_status_indicator(self, title, status="OK"):
        """Ajoute un indicateur de statut."""
        indicator = StatusIndicator(title, status)
        self.status_indicators.append(indicator)
        self.status_layout.addWidget(indicator)
        return indicator
        
        
class KPILine(QFrame):
    """Ligne simple pour afficher un KPI."""
    
    def __init__(self, title, value="--", unit="", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        
        self.setProperty("class", "kpi-line")
        self.setStyleSheet(f"""
            QFrame[class="kpi-line"] {{
                border-bottom: 1px solid {SotramineTheme.GRAY_200};
                padding: 8px 0;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-weight: bold;
            color: {SotramineTheme.TEXT_SECONDARY};
            font-size: 10pt;
        """)
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # Valeur
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            font-size: 12pt;
        """)
        layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                color: {SotramineTheme.TEXT_MUTED};
                font-size: 9pt;
                margin-left: 2px;
            """)
            layout.addWidget(unit_label)
            
    def update_value(self, value, unit=None):
        """Met à jour la valeur affichée."""
        self.value = value
        if unit:
            self.unit = unit
            
        self.value_label.setText(str(value))
