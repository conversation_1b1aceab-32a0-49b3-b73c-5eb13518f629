"""
Service de gestion des arrêts.
"""

from datetime import datetime
from models.enums import ProcessStepEnum
from models.database_models import Downtime

class DowntimeService:
    def __init__(self, db_manager: object):
        self.db_manager = db_manager

    def add_downtime(self, reason: str, duration: float, step: ProcessStepEnum,
                    downtime_date: datetime) -> None:
        """Ajoute un arrêt."""
        try:
            self.db_manager.add_downtime(
                reason=reason,
                duration=duration,
                step=step,
                downtime_date=downtime_date
            )
        except Exception as e:
            raise Exception(f"Erreur lors de l'ajout de l'arrêt: {str(e)}")

    def get_downtime_summary(self, step: ProcessStepEnum) -> list:
        """Récupère le résumé des arrêts pour une étape donnée."""
        try:
            return self.db_manager.get_downtime_summary(step)
        except Exception as e:
            raise Exception(f"Erreur lors de la récupération du résumé des arrêts: {str(e)}")

    def delete_downtime(self, date: str, time: str) -> None:
        """Supprime un arrêt pour une date et heure données."""
        try:
            self.db_manager.delete_downtime_data(date, time)
        except Exception as e:
            raise Exception(f"Erreur lors de la suppression de l'arrêt: {str(e)}") 