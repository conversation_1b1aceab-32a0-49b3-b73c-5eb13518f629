# Standard library imports
import logging
from typing import Optional, List, Dict, Any

# Third-party imports
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QGroupBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, 
                             QAbstractItemView)

# Local imports
from models.enums import ProcessStepEnum

class HourCounterTab(QWidget):
    """Onglet d'affichage des compteurs horaires des unités."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.init_ui()
        self.setup_timers()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Titre de l'onglet
        title_label = QLabel("Compteurs d'heures de production")
        title_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        title_label.setFont(font)
        main_layout.addWidget(title_label)
        
        # Tableau des compteurs d'heures cumulés
        summary_group = QGroupBox("Heures de production cumulées")
        summary_layout = QVBoxLayout()
        
        self.summary_table = QTableWidget()
        self.summary_table.setColumnCount(2)
        self.summary_table.setRowCount(2)  # Une ligne pour chaque unité
        self.summary_table.setHorizontalHeaderLabels(["Unité", "Total des heures"])
        
        # Configurer les cellules pour les unités
        unit_item_concassage = QTableWidgetItem("Concassage")
        unit_item_concassage.setTextAlignment(Qt.AlignCenter)
        unit_item_concassage.setFlags(Qt.ItemIsEnabled)  # Rendre la cellule non éditable
        unit_item_concassage.setBackground(QBrush(QColor(240, 240, 240)))
        self.summary_table.setItem(0, 0, unit_item_concassage)
        
        unit_item_laverie = QTableWidgetItem("Laverie")
        unit_item_laverie.setTextAlignment(Qt.AlignCenter)
        unit_item_laverie.setFlags(Qt.ItemIsEnabled)  # Rendre la cellule non éditable
        unit_item_laverie.setBackground(QBrush(QColor(240, 240, 240)))
        self.summary_table.setItem(1, 0, unit_item_laverie)
        
        # Cellules pour les totaux (seront mises à jour plus tard)
        self.crushing_total_item = QTableWidgetItem("0.0")
        self.crushing_total_item.setTextAlignment(Qt.AlignCenter)
        self.crushing_total_item.setFlags(Qt.ItemIsEnabled)  # Rendre la cellule non éditable
        self.summary_table.setItem(0, 1, self.crushing_total_item)
        
        self.laverie_total_item = QTableWidgetItem("0.0")
        self.laverie_total_item.setTextAlignment(Qt.AlignCenter)
        self.laverie_total_item.setFlags(Qt.ItemIsEnabled)  # Rendre la cellule non éditable
        self.summary_table.setItem(1, 1, self.laverie_total_item)
        
        # Configurer l'apparence du tableau
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.summary_table.verticalHeader().setVisible(False)
        self.summary_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.summary_table.setSelectionMode(QAbstractItemView.NoSelection)
        
        # Définir une hauteur fixe pour les lignes
        for row in range(self.summary_table.rowCount()):
            self.summary_table.setRowHeight(row, 60)
        
        # Appliquer une police plus grande pour les totaux
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        self.crushing_total_item.setFont(font)
        self.laverie_total_item.setFont(font)
        
        summary_layout.addWidget(self.summary_table)
        summary_group.setLayout(summary_layout)
        main_layout.addWidget(summary_group)
        
        # Ajouter un espace vide en bas pour centrer le tableau
        spacer_label = QLabel("")
        main_layout.addWidget(spacer_label)
        
        self.setLayout(main_layout)
    
    def setup_timers(self):
        """Configure les timers pour la mise à jour automatique."""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_tab)
        self.update_timer.start(10000)  # Mise à jour toutes les 10 secondes
    
    def refresh_tab(self):
        """Rafraîchit toutes les données de l'onglet."""
        try:
            self.update_summary_table()
        except Exception as e:
            logging.error(f"Erreur lors du rafraîchissement de l'onglet: {str(e)}")
    
    def update_summary_table(self):
        """Met à jour le tableau des totaux cumulés."""
        try:
            # Récupérer les données des compteurs horaires
            crushing_counters = self.db_manager.get_unit_hour_counters(ProcessStepEnum.CONCASSAGE)
            laverie_counters = self.db_manager.get_unit_hour_counters(ProcessStepEnum.LAVERIE)
            
            # Calculer les totaux pour le concassage
            crushing_total_hours = sum(counter['hours'] for counter in crushing_counters)
            
            # Calculer les totaux pour la laverie
            laverie_total_hours = sum(counter['hours'] for counter in laverie_counters)
            
            # Mettre à jour les cellules du tableau
            self.crushing_total_item.setText(f"{crushing_total_hours:.1f}")
            self.laverie_total_item.setText(f"{laverie_total_hours:.1f}")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour du tableau des totaux: {str(e)}")