"""
Onglet concassage amélioré avec layout optimisé.
Utilise un splitter horizontal pour désencombrer l'interface.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QGroupBox,
                            QMessageBox, QDateTimeEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QTabWidget)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QPalette, QColor
from ui.layouts.improved_layout_base import ImprovedLayoutBase, CollapsiblePanel, KPICard
from models.enums import ProcessStepEnum
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any


class ImprovedCrushingTab(ImprovedLayoutBase):
    """Onglet de gestion du concassage avec layout amélioré."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        
        # Widgets de formulaire production
        self.production_date = QDateTimeEdit()
        self.production_date.setDateTime(QDateTime.currentDateTime())
        self.production_hours_input = QLineEdit()
        self.quantity_produced_input = QLineEdit()
        self.quality_input = QLineEdit()
        self.quantity_used_input = QLineEdit()
        
        # Widgets de formulaire arrêts
        self.downtime_date = QDateTimeEdit()
        self.downtime_date.setDateTime(QDateTime.currentDateTime())
        self.downtime_duration = QLineEdit()
        self.downtime_reason = QLineEdit()
        
        # KPI Cards
        self.stock_card = None
        self.productivity_card = None
        self.yield_card = None
        self.availability_card = None
        
        # Tables
        self.production_table = QTableWidget()
        self.downtime_table = QTableWidget()
        
        self.init_improved_ui()
        self.setup_timers()
    
    def init_improved_ui(self):
        """Initialise l'interface améliorée."""
        
        # === PANNEAU GAUCHE (Contrôles) ===
        
        # 1. Panel KPI
        kpi_panel, kpi_grid = self.create_kpi_panel("📊 Indicateurs de Performance")
        
        # Créer les cartes KPI
        self.stock_card = KPICard("Stock Disponible", "0.0", "tonnes")
        self.productivity_card = KPICard("Productivité", "0.0", "t/h")
        self.yield_card = KPICard("Rendement", "0.0", "%")
        self.availability_card = KPICard("Disponibilité", "0.0", "%")
        
        # Disposition en grille 2x2
        kpi_grid.addWidget(self.stock_card, 0, 0)
        kpi_grid.addWidget(self.productivity_card, 0, 1)
        kpi_grid.addWidget(self.yield_card, 1, 0)
        kpi_grid.addWidget(self.availability_card, 1, 1)
        
        self.add_to_left_panel(kpi_panel)
        
        # 2. Panel Saisie Production
        production_panel, production_layout = self.create_form_panel("🏭 Nouvelle Production")
        
        production_layout.addRow("Date de production:", self.production_date)
        
        self.production_hours_input.setPlaceholderText("Ex: 8.5")
        production_layout.addRow("Heures de production:", self.production_hours_input)
        
        self.quantity_produced_input.setPlaceholderText("Ex: 145.2")
        production_layout.addRow("Quantité produite (t):", self.quantity_produced_input)
        
        self.quality_input.setPlaceholderText("Ex: 30.5")
        production_layout.addRow("Qualité P2O5 (%):", self.quality_input)
        
        self.quantity_used_input.setPlaceholderText("Ex: 180.0")
        production_layout.addRow("Quantité utilisée (t):", self.quantity_used_input)
        
        # Bouton d'ajout production
        add_prod_button = QPushButton("✅ Ajouter Production")
        add_prod_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        add_prod_button.clicked.connect(self.add_production)
        production_layout.addRow("", add_prod_button)
        
        self.add_to_left_panel(production_panel)
        
        # 3. Panel Saisie Arrêts
        downtime_panel, downtime_layout = self.create_form_panel("⏸️ Nouvel Arrêt")
        
        downtime_layout.addRow("Date d'arrêt:", self.downtime_date)
        
        self.downtime_duration.setPlaceholderText("Ex: 45")
        downtime_layout.addRow("Durée (min):", self.downtime_duration)
        
        self.downtime_reason.setPlaceholderText("Ex: Maintenance préventive")
        downtime_layout.addRow("Raison:", self.downtime_reason)
        
        # Bouton d'ajout arrêt
        add_downtime_button = QPushButton("⏸️ Ajouter Arrêt")
        add_downtime_button.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #B91C1C;
            }
        """)
        add_downtime_button.clicked.connect(self.add_downtime)
        downtime_layout.addRow("", add_downtime_button)
        
        self.add_to_left_panel(downtime_panel)
        
        # 4. Panel Outils
        tools_panel, tools_layout = self.create_form_panel("🛠️ Outils")
        
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #7C3AED;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6D28D9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        
        clear_button = QPushButton("🗑️ Vider Formulaires")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        clear_button.clicked.connect(self.clear_forms)
        
        tools_layout.addRow(refresh_button)
        tools_layout.addRow(clear_button)
        
        self.add_to_left_panel(tools_panel)
        self.add_stretch_to_left()
        
        # === PANNEAU DROIT (Données) ===
        
        # Créer des onglets pour séparer Production et Arrêts
        data_tabs = QTabWidget()
        
        # Onglet Production
        production_tab_widget = CollapsiblePanel("📈 Historique Production")
        
        # Configuration table production
        self.production_table.setColumnCount(6)
        self.production_table.setHorizontalHeaderLabels([
            "Date", "Heures", "Quantité (t)", "Qualité (%)", "Utilisée (t)", "Actions"
        ])
        
        # Style de la table
        self.production_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #F9FAFB;
                selection-background-color: #DBEAFE;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.production_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.production_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.production_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.production_table.setAlternatingRowColors(True)
        
        production_tab_widget.add_widget(self.production_table)
        
        # Onglet Arrêts
        downtime_tab_widget = CollapsiblePanel("⏸️ Historique Arrêts")
        
        # Configuration table arrêts
        self.downtime_table.setColumnCount(4)
        self.downtime_table.setHorizontalHeaderLabels([
            "Date", "Durée (min)", "Raison", "Actions"
        ])
        
        self.downtime_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #FEF2F2;
                selection-background-color: #FEE2E2;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #DC2626;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.downtime_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.downtime_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.downtime_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.downtime_table.setAlternatingRowColors(True)
        
        downtime_tab_widget.add_widget(self.downtime_table)
        
        # Ajouter les deux panneaux au panneau droit
        self.add_to_right_panel(production_tab_widget)
        self.add_to_right_panel(downtime_tab_widget)
        
        # Chargement initial des données
        self.refresh_data()
    
    def setup_timers(self):
        """Configure les timers pour la mise à jour automatique."""
        # Timer pour mettre à jour les KPI toutes les 30 secondes
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_indicators)
        self.kpi_timer.start(30000)  # 30 secondes
        
        # Première mise à jour immédiate
        self.update_indicators()
    
    def add_production(self):
        """Ajoute une nouvelle production."""
        try:
            # Validation des données
            if not self.production_hours_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir les heures de production.")
                return
                
            if not self.quantity_produced_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la quantité produite.")
                return
            
            hours = float(self.production_hours_input.text())
            quantity = float(self.quantity_produced_input.text())
            quality = self.quality_input.text().strip()
            quantity_used = float(self.quantity_used_input.text()) if self.quantity_used_input.text().strip() else None
            
            if hours <= 0:
                QMessageBox.warning(self, "Erreur", "Les heures doivent être positives.")
                return
                
            if quantity <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive.")
                return
            
            # Ajouter à la base de données
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity,
                production_hours=hours,
                quality=quality,
                quantity_used=quantity_used
            )
            
            # Message de succès
            QMessageBox.information(
                self, "✅ Succès", 
                f"Production ajoutée: {quantity} tonnes en {hours} heures"
            )
            
            # Vider le formulaire et actualiser
            self.clear_production_form()
            self.refresh_data()
            self.update_indicators()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir des valeurs numériques valides.")
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout de production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def add_downtime(self):
        """Ajoute un nouvel arrêt."""
        try:
            # Validation des données
            if not self.downtime_duration.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la durée de l'arrêt.")
                return
                
            if not self.downtime_reason.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la raison de l'arrêt.")
                return
            
            duration = int(self.downtime_duration.text())
            reason = self.downtime_reason.text().strip()
            
            if duration <= 0:
                QMessageBox.warning(self, "Erreur", "La durée doit être positive.")
                return
            
            # Ajouter à la base de données
            self.db_manager.add_downtime(
                step=ProcessStepEnum.CONCASSAGE,
                duration_minutes=duration,
                reason=reason,
                downtime_date=self.downtime_date.dateTime().toPyDateTime()
            )
            
            # Message de succès
            QMessageBox.information(
                self, "✅ Succès", 
                f"Arrêt ajouté: {duration} minutes - {reason}"
            )
            
            # Vider le formulaire et actualiser
            self.clear_downtime_form()
            self.refresh_data()
            self.update_indicators()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir des valeurs valides.")
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout d'arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def clear_production_form(self):
        """Vide le formulaire de production."""
        self.production_hours_input.clear()
        self.quantity_produced_input.clear()
        self.quality_input.clear()
        self.quantity_used_input.clear()
        self.production_date.setDateTime(QDateTime.currentDateTime())
    
    def clear_downtime_form(self):
        """Vide le formulaire d'arrêt."""
        self.downtime_duration.clear()
        self.downtime_reason.clear()
        self.downtime_date.setDateTime(QDateTime.currentDateTime())
    
    def clear_forms(self):
        """Vide tous les formulaires."""
        self.clear_production_form()
        self.clear_downtime_form()
    
    def update_indicators(self):
        """Met à jour les indicateurs KPI."""
        try:
            # Stock disponible (stock de réception)
            available_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_card.update_value(f"{available_stock:.1f}")
            
            # Calculer les autres KPI avec des valeurs placeholder
            self.productivity_card.update_value("15.2")  # Placeholder
            self.yield_card.update_value("85.4")  # Placeholder
            self.availability_card.update_value("92.1")  # Placeholder
                
        except Exception as e:
            logging.error(f"Erreur mise à jour indicateurs concassage: {str(e)}")
    
    def refresh_data(self):
        """Actualise les données des tables."""
        try:
            self.refresh_production_data()
            self.refresh_downtime_data()
        except Exception as e:
            logging.error(f"Erreur actualisation données concassage: {str(e)}")
    
    def refresh_production_data(self):
        """Actualise les données de production."""
        try:
            # Récupérer les productions récentes
            productions = self.db_manager.get_production_summary(ProcessStepEnum.CONCASSAGE)
            
            # Prendre les 20 dernières
            productions = productions[-20:] if len(productions) > 20 else productions
            productions.reverse()  # Plus récent en premier
            
            # Remplir la table
            self.production_table.setRowCount(len(productions))
            
            for row, production in enumerate(productions):
                # Date
                date_str = production['production_date'].strftime("%d/%m/%Y %H:%M")
                self.production_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Heures
                hours_str = f"{production.get('production_hours', 0):.1f}"
                self.production_table.setItem(row, 1, QTableWidgetItem(hours_str))
                
                # Quantité
                quantity_str = f"{production['quantity']:.2f}"
                self.production_table.setItem(row, 2, QTableWidgetItem(quantity_str))
                
                # Qualité
                quality_str = production.get('quality', '--')
                self.production_table.setItem(row, 3, QTableWidgetItem(str(quality_str)))
                
                # Quantité utilisée
                used_str = f"{production.get('quantity_used', 0):.2f}" if production.get('quantity_used') else "--"
                self.production_table.setItem(row, 4, QTableWidgetItem(used_str))
                
                # Actions
                self.production_table.setItem(row, 5, QTableWidgetItem("Modifier"))
                
        except Exception as e:
            logging.error(f"Erreur actualisation production: {str(e)}")
            # Afficher un message d'info dans la table
            self.production_table.setRowCount(1)
            self.production_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            for col in range(1, 6):
                self.production_table.setItem(0, col, QTableWidgetItem("--"))
    
    def refresh_downtime_data(self):
        """Actualise les données d'arrêts."""
        try:
            # Récupérer les arrêts récents
            downtimes = self.db_manager.get_downtime_summary(ProcessStepEnum.CONCASSAGE)
            
            # Prendre les 20 derniers
            downtimes = downtimes[-20:] if len(downtimes) > 20 else downtimes
            downtimes.reverse()  # Plus récent en premier
            
            # Remplir la table
            self.downtime_table.setRowCount(len(downtimes))
            
            for row, downtime in enumerate(downtimes):
                # Date
                date_str = downtime['downtime_date'].strftime("%d/%m/%Y %H:%M")
                self.downtime_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Durée
                duration_str = f"{downtime['duration_minutes']}"
                self.downtime_table.setItem(row, 1, QTableWidgetItem(duration_str))
                
                # Raison
                reason_str = downtime.get('reason', '--')
                self.downtime_table.setItem(row, 2, QTableWidgetItem(str(reason_str)))
                
                # Actions
                self.downtime_table.setItem(row, 3, QTableWidgetItem("Modifier"))
                
        except Exception as e:
            logging.error(f"Erreur actualisation arrêts: {str(e)}")
            # Afficher un message d'info dans la table
            self.downtime_table.setRowCount(1)
            self.downtime_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            for col in range(1, 4):
                self.downtime_table.setItem(0, col, QTableWidgetItem("--"))
