"""
Widgets KPI améliorés pour une meilleure organisation et lisibilité des indicateurs de performance.
"""

from PyQt5.QtWidgets import (QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
                            QGridLayout, QSizePolicy, QWidget, QGroupBox)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPainterPath
from ui.professional_theme import SotramineTheme
import math

class EnhancedKPICard(QFrame):
    """Carte KPI améliorée avec meilleure organisation visuelle et options de personnalisation."""
    
    def __init__(self, title, value="--", unit="", trend=None, category="", icon="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.trend = trend
        self.category = category
        self.icon = icon
        self.color = color or SotramineTheme.PRIMARY
        
        self.setProperty("class", "card")
        self.setMinimumHeight(120)
        self.setMaximumHeight(150)
        self.setMinimumWidth(180)
        
        # Appliquer un style spécifique à cette carte
        self.setStyleSheet(f"""
            QFrame[class="card"] {{
                background-color: {SotramineTheme.BG_CARD};
                border: 2px solid {self.color}30;
                border-left: 5px solid {self.color};
                border-radius: 8px;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la carte KPI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(5)
        
        # En-tête avec titre et catégorie
        header_layout = QHBoxLayout()
        header_layout.setSpacing(5)
        
        # Icône (si disponible)
        if self.icon:
            icon_label = QLabel()
            icon_label.setText(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 16pt;
                color: {self.color};
                margin-right: 5px;
            """)
            header_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 11pt;
            font-weight: bold;
            color: {self.color};
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        header_layout.addWidget(title_label, 1)
        
        # Catégorie (si disponible)
        if self.category:
            category_label = QLabel(self.category)
            category_label.setStyleSheet(f"""
                font-size: 8pt;
                color: {SotramineTheme.TEXT_MUTED};
                background-color: {self.color}15;
                border-radius: 4px;
                padding: 2px 5px;
            """)
            header_layout.addWidget(category_label)
        
        layout.addLayout(header_layout)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"background-color: {self.color}30;")
        separator.setMaximumHeight(1)
        layout.addWidget(separator)
        
        # Valeur principale
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 5, 0, 0)
        
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 28pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        value_layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 12pt;
                color: {SotramineTheme.TEXT_MUTED};
                margin-left: 5px;
            """)
            unit_label.setAlignment(Qt.AlignBottom | Qt.AlignLeft)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        layout.addLayout(value_layout)
        
        # Tendance (si disponible)
        if self.trend is not None:
            self.trend_label = QLabel()
            self.update_trend(self.trend)
            self.trend_label.setAlignment(Qt.AlignRight)
            layout.addWidget(self.trend_label)
        else:
            self.trend_label = None
            layout.addStretch()
    
    def update_value(self, value, trend=None):
        """Met à jour la valeur et éventuellement la tendance."""
        self.value = value
        self.value_label.setText(str(value))
        
        if trend is not None and self.trend_label:
            self.update_trend(trend)
    
    def update_trend(self, trend):
        """Met à jour l'affichage de la tendance."""
        self.trend = trend
        
        if not self.trend_label:
            return
            
        if trend > 0:
            self.trend_label.setText(f"↗ +{trend:.1f}%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.SECONDARY}; font-weight: bold;")
        elif trend < 0:
            self.trend_label.setText(f"↘ {trend:.1f}%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-weight: bold;")
        else:
            self.trend_label.setText("→ 0.0%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.TEXT_MUTED}; font-weight: bold;")


class KPISection(QWidget):
    """Section organisée d'indicateurs KPI avec catégories."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.categories = {}  # {category_name: [kpi_cards]}
        self.category_layouts = {}  # {category_name: layout}
        self.kpi_cards = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la section KPI."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(15)
        
        # Layout pour les catégories
        self.categories_layout = QVBoxLayout()
        self.categories_layout.setSpacing(10)
        self.main_layout.addLayout(self.categories_layout)
    
    def add_kpi_card(self, title, value="0", unit="", trend=None, category="Général", icon="", color=None):
        """Ajoute une carte KPI à la section, organisée par catégorie."""
        # Créer la carte KPI
        card = EnhancedKPICard(title, value, unit, trend, category, icon, color)
        self.kpi_cards.append(card)
        
        # Ajouter à la catégorie appropriée
        if category not in self.categories:
            self.categories[category] = []
            
            # Créer un groupe pour cette catégorie
            category_group = QGroupBox(category)
            category_group.setStyleSheet(f"""
                QGroupBox {{
                    font-weight: bold;
                    border: 1px solid {SotramineTheme.GRAY_300};
                    border-radius: 8px;
                    margin-top: 12px;
                    background-color: {SotramineTheme.BG_CARD};
                }}
                
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    subcontrol-position: top left;
                    padding: 5px 10px;
                    background-color: {SotramineTheme.PRIMARY};
                    color: {SotramineTheme.TEXT_WHITE};
                    border-radius: 4px;
                }}
            """)
            
            # Layout pour les cartes de cette catégorie
            category_layout = QGridLayout(category_group)
            category_layout.setContentsMargins(15, 25, 15, 15)
            category_layout.setSpacing(10)
            
            self.category_layouts[category] = category_layout
            self.categories_layout.addWidget(category_group)
        
        # Ajouter la carte à la grille de sa catégorie
        layout = self.category_layouts[category]
        row, col = divmod(len(self.categories[category]), 3)  # 3 cartes par ligne
        layout.addWidget(card, row, col)
        
        self.categories[category].append(card)
        return card


class PerformanceIndicator(QFrame):
    """Indicateur de performance visuel avec jauge et valeur."""
    
    def __init__(self, title, value=0, min_value=0, max_value=100, target=None, 
                 unit="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.min_value = min_value
        self.max_value = max_value
        self.target = target
        self.unit = unit
        self.color = color or SotramineTheme.PRIMARY
        
        # Déterminer la couleur en fonction de la valeur
        self.update_color()
        
        self.setMinimumHeight(150)
        self.setMinimumWidth(200)
        self.setProperty("class", "card")
        
        self.init_ui()
    
    def update_color(self):
        """Met à jour la couleur en fonction de la valeur."""
        if self.target is not None:
            # Si on a une cible, la couleur dépend de la position par rapport à la cible
            if self.value >= self.target:
                self.color = SotramineTheme.SECONDARY  # Vert pour bon
            elif self.value >= self.target * 0.8:
                self.color = SotramineTheme.ACCENT  # Orange pour moyen
            else:
                self.color = SotramineTheme.DANGER  # Rouge pour mauvais
    
    def init_ui(self):
        """Initialise l'interface utilisateur de l'indicateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 12pt;
            font-weight: bold;
            color: {self.color};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Jauge visuelle (sera dessinée dans paintEvent)
        self.gauge = QFrame()
        self.gauge.setMinimumHeight(80)
        self.gauge.paintEvent = self.paint_gauge
        layout.addWidget(self.gauge)
        
        # Valeur et unité
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignCenter)
        value_layout.addWidget(self.value_label, 1)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 10pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        layout.addLayout(value_layout)
        
        # Afficher la cible si disponible
        if self.target is not None:
            target_label = QLabel(f"Cible: {self.target}{self.unit}")
            target_label.setStyleSheet(f"""
                font-size: 9pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            target_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(target_label)
    
    def paint_gauge(self, event):
        """Dessine la jauge de performance."""
        painter = QPainter(self.gauge)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.gauge.width()
        height = self.gauge.height()
        
        # Calculer le pourcentage de progression
        range_value = self.max_value - self.min_value
        if range_value <= 0:
            progress = 0
        else:
            progress = (self.value - self.min_value) / range_value
            progress = max(0, min(1, progress))  # Limiter entre 0 et 1
        
        # Dessiner l'arrière-plan
        bg_color = QColor(self.color)
        bg_color.setAlpha(30)
        painter.setBrush(QBrush(bg_color))
        painter.setPen(Qt.NoPen)
        
        bg_rect = QRect(10, height // 2 - 10, width - 20, 20)
        painter.drawRoundedRect(bg_rect, 10, 10)
        
        # Dessiner la progression
        if progress > 0:
            progress_width = int((width - 20) * progress)
            progress_rect = QRect(10, height // 2 - 10, progress_width, 20)
            
            painter.setBrush(QBrush(QColor(self.color)))
            painter.drawRoundedRect(progress_rect, 10, 10)
        
        # Dessiner la cible si disponible
        if self.target is not None:
            target_progress = (self.target - self.min_value) / range_value
            target_progress = max(0, min(1, target_progress))
            
            target_x = 10 + int((width - 20) * target_progress)
            
            painter.setPen(QPen(QColor(SotramineTheme.TEXT_PRIMARY), 2))
            painter.drawLine(target_x, height // 2 - 15, target_x, height // 2 + 15)
            
            # Petit triangle pour marquer la cible
            path = QPainterPath()
            path.moveTo(target_x, height // 2 - 20)
            path.lineTo(target_x - 5, height // 2 - 15)
            path.lineTo(target_x + 5, height // 2 - 15)
            path.closeSubpath()
            
            painter.setBrush(QBrush(QColor(SotramineTheme.TEXT_PRIMARY)))
            painter.drawPath(path)
    
    def update_value(self, value):
        """Met à jour la valeur de l'indicateur."""
        self.value = value
        self.update_color()
        
        self.value_label.setText(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.color};
        """)
        
        # Mettre à jour la jauge
        self.gauge.update()


class QualityIndicator(QFrame):
    """Indicateur spécifique pour la qualité avec échelle de couleurs."""
    
    def __init__(self, title, value=0, min_value=0, max_value=100, 
                 good_threshold=30, unit="%", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.min_value = min_value
        self.max_value = max_value
        self.good_threshold = good_threshold
        self.unit = unit
        
        # Déterminer la couleur en fonction de la valeur
        self.update_color()
        
        self.setMinimumHeight(150)
        self.setMinimumWidth(200)
        self.setProperty("class", "card")
        
        self.init_ui()
    
    def update_color(self):
        """Met à jour la couleur en fonction de la valeur de qualité."""
        if self.value >= self.good_threshold:
            self.color = SotramineTheme.SECONDARY  # Vert pour bonne qualité
        elif self.value >= self.good_threshold * 0.8:
            self.color = SotramineTheme.ACCENT  # Orange pour qualité moyenne
        else:
            self.color = SotramineTheme.DANGER  # Rouge pour mauvaise qualité
    
    def init_ui(self):
        """Initialise l'interface utilisateur de l'indicateur de qualité."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 12pt;
            font-weight: bold;
            color: {self.color};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Indicateur visuel (sera dessiné dans paintEvent)
        self.indicator = QFrame()
        self.indicator.setMinimumHeight(80)
        self.indicator.paintEvent = self.paint_indicator
        layout.addWidget(self.indicator)
        
        # Valeur et unité
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignCenter)
        value_layout.addWidget(self.value_label, 1)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 10pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        layout.addLayout(value_layout)
        
        # Afficher le seuil de bonne qualité
        threshold_label = QLabel(f"Seuil optimal: {self.good_threshold}{self.unit}")
        threshold_label.setStyleSheet(f"""
            font-size: 9pt;
            color: {SotramineTheme.TEXT_MUTED};
        """)
        threshold_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(threshold_label)
    
    def paint_indicator(self, event):
        """Dessine l'indicateur de qualité."""
        painter = QPainter(self.indicator)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.indicator.width()
        height = self.indicator.height()
        
        # Dessiner un arc de cercle pour représenter la qualité
        center_x = width // 2
        center_y = height - 20
        radius = min(width // 2 - 10, height - 30)
        
        # Dessiner l'arrière-plan de l'arc
        painter.setPen(QPen(QColor(SotramineTheme.GRAY_300), 10))
        painter.drawArc(center_x - radius, center_y - radius, 
                       radius * 2, radius * 2, 
                       180 * 16, 180 * 16)  # Angles en 1/16 de degré
        
        # Calculer l'angle en fonction de la valeur
        range_value = self.max_value - self.min_value
        if range_value <= 0:
            angle = 0
        else:
            progress = (self.value - self.min_value) / range_value
            progress = max(0, min(1, progress))  # Limiter entre 0 et 1
            angle = int(180 * progress)
        
        # Dessiner l'arc de progression
        painter.setPen(QPen(QColor(self.color), 10))
        painter.drawArc(center_x - radius, center_y - radius, 
                       radius * 2, radius * 2, 
                       180 * 16, -angle * 16)  # Angle négatif pour aller dans le sens horaire
        
        # Dessiner le seuil de bonne qualité
        threshold_progress = (self.good_threshold - self.min_value) / range_value
        threshold_progress = max(0, min(1, threshold_progress))
        threshold_angle = 180 - int(180 * threshold_progress)
        
        # Convertir l'angle en radians et calculer la position
        threshold_rad = math.radians(threshold_angle)
        threshold_x = center_x + int(radius * math.cos(threshold_rad))
        threshold_y = center_y - int(radius * math.sin(threshold_rad))
        
        # Dessiner un petit cercle pour marquer le seuil
        painter.setBrush(QBrush(QColor(SotramineTheme.SECONDARY)))
        painter.setPen(QPen(QColor(SotramineTheme.TEXT_WHITE), 1))
        painter.drawEllipse(threshold_x - 5, threshold_y - 5, 10, 10)
    
    def update_value(self, value):
        """Met à jour la valeur de l'indicateur de qualité."""
        self.value = value
        self.update_color()
        
        self.value_label.setText(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.color};
        """)
        
        # Mettre à jour l'indicateur
        self.indicator.update()