"""
Gestionnaire de thèmes pour l'application.
"""
import json
import os
import logging
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt

class ThemeManager:
    """Gestionnaire de thèmes pour l'application."""
    
    # Thèmes prédéfinis
    THEMES = {
        "light": {
            "name": "Clair",
            "description": "Thème clair par défaut",
            "styles": {
                "app_background": "#f0f0f0",
                "window_background": "#ffffff",
                "primary_color": "#0078d7",
                "secondary_color": "#106ebe",
                "accent_color": "#005a9e",
                "text_color": "#333333",
                "border_color": "#cccccc",
                "success_color": "#107c10",
                "warning_color": "#ff8c00",
                "error_color": "#e81123",
                "info_color": "#0078d7",
                "disabled_color": "#a6a6a6",
                "hover_color": "#e6e6e6",
                "selected_color": "#cce4f7",
                "tab_background": "#e0e0e0",
                "tab_selected": "#ffffff",
                "button_text": "#ffffff",
                "input_background": "#ffffff",
                "table_header": "#f0f0f0",
                "table_row_alternate": "#f9f9f9",
                "scrollbar_background": "#f0f0f0",
                "scrollbar_handle": "#cdcdcd",
                "tooltip_background": "#f0f0f0",
                "tooltip_text": "#333333"
            }
        },
        "dark": {
            "name": "Sombre",
            "description": "Thème sombre pour une utilisation nocturne",
            "styles": {
                "app_background": "#1e1e1e",
                "window_background": "#252526",
                "primary_color": "#0078d7",
                "secondary_color": "#106ebe",
                "accent_color": "#005a9e",
                "text_color": "#e0e0e0",
                "border_color": "#3f3f3f",
                "success_color": "#107c10",
                "warning_color": "#ff8c00",
                "error_color": "#e81123",
                "info_color": "#0078d7",
                "disabled_color": "#6d6d6d",
                "hover_color": "#3e3e3e",
                "selected_color": "#094771",
                "tab_background": "#2d2d2d",
                "tab_selected": "#1e1e1e",
                "button_text": "#ffffff",
                "input_background": "#3c3c3c",
                "table_header": "#2d2d2d",
                "table_row_alternate": "#333333",
                "scrollbar_background": "#1e1e1e",
                "scrollbar_handle": "#3f3f3f",
                "tooltip_background": "#2d2d2d",
                "tooltip_text": "#e0e0e0"
            }
        },
        "blue": {
            "name": "Bleu",
            "description": "Thème bleu professionnel",
            "styles": {
                "app_background": "#f0f5ff",
                "window_background": "#ffffff",
                "primary_color": "#0050c5",
                "secondary_color": "#0078d7",
                "accent_color": "#00397a",
                "text_color": "#333333",
                "border_color": "#b3d6ff",
                "success_color": "#107c10",
                "warning_color": "#ff8c00",
                "error_color": "#e81123",
                "info_color": "#0078d7",
                "disabled_color": "#a6a6a6",
                "hover_color": "#e6f0ff",
                "selected_color": "#cce4ff",
                "tab_background": "#e0e9ff",
                "tab_selected": "#ffffff",
                "button_text": "#ffffff",
                "input_background": "#ffffff",
                "table_header": "#e0e9ff",
                "table_row_alternate": "#f5f9ff",
                "scrollbar_background": "#f0f5ff",
                "scrollbar_handle": "#b3d6ff",
                "tooltip_background": "#f0f5ff",
                "tooltip_text": "#333333"
            }
        },
        "high_contrast": {
            "name": "Contraste élevé",
            "description": "Thème à contraste élevé pour une meilleure accessibilité",
            "styles": {
                "app_background": "#000000",
                "window_background": "#000000",
                "primary_color": "#1aebff",
                "secondary_color": "#ffffff",
                "accent_color": "#ffff00",
                "text_color": "#ffffff",
                "border_color": "#ffffff",
                "success_color": "#00ff00",
                "warning_color": "#ffff00",
                "error_color": "#ff0000",
                "info_color": "#1aebff",
                "disabled_color": "#7f7f7f",
                "hover_color": "#1f1f1f",
                "selected_color": "#0050c5",
                "tab_background": "#1f1f1f",
                "tab_selected": "#000000",
                "button_text": "#000000",
                "input_background": "#000000",
                "table_header": "#1f1f1f",
                "table_row_alternate": "#0f0f0f",
                "scrollbar_background": "#000000",
                "scrollbar_handle": "#ffffff",
                "tooltip_background": "#000000",
                "tooltip_text": "#ffffff"
            }
        }
    }
    
    def __init__(self, config_file: str = "theme_config.json"):
        """
        Initialise le gestionnaire de thèmes.
        
        Args:
            config_file: Chemin vers le fichier de configuration des thèmes
        """
        self.config_file = config_file
        self.config_path = os.path.join("config", config_file)
        self.current_theme = "light"
        self.custom_themes = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Charge la configuration des thèmes depuis le fichier."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.current_theme = config.get("current_theme", "light")
                    self.custom_themes = config.get("custom_themes", {})
            else:
                # Créer le fichier avec la configuration par défaut
                self.save_config()
        except Exception as e:
            logging.error(f"Erreur lors du chargement de la configuration des thèmes: {str(e)}")
    
    def save_config(self) -> bool:
        """
        Sauvegarde la configuration des thèmes dans le fichier.
        
        Returns:
            True si la sauvegarde a réussi, False sinon
        """
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            config = {
                "current_theme": self.current_theme,
                "custom_themes": self.custom_themes
            }
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde de la configuration des thèmes: {str(e)}")
            return False
    
    def get_theme(self, theme_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère un thème par son ID.
        
        Args:
            theme_id: ID du thème à récupérer (utilise le thème actuel si None)
            
        Returns:
            Dictionnaire contenant les informations du thème
        """
        if theme_id is None:
            theme_id = self.current_theme
        
        # Vérifier si c'est un thème prédéfini
        if theme_id in self.THEMES:
            return self.THEMES[theme_id]
        
        # Vérifier si c'est un thème personnalisé
        if theme_id in self.custom_themes:
            return self.custom_themes[theme_id]
        
        # Si le thème n'est pas trouvé, retourner le thème clair par défaut
        return self.THEMES["light"]
    
    def get_all_themes(self) -> Dict[str, Dict[str, Any]]:
        """
        Récupère tous les thèmes disponibles.
        
        Returns:
            Dictionnaire contenant tous les thèmes (prédéfinis et personnalisés)
        """
        return {**self.THEMES, **self.custom_themes}
    
    def set_theme(self, theme_id: str) -> bool:
        """
        Définit le thème actuel.
        
        Args:
            theme_id: ID du thème à définir
            
        Returns:
            True si le thème a été défini avec succès, False sinon
        """
        if theme_id in self.THEMES or theme_id in self.custom_themes:
            self.current_theme = theme_id
            self.save_config()
            return True
        return False
    
    def add_custom_theme(self, theme_id: str, theme_data: Dict[str, Any]) -> bool:
        """
        Ajoute un thème personnalisé.
        
        Args:
            theme_id: ID du thème à ajouter
            theme_data: Données du thème
            
        Returns:
            True si le thème a été ajouté avec succès, False sinon
        """
        if theme_id in self.THEMES:
            return False  # Ne pas écraser les thèmes prédéfinis
        
        self.custom_themes[theme_id] = theme_data
        self.save_config()
        return True
    
    def update_custom_theme(self, theme_id: str, theme_data: Dict[str, Any]) -> bool:
        """
        Met à jour un thème personnalisé.
        
        Args:
            theme_id: ID du thème à mettre à jour
            theme_data: Nouvelles données du thème
            
        Returns:
            True si le thème a été mis à jour avec succès, False sinon
        """
        if theme_id not in self.custom_themes:
            return False
        
        self.custom_themes[theme_id] = theme_data
        self.save_config()
        return True
    
    def delete_custom_theme(self, theme_id: str) -> bool:
        """
        Supprime un thème personnalisé.
        
        Args:
            theme_id: ID du thème à supprimer
            
        Returns:
            True si le thème a été supprimé avec succès, False sinon
        """
        if theme_id not in self.custom_themes:
            return False
        
        del self.custom_themes[theme_id]
        
        # Si le thème supprimé était le thème actuel, revenir au thème clair
        if self.current_theme == theme_id:
            self.current_theme = "light"
        
        self.save_config()
        return True
    
    def apply_theme_to_app(self, app: QApplication) -> None:
        """
        Applique le thème actuel à l'application.
        
        Args:
            app: Instance de QApplication
        """
        theme = self.get_theme()
        styles = theme["styles"]
        
        # Appliquer la palette de couleurs
        palette = QPalette()
        
        # Couleurs générales
        palette.setColor(QPalette.Window, QColor(styles["window_background"]))
        palette.setColor(QPalette.WindowText, QColor(styles["text_color"]))
        palette.setColor(QPalette.Base, QColor(styles["input_background"]))
        palette.setColor(QPalette.AlternateBase, QColor(styles["table_row_alternate"]))
        palette.setColor(QPalette.ToolTipBase, QColor(styles["tooltip_background"]))
        palette.setColor(QPalette.ToolTipText, QColor(styles["tooltip_text"]))
        palette.setColor(QPalette.Text, QColor(styles["text_color"]))
        palette.setColor(QPalette.Button, QColor(styles["primary_color"]))
        palette.setColor(QPalette.ButtonText, QColor(styles["button_text"]))
        palette.setColor(QPalette.BrightText, Qt.white)
        
        # Couleurs de sélection
        palette.setColor(QPalette.Highlight, QColor(styles["primary_color"]))
        palette.setColor(QPalette.HighlightedText, QColor(styles["button_text"]))
        
        # Couleurs désactivées
        palette.setColor(QPalette.Disabled, QPalette.WindowText, QColor(styles["disabled_color"]))
        palette.setColor(QPalette.Disabled, QPalette.Text, QColor(styles["disabled_color"]))
        palette.setColor(QPalette.Disabled, QPalette.ButtonText, QColor(styles["disabled_color"]))
        
        # Appliquer la palette
        app.setPalette(palette)
        
        # Appliquer la feuille de style
        app.setStyleSheet(f"""
            QMainWindow {{
                background-color: {styles["app_background"]};
            }}
            QWidget {{
                color: {styles["text_color"]};
                background-color: {styles["window_background"]};
            }}
            QTabWidget::pane {{
                border: 1px solid {styles["border_color"]};
                background-color: {styles["window_background"]};
            }}
            QTabBar::tab {{
                background-color: {styles["tab_background"]};
                border: 1px solid {styles["border_color"]};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: {styles["tab_selected"]};
                border-bottom-color: {styles["tab_selected"]};
            }}
            QGroupBox {{
                font-weight: bold;
                border: 1px solid {styles["border_color"]};
                border-radius: 5px;
                margin-top: 1em;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }}
            QPushButton {{
                background-color: {styles["primary_color"]};
                color: {styles["button_text"]};
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {styles["secondary_color"]};
            }}
            QPushButton:pressed {{
                background-color: {styles["accent_color"]};
            }}
            QPushButton:disabled {{
                background-color: {styles["disabled_color"]};
            }}
            QLineEdit, QDateEdit, QTimeEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
                padding: 5px;
                border: 1px solid {styles["border_color"]};
                border-radius: 3px;
                background-color: {styles["input_background"]};
                color: {styles["text_color"]};
            }}
            QTableWidget {{
                border: 1px solid {styles["border_color"]};
                gridline-color: {styles["border_color"]};
                background-color: {styles["window_background"]};
                alternate-background-color: {styles["table_row_alternate"]};
            }}
            QTableWidget::item {{
                padding: 5px;
            }}
            QHeaderView::section {{
                background-color: {styles["table_header"]};
                padding: 5px;
                border: 1px solid {styles["border_color"]};
            }}
            QScrollBar:vertical {{
                border: none;
                background: {styles["scrollbar_background"]};
                width: 10px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {styles["scrollbar_handle"]};
                min-height: 20px;
                border-radius: 5px;
            }}
            QScrollBar:horizontal {{
                border: none;
                background: {styles["scrollbar_background"]};
                height: 10px;
                margin: 0px;
            }}
            QScrollBar::handle:horizontal {{
                background: {styles["scrollbar_handle"]};
                min-width: 20px;
                border-radius: 5px;
            }}
            QScrollBar::add-line, QScrollBar::sub-line {{
                border: none;
                background: none;
            }}
            QScrollBar::add-page, QScrollBar::sub-page {{
                background: none;
            }}
            QLabel {{
                color: {styles["text_color"]};
            }}
            QCheckBox {{
                color: {styles["text_color"]};
            }}
            QRadioButton {{
                color: {styles["text_color"]};
            }}
            QMenu {{
                background-color: {styles["window_background"]};
                border: 1px solid {styles["border_color"]};
            }}
            QMenu::item {{
                padding: 5px 20px;
            }}
            QMenu::item:selected {{
                background-color: {styles["selected_color"]};
            }}
            QToolTip {{
                background-color: {styles["tooltip_background"]};
                color: {styles["tooltip_text"]};
                border: 1px solid {styles["border_color"]};
                padding: 5px;
            }}
        """)

# Instance globale du gestionnaire de thèmes
theme_manager = ThemeManager()

def get_current_theme() -> Dict[str, Any]:
    """
    Récupère le thème actuel.
    
    Returns:
        Dictionnaire contenant les informations du thème actuel
    """
    return theme_manager.get_theme()

def set_theme(theme_id: str) -> bool:
    """
    Définit le thème actuel.
    
    Args:
        theme_id: ID du thème à définir
        
    Returns:
        True si le thème a été défini avec succès, False sinon
    """
    return theme_manager.set_theme(theme_id)

def apply_theme(app: QApplication) -> None:
    """
    Applique le thème actuel à l'application.
    
    Args:
        app: Instance de QApplication
    """
    theme_manager.apply_theme_to_app(app)

def get_all_themes() -> Dict[str, Dict[str, Any]]:
    """
    Récupère tous les thèmes disponibles.
    
    Returns:
        Dictionnaire contenant tous les thèmes (prédéfinis et personnalisés)
    """
    return theme_manager.get_all_themes()