"""
Module d'optimisation des performances pour les indicateurs KPI.
Ce module fournit des fonctions et classes pour améliorer les performances
des calculs et affichages des indicateurs sans modifier les widgets existants.
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
import threading
import logging
from functools import lru_cache

from models.enums import ProcessStepEnum, StockMovementType, QualityGrade

# Configuration du logging
logger = logging.getLogger(__name__)

class KPICache:
    """Classe de cache pour les indicateurs de performance."""
    
    def __init__(self, ttl=300):  # TTL par défaut: 5 minutes
        """Initialise le cache avec un temps de vie (TTL) en secondes."""
        self.cache = {}
        self.ttl = ttl
        self.lock = threading.RLock()
    
    def get(self, key):
        """Récupère une valeur du cache si elle existe et n'est pas expirée."""
        with self.lock:
            if key in self.cache:
                timestamp, value = self.cache[key]
                if time.time() - timestamp < self.ttl:
                    return value
                else:
                    # Valeur expirée, la supprimer
                    del self.cache[key]
        return None
    
    def set(self, key, value):
        """Stocke une valeur dans le cache avec l'horodatage actuel."""
        with self.lock:
            self.cache[key] = (time.time(), value)
    
    def invalidate(self, key=None):
        """Invalide une entrée spécifique ou tout le cache."""
        with self.lock:
            if key is None:
                self.cache.clear()
            elif key in self.cache:
                del self.cache[key]
    
    def get_or_compute(self, key, compute_func, *args, **kwargs):
        """Récupère du cache ou calcule et stocke si absent."""
        value = self.get(key)
        if value is None:
            value = compute_func(*args, **kwargs)
            self.set(key, value)
        return value


class PerformanceOptimizer:
    """Classe d'optimisation des performances pour les indicateurs KPI."""
    
    def __init__(self, db_manager):
        """Initialise l'optimiseur avec un gestionnaire de base de données."""
        self.db_manager = db_manager
        self.kpi_cache = KPICache()
        self.batch_updates = {}
        self.update_lock = threading.RLock()
    
    def get_reference_dates(self) -> Dict[str, datetime]:
        """Retourne les dates de référence couramment utilisées."""
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        
        return {
            'today': today,
            'start_of_week': start_of_week,
            'start_of_month': start_of_month,
            'yesterday': today - timedelta(days=1),
            'last_week': start_of_week - timedelta(days=7),
            'last_month': (today.replace(day=1) - timedelta(days=1)).replace(day=1)
        }
    
    @lru_cache(maxsize=32)
    def get_cached_stock(self, step: ProcessStepEnum) -> float:
        """Récupère le stock actuel avec mise en cache."""
        return self.db_manager.get_current_stock(step)
    
    def get_optimized_movements(self, step: ProcessStepEnum, 
                               movement_type: StockMovementType,
                               start_date: datetime,
                               end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère les mouvements de stock de manière optimisée avec mise en cache."""
        cache_key = f"movements_{step.value}_{movement_type.value}_{start_date.isoformat()}_{end_date.isoformat() if end_date else 'now'}"
        
        def fetch_movements():
            return self.db_manager.get_stock_movements(
                step=step,
                movement_type=movement_type,
                start_date=start_date,
                end_date=end_date
            )
        
        return self.kpi_cache.get_or_compute(cache_key, fetch_movements)
    
    def calculate_totals(self, movements: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calcule les totaux à partir d'une liste de mouvements."""
        total_quantity = sum(m['quantity'] for m in movements)
        
        # Calculer les statistiques de qualité
        qualities = [m['quality'] for m in movements if m['quality']]
        avg_quality = sum(qualities) / len(qualities) if qualities else 0
        min_quality = min(qualities) if qualities else 0
        max_quality = max(qualities) if qualities else 0
        
        # Calculer l'écart type (implémentation simple)
        if qualities:
            variance = sum((q - avg_quality) ** 2 for q in qualities) / len(qualities)
            std_dev = variance ** 0.5
        else:
            std_dev = 0
        
        return {
            'total_quantity': total_quantity,
            'avg_quality': avg_quality,
            'min_quality': min_quality,
            'max_quality': max_quality,
            'std_dev': std_dev,
            'count': len(movements)
        }
    
    def get_reception_kpis(self, step: ProcessStepEnum = ProcessStepEnum.RECEPTION) -> Dict[str, Any]:
        """Récupère tous les KPIs de réception en une seule opération optimisée."""
        cache_key = f"reception_kpis_{step.value}_{datetime.now().strftime('%Y-%m-%d')}"
        
        def compute_kpis():
            dates = self.get_reference_dates()
            
            # Récupérer le stock actuel
            current_stock = self.get_cached_stock(step)
            
            # Récupérer les mouvements pour aujourd'hui, cette semaine et ce mois
            today_movements = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.RECEPTION,
                start_date=dates['today']
            )
            
            week_movements = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.RECEPTION,
                start_date=dates['start_of_week']
            )
            
            month_movements = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.RECEPTION,
                start_date=dates['start_of_month']
            )
            
            # Calculer les totaux
            today_stats = self.calculate_totals(today_movements)
            week_stats = self.calculate_totals(week_movements)
            month_stats = self.calculate_totals(month_movements)
            
            # Calculer les moyennes
            days_in_month = (datetime.now() - dates['start_of_month']).days + 1
            daily_avg = month_stats['total_quantity'] / days_in_month if days_in_month > 0 else 0
            
            weeks_in_month = days_in_month / 7
            weekly_avg = month_stats['total_quantity'] / weeks_in_month if weeks_in_month > 0 else 0
            
            # Objectif mensuel (simulé pour l'exemple)
            monthly_target = 5000  # Tonnes
            target_completion = (month_stats['total_quantity'] / monthly_target * 100) if monthly_target > 0 else 0
            
            return {
                'current_stock': current_stock,
                'today_total': today_stats['total_quantity'],
                'week_total': week_stats['total_quantity'],
                'month_total': month_stats['total_quantity'],
                'daily_avg': daily_avg,
                'weekly_avg': weekly_avg,
                'target_completion': target_completion,
                'avg_quality': month_stats['avg_quality'],
                'min_quality': month_stats['min_quality'],
                'max_quality': month_stats['max_quality'],
                'std_dev': month_stats['std_dev'],
                'truck_count': today_stats['count'],
                'avg_load': today_stats['total_quantity'] / today_stats['count'] if today_stats['count'] > 0 else 0
            }
        
        return self.kpi_cache.get_or_compute(cache_key, compute_kpis)
    
    def get_crushing_kpis(self, step: ProcessStepEnum = ProcessStepEnum.CONCASSAGE) -> Dict[str, Any]:
        """Récupère tous les KPIs de concassage en une seule opération optimisée."""
        cache_key = f"crushing_kpis_{step.value}_{datetime.now().strftime('%Y-%m-%d')}"
        
        def compute_kpis():
            dates = self.get_reference_dates()
            
            # Récupérer le stock actuel
            current_stock = self.get_cached_stock(step)
            
            # Récupérer les mouvements pour aujourd'hui, cette semaine et ce mois
            today_in = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.RECEPTION,
                start_date=dates['today']
            )
            
            today_out = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.TRANSFER,
                start_date=dates['today']
            )
            
            month_in = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.RECEPTION,
                start_date=dates['start_of_month']
            )
            
            month_out = self.get_optimized_movements(
                step=step,
                movement_type=StockMovementType.TRANSFER,
                start_date=dates['start_of_month']
            )
            
            # Calculer les totaux
            today_in_stats = self.calculate_totals(today_in)
            today_out_stats = self.calculate_totals(today_out)
            month_in_stats = self.calculate_totals(month_in)
            month_out_stats = self.calculate_totals(month_out)
            
            # Calculer les rendements
            today_yield = (today_out_stats['total_quantity'] / today_in_stats['total_quantity'] * 100) if today_in_stats['total_quantity'] > 0 else 0
            month_yield = (month_out_stats['total_quantity'] / month_in_stats['total_quantity'] * 100) if month_in_stats['total_quantity'] > 0 else 0
            
            # Calculer les moyennes
            days_in_month = (datetime.now() - dates['start_of_month']).days + 1
            daily_avg_in = month_in_stats['total_quantity'] / days_in_month if days_in_month > 0 else 0
            daily_avg_out = month_out_stats['total_quantity'] / days_in_month if days_in_month > 0 else 0
            
            return {
                'current_stock': current_stock,
                'today_in': today_in_stats['total_quantity'],
                'today_out': today_out_stats['total_quantity'],
                'month_in': month_in_stats['total_quantity'],
                'month_out': month_out_stats['total_quantity'],
                'today_yield': today_yield,
                'month_yield': month_yield,
                'daily_avg_in': daily_avg_in,
                'daily_avg_out': daily_avg_out,
                'avg_quality_in': month_in_stats['avg_quality'],
                'avg_quality_out': month_out_stats['avg_quality']
            }
        
        return self.kpi_cache.get_or_compute(cache_key, compute_kpis)
    
    def register_batch_update(self, widget_id, update_func):
        """Enregistre une fonction de mise à jour pour un traitement par lots."""
        with self.update_lock:
            self.batch_updates[widget_id] = update_func
    
    def execute_batch_updates(self):
        """Exécute toutes les mises à jour enregistrées en une seule fois."""
        with self.update_lock:
            for widget_id, update_func in self.batch_updates.items():
                try:
                    update_func()
                except Exception as e:
                    logger.error(f"Erreur lors de la mise à jour du widget {widget_id}: {str(e)}")
            
            # Vider la liste des mises à jour
            self.batch_updates.clear()
    
    def invalidate_cache(self, step: Optional[ProcessStepEnum] = None):
        """Invalide le cache pour une étape spécifique ou tout le cache."""
        if step:
            self.kpi_cache.invalidate(f"reception_kpis_{step.value}_{datetime.now().strftime('%Y-%m-%d')}")
            self.kpi_cache.invalidate(f"crushing_kpis_{step.value}_{datetime.now().strftime('%Y-%m-%d')}")
            # Invalider également les caches de mouvements
            for movement_type in StockMovementType:
                for date_key in ['today', 'start_of_week', 'start_of_month']:
                    date = self.get_reference_dates()[date_key]
                    cache_key = f"movements_{step.value}_{movement_type.value}_{date.isoformat()}_now"
                    self.kpi_cache.invalidate(cache_key)
        else:
            # Invalider tout le cache
            self.kpi_cache.invalidate()
        
        # Vider le cache LRU également
        self.get_cached_stock.cache_clear()


# Fonction d'aide pour optimiser un onglet existant
def optimize_tab_kpis(tab, optimizer):
    """
    Optimise les indicateurs de performance d'un onglet existant.
    
    Args:
        tab: L'onglet à optimiser
        optimizer: L'instance de PerformanceOptimizer à utiliser
    """
    # Sauvegarder la méthode originale
    original_update_kpi = tab.update_kpi
    
    # Remplacer par une version optimisée
    def optimized_update_kpi():
        """Version optimisée de la méthode update_kpi."""
        try:
            start_time = time.time()
            
            # Déterminer le type d'onglet et appliquer l'optimisation appropriée
            if hasattr(tab, 'stock_item') and hasattr(tab, 'today_item'):
                # Onglet de réception
                kpis = optimizer.get_reception_kpis()
                
                # Mettre à jour les widgets avec les valeurs calculées
                if hasattr(tab, 'stock_item'):
                    tab.stock_item.update_value(f"{kpis['current_stock']:.1f}")
                if hasattr(tab, 'today_item'):
                    tab.today_item.update_value(f"{kpis['today_total']:.1f}")
                if hasattr(tab, 'week_item'):
                    tab.week_item.update_value(f"{kpis['week_total']:.1f}")
                if hasattr(tab, 'month_item'):
                    tab.month_item.update_value(f"{kpis['month_total']:.1f}")
                if hasattr(tab, 'daily_avg_item'):
                    tab.daily_avg_item.update_value(f"{kpis['daily_avg']:.1f}")
                if hasattr(tab, 'weekly_avg_item'):
                    tab.weekly_avg_item.update_value(f"{kpis['weekly_avg']:.1f}")
                if hasattr(tab, 'target_completion_item'):
                    tab.target_completion_item.update_value(f"{kpis['target_completion']:.1f}")
                if hasattr(tab, 'avg_quality_item'):
                    tab.avg_quality_item.update_value(f"{kpis['avg_quality']:.1f}")
                if hasattr(tab, 'min_quality_item'):
                    tab.min_quality_item.update_value(f"{kpis['min_quality']:.1f}")
                if hasattr(tab, 'max_quality_item'):
                    tab.max_quality_item.update_value(f"{kpis['max_quality']:.1f}")
                if hasattr(tab, 'std_dev_item'):
                    tab.std_dev_item.update_value(f"{kpis['std_dev']:.1f}")
                if hasattr(tab, 'trucks_item'):
                    tab.trucks_item.update_value(str(kpis['truck_count']))
                if hasattr(tab, 'avg_load_item'):
                    tab.avg_load_item.update_value(f"{kpis['avg_load']:.1f}")
                
            elif hasattr(tab, 'input_item') or hasattr(tab, 'output_item'):
                # Onglet de concassage
                kpis = optimizer.get_crushing_kpis()
                
                # Mettre à jour les widgets avec les valeurs calculées
                if hasattr(tab, 'stock_item'):
                    tab.stock_item.update_value(f"{kpis['current_stock']:.1f}")
                if hasattr(tab, 'input_item'):
                    tab.input_item.update_value(f"{kpis['today_in']:.1f}")
                if hasattr(tab, 'output_item'):
                    tab.output_item.update_value(f"{kpis['today_out']:.1f}")
                if hasattr(tab, 'yield_item'):
                    tab.yield_item.update_value(f"{kpis['today_yield']:.1f}")
                if hasattr(tab, 'month_input_item'):
                    tab.month_input_item.update_value(f"{kpis['month_in']:.1f}")
                if hasattr(tab, 'month_output_item'):
                    tab.month_output_item.update_value(f"{kpis['month_out']:.1f}")
                if hasattr(tab, 'month_yield_item'):
                    tab.month_yield_item.update_value(f"{kpis['month_yield']:.1f}")
                if hasattr(tab, 'daily_avg_in_item'):
                    tab.daily_avg_in_item.update_value(f"{kpis['daily_avg_in']:.1f}")
                if hasattr(tab, 'daily_avg_out_item'):
                    tab.daily_avg_out_item.update_value(f"{kpis['daily_avg_out']:.1f}")
                if hasattr(tab, 'quality_in_item'):
                    tab.quality_in_item.update_value(f"{kpis['avg_quality_in']:.1f}")
                if hasattr(tab, 'quality_out_item'):
                    tab.quality_out_item.update_value(f"{kpis['avg_quality_out']:.1f}")
            
            else:
                # Si on ne reconnaît pas le type d'onglet, utiliser la méthode originale
                original_update_kpi()
                return
            
            end_time = time.time()
            logger.debug(f"Mise à jour optimisée des KPIs en {end_time - start_time:.3f} secondes")
            
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour optimisée des KPIs: {str(e)}")
            # En cas d'erreur, revenir à la méthode originale
            original_update_kpi()
    
    # Remplacer la méthode originale par la version optimisée
    tab.update_kpi = optimized_update_kpi
    
    # Retourner la méthode originale pour pouvoir la restaurer si nécessaire
    return original_update_kpi