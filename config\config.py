import os
import json
from typing import Dict, Any
from pathlib import Path

class Config:
    """Classe de gestion de la configuration de l'application."""
    
    def __init__(self):
        self.config_file = Path("config/config.json")
        self.default_config = {
            "database": {
                "url": "sqlite:///phosphate.db",
                "echo": False
            },
            "ui": {
                "theme": "light",
                "language": "fr",
                "refresh_interval": 30,  # secondes
                "date_format": "%d/%m/%Y",
                "time_format": "%H:%M",
                "decimal_separator": ",",
                "thousand_separator": " "
            },
            "production": {
                "default_hours": 8,
                "min_quality": 0,
                "max_quality": 100,
                "min_quantity": 0,
                "max_quantity": 10000
            },
            "reports": {
                "default_period": "daily",
                "export_path": "exports",
                "excel_template": "templates/report_template.xlsx"
            },
            "logging": {
                "level": "INFO",
                "file": "app.log",
                "max_size": 10485760,  # 10 MB
                "backup_count": 5
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Charge la configuration depuis le fichier."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return self.default_config
        except Exception as e:
            print(f"Erreur lors du chargement de la configuration: {e}")
            return self.default_config
    
    def save_config(self) -> bool:
        """Sauvegarde la configuration dans le fichier."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde de la configuration: {e}")
            return False
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """Récupère une valeur de configuration."""
        try:
            return self.config[section][key]
        except KeyError:
            return default
    
    def set(self, section: str, key: str, value: Any) -> bool:
        """Définit une valeur de configuration."""
        try:
            if section not in self.config:
                self.config[section] = {}
            self.config[section][key] = value
            return self.save_config()
        except Exception as e:
            print(f"Erreur lors de la définition de la configuration: {e}")
            return False
    
    def get_database_url(self) -> str:
        """Récupère l'URL de la base de données."""
        return self.get("database", "url", "sqlite:///phosphate.db")
    
    def get_ui_theme(self) -> str:
        """Récupère le thème de l'interface."""
        return self.get("ui", "theme", "light")
    
    def get_refresh_interval(self) -> int:
        """Récupère l'intervalle de rafraîchissement."""
        return self.get("ui", "refresh_interval", 30)
    
    def get_date_format(self) -> str:
        """Récupère le format de date."""
        return self.get("ui", "date_format", "%d/%m/%Y")
    
    def get_time_format(self) -> str:
        """Récupère le format d'heure."""
        return self.get("ui", "time_format", "%H:%M")
    
    def get_decimal_separator(self) -> str:
        """Récupère le séparateur décimal."""
        return self.get("ui", "decimal_separator", ",")
    
    def get_thousand_separator(self) -> str:
        """Récupère le séparateur de milliers."""
        return self.get("ui", "thousand_separator", " ")
    
    def get_default_hours(self) -> float:
        """Récupère les heures de travail par défaut."""
        return self.get("production", "default_hours", 8.0)
    
    def get_quality_limits(self) -> tuple:
        """Récupère les limites de qualité."""
        return (
            self.get("production", "min_quality", 0),
            self.get("production", "max_quality", 100)
        )
    
    def get_quantity_limits(self) -> tuple:
        """Récupère les limites de quantité."""
        return (
            self.get("production", "min_quantity", 0),
            self.get("production", "max_quantity", 10000)
        )
    
    def get_export_path(self) -> str:
        """Récupère le chemin d'export."""
        path = self.get("reports", "export_path", "exports")
        os.makedirs(path, exist_ok=True)
        return path
    
    def get_excel_template(self) -> str:
        """Récupère le chemin du template Excel."""
        return self.get("reports", "excel_template", "templates/report_template.xlsx")
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Récupère la configuration du logging."""
        return self.get("logging", None, {}) 