"""
Base class pour améliorer les layouts des onglets existants.
Fournit une structure en splitter horizontal avec panneaux réductibles.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSplitter, 
                            QScrollArea, QGroupBox, QToolButton, QFrame, QLabel,
                            QGridLayout, QFormLayout)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont


class CollapsiblePanel(QWidget):
    """Panneau réductible/extensible avec titre."""
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        self.content_layout.setSpacing(10)
        
        self.is_collapsed = False
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface du panneau."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # En-tête avec bouton toggle
        header = QFrame()
        header.setFrameStyle(QFrame.Box)
        header.setStyleSheet("""
            QFrame {
                background-color: #1E3A8A;
                border: 1px solid #1E40AF;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(10, 5, 10, 5)
        
        # Bouton d'expansion/réduction
        self.toggle_button = QToolButton()
        self.toggle_button.setArrowType(Qt.DownArrow)
        self.toggle_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                color: white;
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
        """)
        self.toggle_button.clicked.connect(self.toggle_content)
        header_layout.addWidget(self.toggle_button)
        
        # Titre
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Zone de contenu avec scroll
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.content_widget)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)
        
        main_layout.addWidget(header)
        main_layout.addWidget(self.scroll_area)
        
    def toggle_content(self):
        """Bascule l'affichage du contenu."""
        self.is_collapsed = not self.is_collapsed
        self.scroll_area.setVisible(not self.is_collapsed)
        
        if self.is_collapsed:
            self.toggle_button.setArrowType(Qt.RightArrow)
        else:
            self.toggle_button.setArrowType(Qt.DownArrow)
    
    def add_widget(self, widget):
        """Ajoute un widget au contenu."""
        self.content_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Ajoute un layout au contenu."""
        self.content_layout.addLayout(layout)
    
    def add_stretch(self):
        """Ajoute un stretch au contenu."""
        self.content_layout.addStretch()


class ImprovedLayoutBase(QWidget):
    """Classe de base pour des layouts améliorés avec splitter horizontal."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.left_panel = None
        self.right_panel = None
        self.splitter = None
        self.init_layout()
    
    def init_layout(self):
        """Initialise le layout de base avec splitter horizontal."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # Splitter horizontal
        self.splitter = QSplitter(Qt.Horizontal)
        
        # Panneau gauche (contrôles et formulaires)
        self.left_panel = QWidget()
        self.left_panel.setMinimumWidth(300)
        self.left_panel.setMaximumWidth(450)
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)
        
        # Panneau droit (données et tableaux)
        self.right_panel = QWidget() 
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)
        
        # Ajouter les panneaux au splitter
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        
        # Définir les tailles initiales (30% gauche, 70% droite)
        self.splitter.setSizes([300, 700])
        
        main_layout.addWidget(self.splitter)
    
    def add_to_left_panel(self, widget):
        """Ajoute un widget au panneau gauche."""
        if self.left_panel:
            self.left_panel.layout().addWidget(widget)
    
    def add_to_right_panel(self, widget):
        """Ajoute un widget au panneau droit."""
        if self.right_panel:
            self.right_panel.layout().addWidget(widget)
    
    def add_stretch_to_left(self):
        """Ajoute un stretch au panneau gauche."""
        if self.left_panel:
            self.left_panel.layout().addStretch()
    
    def add_stretch_to_right(self):
        """Ajoute un stretch au panneau droit."""
        if self.right_panel:
            self.right_panel.layout().addStretch()
    
    def create_kpi_panel(self, title="Indicateurs"):
        """Crée un panneau KPI standardisé."""
        kpi_panel = CollapsiblePanel(title)
        
        # Grid pour les KPI
        kpi_grid = QGridLayout()
        kpi_widget = QWidget()
        kpi_widget.setLayout(kpi_grid)
        kpi_panel.add_widget(kpi_widget)
        
        return kpi_panel, kpi_grid
    
    def create_form_panel(self, title="Saisie"):
        """Crée un panneau de formulaire standardisé."""
        form_panel = CollapsiblePanel(title)
        
        # Form layout
        form_layout = QFormLayout()
        form_widget = QWidget()
        form_widget.setLayout(form_layout)
        form_panel.add_widget(form_widget)
        
        return form_panel, form_layout
    
    def create_controls_panel(self, title="Contrôles"):
        """Crée un panneau de contrôles standardisé."""
        controls_panel = CollapsiblePanel(title)
        
        # Vertical layout pour les contrôles
        controls_layout = QVBoxLayout()
        controls_widget = QWidget()
        controls_widget.setLayout(controls_layout)
        controls_panel.add_widget(controls_widget)
        
        return controls_panel, controls_layout


class KPICard(QFrame):
    """Widget pour afficher un KPI avec titre et valeur."""
    
    def __init__(self, title, value="--", unit="", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface de la carte KPI."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame:hover {
                border-color: #1E3A8A;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 5px;
            }
        """)
        
        # Valeur
        self.value_label = QLabel(str(self.value))
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet("""
            QLabel {
                color: #1E3A8A;
                font-size: 24px;
                font-weight: bold;
                margin: 5px 0;
            }
        """)
        
        # Unité
        unit_label = QLabel(self.unit)
        unit_label.setAlignment(Qt.AlignCenter)
        unit_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                font-weight: 500;
            }
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(self.value_label)
        if self.unit:
            layout.addWidget(unit_label)
    
    def update_value(self, value, unit=None):
        """Met à jour la valeur affichée."""
        self.value = value
        self.value_label.setText(str(value))
        if unit:
            self.unit = unit
