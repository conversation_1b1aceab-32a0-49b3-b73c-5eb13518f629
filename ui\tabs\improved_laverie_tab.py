"""
Onglet laverie amélioré avec layout optimisé.
Utilise un splitter horizontal pour désencombrer l'interface.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QGroupBox,
                            QMessageBox, QDateTimeEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QDateEdit, QComboBox, QTabWidget)
from PyQt5.QtCore import Qt, QDateTime, QTimer, QDate
from PyQt5.QtGui import QPalette, QColor
from ui.layouts.improved_layout_base import ImprovedLayoutBase, CollapsiblePanel, KPICard
from models.enums import ProcessStepEnum, ResourceType, MovementTypeEnum
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any


class ImprovedLaverieTab(ImprovedLayoutBase):
    """Onglet de gestion de la laverie avec layout amélioré."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.process_step = ProcessStepEnum.LAVERIE
        self.logger = logging.getLogger(__name__)
        
        # Widgets de formulaire production
        self.prod_date_input = QDateEdit()
        self.prod_date_input.setDate(QDate.currentDate())
        self.prod_hours_input = QLineEdit()
        self.prod_quantity_input = QLineEdit()
        self.prod_quality_input = QLineEdit()
        self.prod_quantity_used_input = QLineEdit()
        
        # Widgets de formulaire consommation
        self.cons_date_input = QDateEdit()
        self.cons_date_input.setDate(QDate.currentDate())
        self.cons_resource_combo = QComboBox()
        self.cons_quantity_input = QLineEdit()
        
        # Widgets de formulaire arrêts
        self.downtime_date_input = QDateEdit()
        self.downtime_date_input.setDate(QDate.currentDate())
        self.downtime_duration_input = QLineEdit()
        self.downtime_reason_input = QLineEdit()
        
        # KPI Cards
        self.input_stock_card = None
        self.output_stock_card = None
        self.efficiency_card = None
        self.water_usage_card = None
        
        # Tables
        self.production_table = QTableWidget()
        self.consumption_table = QTableWidget()
        self.downtime_table = QTableWidget()
        
        self.init_improved_ui()
        self.setup_timers()
    
    def init_improved_ui(self):
        """Initialise l'interface améliorée."""
        
        # === PANNEAU GAUCHE (Contrôles) ===
        
        # 1. Panel KPI
        kpi_panel, kpi_grid = self.create_kpi_panel("📊 Indicateurs Laverie")
        
        # Créer les cartes KPI
        self.input_stock_card = KPICard("Stock Entrée", "0.0", "tonnes")
        self.output_stock_card = KPICard("Stock Sortie", "0.0", "tonnes")
        self.efficiency_card = KPICard("Efficacité", "0.0", "%")
        self.water_usage_card = KPICard("Consommation Eau", "0.0", "m³")
        
        # Disposition en grille 2x2
        kpi_grid.addWidget(self.input_stock_card, 0, 0)
        kpi_grid.addWidget(self.output_stock_card, 0, 1)
        kpi_grid.addWidget(self.efficiency_card, 1, 0)
        kpi_grid.addWidget(self.water_usage_card, 1, 1)
        
        self.add_to_left_panel(kpi_panel)
        
        # 2. Panel Saisie Production
        production_panel, production_layout = self.create_form_panel("🏭 Nouvelle Production")
        
        production_layout.addRow("Date:", self.prod_date_input)
        
        self.prod_hours_input.setPlaceholderText("Ex: 8.0")
        production_layout.addRow("Heures production:", self.prod_hours_input)
        
        self.prod_quantity_input.setPlaceholderText("Ex: 120.5")
        production_layout.addRow("Quantité produite (t):", self.prod_quantity_input)
        
        self.prod_quality_input.setPlaceholderText("Ex: 31.2")
        production_layout.addRow("Qualité P2O5 (%):", self.prod_quality_input)
        
        self.prod_quantity_used_input.setPlaceholderText("Ex: 140.0")
        production_layout.addRow("Quantité utilisée (t):", self.prod_quantity_used_input)
        
        # Bouton d'ajout production
        add_prod_button = QPushButton("✅ Ajouter Production")
        add_prod_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        add_prod_button.clicked.connect(self.add_production)
        production_layout.addRow("", add_prod_button)
        
        self.add_to_left_panel(production_panel)
        
        # 3. Panel Consommation Ressources
        consumption_panel, consumption_layout = self.create_form_panel("💧 Consommation Ressources")
        
        consumption_layout.addRow("Date:", self.cons_date_input)
        
        # ComboBox pour les ressources
        self.cons_resource_combo.addItems(["EAU", "REACTIVE", "ENERGIE"])
        consumption_layout.addRow("Type de ressource:", self.cons_resource_combo)
        
        self.cons_quantity_input.setPlaceholderText("Ex: 45.2")
        consumption_layout.addRow("Quantité:", self.cons_quantity_input)
        
        # Bouton d'ajout consommation
        add_cons_button = QPushButton("💧 Ajouter Consommation")
        add_cons_button.setStyleSheet("""
            QPushButton {
                background-color: #0EA5E9;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0284C7;
            }
        """)
        add_cons_button.clicked.connect(self.add_consumption)
        consumption_layout.addRow("", add_cons_button)
        
        self.add_to_left_panel(consumption_panel)
        
        # 4. Panel Arrêts
        downtime_panel, downtime_layout = self.create_form_panel("⏸️ Temps d'Arrêt")
        
        downtime_layout.addRow("Date:", self.downtime_date_input)
        
        self.downtime_duration_input.setPlaceholderText("Ex: 2.5")
        downtime_layout.addRow("Durée (heures):", self.downtime_duration_input)
        
        self.downtime_reason_input.setPlaceholderText("Ex: Nettoyage filtre")
        downtime_layout.addRow("Raison:", self.downtime_reason_input)
        
        # Bouton d'ajout arrêt
        add_downtime_button = QPushButton("⏸️ Ajouter Arrêt")
        add_downtime_button.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #B91C1C;
            }
        """)
        add_downtime_button.clicked.connect(self.add_downtime)
        downtime_layout.addRow("", add_downtime_button)
        
        self.add_to_left_panel(downtime_panel)
        
        # 5. Panel Outils
        tools_panel, tools_layout = self.create_form_panel("🛠️ Outils")
        
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #7C3AED;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6D28D9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_tab)
        
        clear_button = QPushButton("🗑️ Vider Formulaires")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        clear_button.clicked.connect(self.clear_inputs)
        
        tools_layout.addRow(refresh_button)
        tools_layout.addRow(clear_button)
        
        self.add_to_left_panel(tools_panel)
        self.add_stretch_to_left()
        
        # === PANNEAU DROIT (Données) ===
        
        # Créer des panneaux séparés pour chaque type de données
        
        # Panel Production
        production_data_panel = CollapsiblePanel("📈 Historique Production")
        
        self.production_table.setColumnCount(6)
        self.production_table.setHorizontalHeaderLabels([
            "Date", "Heures", "Quantité (t)", "Qualité (%)", "Utilisée (t)", "Actions"
        ])
        
        self.production_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #F9FAFB;
                selection-background-color: #DBEAFE;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #10B981;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.production_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.production_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.production_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.production_table.setAlternatingRowColors(True)
        
        production_data_panel.add_widget(self.production_table)
        self.add_to_right_panel(production_data_panel)
        
        # Panel Consommations
        consumption_data_panel = CollapsiblePanel("💧 Historique Consommations")
        
        self.consumption_table.setColumnCount(4)
        self.consumption_table.setHorizontalHeaderLabels([
            "Date", "Ressource", "Quantité", "Actions"
        ])
        
        self.consumption_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #F0F9FF;
                selection-background-color: #DBEAFE;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #0EA5E9;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.consumption_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.consumption_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.consumption_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.consumption_table.setAlternatingRowColors(True)
        
        consumption_data_panel.add_widget(self.consumption_table)
        self.add_to_right_panel(consumption_data_panel)
        
        # Panel Arrêts
        downtime_data_panel = CollapsiblePanel("⏸️ Historique Arrêts")
        
        self.downtime_table.setColumnCount(4)
        self.downtime_table.setHorizontalHeaderLabels([
            "Date", "Durée (h)", "Raison", "Actions"
        ])
        
        self.downtime_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #FEF2F2;
                selection-background-color: #FEE2E2;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #DC2626;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.downtime_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.downtime_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.downtime_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.downtime_table.setAlternatingRowColors(True)
        
        downtime_data_panel.add_widget(self.downtime_table)
        self.add_to_right_panel(downtime_data_panel)
        
        # Chargement initial des données
        self.refresh_tab()
    
    def setup_timers(self):
        """Configure les timers pour la mise à jour automatique."""
        # Timer pour mettre à jour les KPI toutes les 30 secondes
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_indicators)
        self.kpi_timer.start(30000)  # 30 secondes
    
    def add_production(self):
        """Ajoute une nouvelle production."""
        try:
            # Validation des données
            if not self.prod_hours_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir les heures de production.")
                return
                
            if not self.prod_quantity_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la quantité produite.")
                return
            
            hours = float(self.prod_hours_input.text())
            quantity = float(self.prod_quantity_input.text())
            quality = self.prod_quality_input.text().strip()
            quantity_used = float(self.prod_quantity_used_input.text()) if self.prod_quantity_used_input.text().strip() else None
            
            if hours <= 0 or quantity <= 0:
                QMessageBox.warning(self, "Erreur", "Les valeurs doivent être positives.")
                return
            
            # Ajouter à la base de données
            self.db_manager.add_production(
                step=self.process_step,
                quantity=quantity,
                production_hours=hours,
                quality=quality,
                quantity_used=quantity_used
            )
            
            QMessageBox.information(
                self, "✅ Succès", 
                f"Production ajoutée: {quantity} tonnes en {hours} heures"
            )
            
            self.clear_production_inputs()
            self.refresh_tab()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir des valeurs numériques valides.")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout de production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def add_consumption(self):
        """Ajoute une nouvelle consommation."""
        try:
            if not self.cons_quantity_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une quantité.")
                return
            
            resource_text = self.cons_resource_combo.currentText()
            quantity = float(self.cons_quantity_input.text())
            
            if quantity <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive.")
                return
            
            # Mapper le texte vers l'enum
            resource_mapping = {
                "EAU": ResourceType.WATER,
                "REACTIVE": ResourceType.REACTIVE,
                "ENERGIE": ResourceType.ENERGY
            }
            
            resource_type = resource_mapping.get(resource_text, ResourceType.WATER)
            
            # Ajouter à la base de données
            self.db_manager.add_resource_consumption(
                resource_type=resource_type,
                quantity=quantity,
                step=self.process_step
            )
            
            QMessageBox.information(
                self, "✅ Succès", 
                f"Consommation ajoutée: {quantity} {resource_text}"
            )
            
            self.clear_consumption_inputs()
            self.refresh_tab()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir une valeur numérique valide.")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout de consommation: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def add_downtime(self):
        """Ajoute un nouvel arrêt."""
        try:
            if not self.downtime_duration_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la durée.")
                return
                
            if not self.downtime_reason_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir la raison.")
                return
            
            duration_hours = float(self.downtime_duration_input.text())
            reason = self.downtime_reason_input.text().strip()
            
            if duration_hours <= 0:
                QMessageBox.warning(self, "Erreur", "La durée doit être positive.")
                return
            
            # Convertir en minutes pour la base de données
            duration_minutes = int(duration_hours * 60)
            
            # Ajouter à la base de données
            self.db_manager.add_downtime(
                step=self.process_step,
                duration_minutes=duration_minutes,
                reason=reason,
                downtime_date=self.downtime_date_input.date().toPyDate()
            )
            
            QMessageBox.information(
                self, "✅ Succès", 
                f"Arrêt ajouté: {duration_hours} heures - {reason}"
            )
            
            self.clear_downtime_inputs()
            self.refresh_tab()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir des valeurs valides.")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout d'arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def clear_production_inputs(self):
        """Vide les champs de production."""
        self.prod_hours_input.clear()
        self.prod_quantity_input.clear()
        self.prod_quality_input.clear()
        self.prod_quantity_used_input.clear()
        self.prod_date_input.setDate(QDate.currentDate())
    
    def clear_consumption_inputs(self):
        """Vide les champs de consommation."""
        self.cons_quantity_input.clear()
        self.cons_date_input.setDate(QDate.currentDate())
    
    def clear_downtime_inputs(self):
        """Vide les champs d'arrêt."""
        self.downtime_duration_input.clear()
        self.downtime_reason_input.clear()
        self.downtime_date_input.setDate(QDate.currentDate())
    
    def clear_inputs(self):
        """Vide tous les formulaires."""
        self.clear_production_inputs()
        self.clear_consumption_inputs()
        self.clear_downtime_inputs()
    
    def update_indicators(self):
        """Met à jour les indicateurs KPI."""
        try:
            self.logger.info(f"Updating indicators for Laverie tab, process step: {self.process_step.name}")
            
            # Stock d'entrée (concassage disponible pour la laverie)
            input_stock = self.db_manager.get_available_crushing_stock()
            self.input_stock_card.update_value(f"{input_stock:.1f}")
            
            # Stock de sortie (laverie)
            output_stock = self.db_manager.get_current_stock(self.process_step)
            self.output_stock_card.update_value(f"{output_stock:.1f}")
            
            # Récupérer les données de production pour calculer l'efficacité
            productions = self.db_manager.get_production_summary(self.process_step)
            
            # Calculer les totaux avec vérification des valeurs nulles
            total_quantity_produced = sum(prod['quantity'] for prod in productions if prod['quantity'] is not None)
            total_quantity_used = sum(prod['quantity_used'] for prod in productions if prod['quantity_used'] is not None)
            
            # Calculer l'efficacité (rendement)
            if total_quantity_used > 0:
                efficiency = (total_quantity_produced / total_quantity_used) * 100
                self.efficiency_card.update_value(f"{efficiency:.1f}")
            else:
                self.efficiency_card.update_value("N/A")
            
            # Récupérer les données de consommation d'eau
            consumptions = self.db_manager.get_resource_consumption_summary(
                self.process_step, ResourceType.WATER)
            
            # Calculer la consommation d'eau totale
            total_water = sum(cons['quantity'] for cons in consumptions if cons['quantity'] is not None)
            
            # Calculer la consommation d'eau par tonne produite
            if total_quantity_produced > 0:
                water_per_ton = total_water / total_quantity_produced
                self.water_usage_card.update_value(f"{water_per_ton:.1f}")
            else:
                self.water_usage_card.update_value("N/A")
            
            self.logger.info(f"Indicateurs mis à jour - Stock entrée: {input_stock}, Stock sortie: {output_stock}, " +
                            f"Efficacité: {efficiency if 'efficiency' in locals() else 'N/A'}, " +
                            f"Eau/tonne: {water_per_ton if 'water_per_ton' in locals() else 'N/A'}")
            
        except Exception as e:
            self.logger.error(f"Erreur mise à jour indicateurs laverie: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    def refresh_tab(self):
        """Actualise tous les données de l'onglet."""
        self.logger.info(f"Refreshing Laverie tab for process step: {self.process_step.name}")
        self.load_production_data()
        self.load_consumption_data()
        self.load_downtime_data()
        self.update_indicators()
    
    def load_production_data(self):
        """Charge les données de production."""
        try:
            productions = self.db_manager.get_production_summary(self.process_step)
            
            # Prendre les 20 dernières
            productions = productions[-20:] if len(productions) > 20 else productions
            productions.reverse()  # Plus récent en premier
            
            self.production_table.setRowCount(len(productions))
            
            for row, prod in enumerate(productions):
                # Date
                date_str = prod['production_date'].strftime("%d/%m/%Y")
                self.production_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Heures
                hours_value = prod.get('production_hours', 0)
                hours_str = f"{hours_value:.2f}" if hours_value is not None else "N/A"
                self.production_table.setItem(row, 1, QTableWidgetItem(hours_str))
                
                # Quantité
                self.production_table.setItem(row, 2, QTableWidgetItem(f"{prod['quantity']:.2f}"))
                
                # Qualité
                quality_str = str(prod.get('quality', '--'))
                self.production_table.setItem(row, 3, QTableWidgetItem(quality_str))
                
                # Quantité utilisée
                used_value = prod.get('quantity_used')
                used_str = f"{used_value:.2f}" if used_value is not None else "N/A"
                self.production_table.setItem(row, 4, QTableWidgetItem(used_str))
                
                # Actions
                self.production_table.setItem(row, 5, QTableWidgetItem("Modifier"))
                
        except Exception as e:
            self.logger.error(f"Erreur chargement données production: {str(e)}")
            self.production_table.setRowCount(1)
            self.production_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            for col in range(1, 6):
                self.production_table.setItem(0, col, QTableWidgetItem("--"))
    
    def load_consumption_data(self):
        """Charge les données de consommation."""
        try:
            consumptions = self.db_manager.get_resource_consumption_summary(self.process_step)
            
            # Prendre les 20 dernières
            consumptions = consumptions[-20:] if len(consumptions) > 20 else consumptions
            consumptions.reverse()  # Plus récent en premier
            
            self.consumption_table.setRowCount(len(consumptions))
            
            for row, cons in enumerate(consumptions):
                # Date
                date_str = cons['consumption_date'].strftime("%d/%m/%Y")
                self.consumption_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Ressource
                resource_str = cons.get('resource_type', 'N/A')
                self.consumption_table.setItem(row, 1, QTableWidgetItem(str(resource_str)))
                
                # Quantité
                quantity_str = f"{cons['quantity']:.2f}"
                self.consumption_table.setItem(row, 2, QTableWidgetItem(quantity_str))
                
                # Actions
                self.consumption_table.setItem(row, 3, QTableWidgetItem("Modifier"))
                
        except Exception as e:
            self.logger.error(f"Erreur chargement données consommation: {str(e)}")
            self.consumption_table.setRowCount(1)
            self.consumption_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            for col in range(1, 4):
                self.consumption_table.setItem(0, col, QTableWidgetItem("--"))
    
    def load_downtime_data(self):
        """Charge les données d'arrêts."""
        try:
            downtimes = self.db_manager.get_downtime_summary(self.process_step)
            
            # Prendre les 20 derniers
            downtimes = downtimes[-20:] if len(downtimes) > 20 else downtimes
            downtimes.reverse()  # Plus récent en premier
            
            self.downtime_table.setRowCount(len(downtimes))
            
            for row, downtime in enumerate(downtimes):
                # Date
                date_str = downtime['downtime_date'].strftime("%d/%m/%Y")
                self.downtime_table.setItem(row, 0, QTableWidgetItem(date_str))
                
                # Durée (convertir minutes en heures)
                duration_hours = downtime['duration_minutes'] / 60.0
                duration_str = f"{duration_hours:.2f}"
                self.downtime_table.setItem(row, 1, QTableWidgetItem(duration_str))
                
                # Raison
                reason_str = downtime.get('reason', 'N/A')
                self.downtime_table.setItem(row, 2, QTableWidgetItem(str(reason_str)))
                
                # Actions
                self.downtime_table.setItem(row, 3, QTableWidgetItem("Modifier"))
                
        except Exception as e:
            self.logger.error(f"Erreur chargement données arrêts: {str(e)}")
            self.downtime_table.setRowCount(1)
            self.downtime_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            for col in range(1, 4):
                self.downtime_table.setItem(0, col, QTableWidgetItem("--"))
