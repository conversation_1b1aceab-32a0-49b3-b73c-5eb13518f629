from datetime import datetime
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship, declarative_base
from .enums import ProcessStepEnum, ResourceType, StockMovementType, QualityGrade

Base = declarative_base()

class ProcessStep(Base):
    """Modèle pour les étapes du processus."""
    __tablename__ = 'process_steps'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(String(200))
    
    # Relations
    productions = relationship("Production", back_populates="step")
    downtimes = relationship("Downtime", back_populates="step")
    stock_movements = relationship("StockMovement", back_populates="step", foreign_keys="StockMovement.step_id")

class Production(Base):
    """Modèle pour les productions."""
    __tablename__ = 'productions'
    
    id = Column(Integer, primary_key=True)
    quantity = Column(Float, nullable=False)
    quality = Column(String(50), nullable=True)  # Changé pour accepter à la fois des pourcentages et des énumérations
    quantity_used = Column(Float, nullable=True)
    production_hours = Column(Float, nullable=True)
    production_date = Column(DateTime, default=datetime.utcnow)
    step_id = Column(Integer, ForeignKey('process_steps.id'))
    
    # Relations
    step = relationship("ProcessStep", back_populates="productions")

class Downtime(Base):
    """Modèle pour les arrêts de production."""
    __tablename__ = 'downtimes'
    
    id = Column(Integer, primary_key=True)
    reason = Column(String(100), nullable=False)  # Raison en texte libre
    duration = Column(Float, nullable=False)  # en minutes
    downtime_date = Column(DateTime, default=datetime.utcnow)
    description = Column(String(200))
    step_id = Column(Integer, ForeignKey('process_steps.id'))
    
    # Relations
    step = relationship("ProcessStep", back_populates="downtimes")

class StockMovement(Base):
    """Modèle pour les mouvements de stock."""
    __tablename__ = 'stock_movements'
    
    id = Column(Integer, primary_key=True)
    quantity = Column(Float, nullable=False)
    movement_type = Column(SQLEnum(StockMovementType), nullable=False)
    movement_date = Column(DateTime, default=datetime.utcnow)
    step_id = Column(Integer, ForeignKey('process_steps.id'))
    reference_id = Column(Integer, nullable=True)  # ID de la production/réception associée
    quality = Column(Float, nullable=True)  # Qualité du phosphate (P2O5 %)
    
    # Add source and destination steps for stock movements
    source_step_id = Column(Integer, ForeignKey('process_steps.id'), nullable=True)
    destination_step_id = Column(Integer, ForeignKey('process_steps.id'), nullable=True)

    # Relations
    step = relationship("ProcessStep", back_populates="stock_movements", foreign_keys=[step_id])
    source_step = relationship("ProcessStep", foreign_keys=[source_step_id])
    destination_step = relationship("ProcessStep", foreign_keys=[destination_step_id])

class ResourceConsumption(Base):
    """Modèle pour les consommations de ressources."""
    __tablename__ = 'resource_consumptions'
    
    id = Column(Integer, primary_key=True)
    resource_type = Column(SQLEnum(ResourceType), nullable=False)
    quantity = Column(Float, nullable=False)
    consumption_date = Column(DateTime, default=datetime.utcnow)
    step_id = Column(Integer, ForeignKey('process_steps.id'))
    
    # Relations
    step = relationship("ProcessStep")

class UnitHourCounter(Base):
    """Modèle pour les compteurs horaires des unités de production."""
    __tablename__ = 'unit_hour_counters'
    
    id = Column(Integer, primary_key=True)
    reading_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    hours = Column(Float, nullable=False)  # Nombre d'heures de production
    step_id = Column(Integer, ForeignKey('process_steps.id'))
    
    # Relations
    step = relationship("ProcessStep")