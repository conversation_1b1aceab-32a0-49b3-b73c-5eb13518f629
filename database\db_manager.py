"""
Gestionnaire de base de données factice pour la démonstration.
"""

from datetime import datetime, timedelta
import random
from enum import Enum

class DatabaseManager:
    """Gestionnaire de base de données factice pour la démonstration."""
    
    def __init__(self):
        """Initialise le gestionnaire de base de données factice."""
        self.data = {
            'reception': {
                'stock': 1250.5,
                'movements': []
            },
            'concassage': {
                'stock': 850.2,
                'movements': []
            }
        }
        
        # Générer des données factices
        self.generate_fake_data()
    
    def generate_fake_data(self):
        """Génère des données factices pour la démonstration."""
        # Dates de référence
        now = datetime.now()
        
        # Générer des mouvements pour la réception
        for i in range(50):
            days_ago = random.randint(0, 30)
            timestamp = now - timedelta(days=days_ago, 
                                      hours=random.randint(0, 23), 
                                      minutes=random.randint(0, 59))
            
            self.data['reception']['movements'].append({
                'id': i + 1,
                'timestamp': timestamp,
                'quantity': random.uniform(50, 200),
                'quality': random.uniform(25, 35),
                'movement_type': 'RECEPTION',
                'supplier': random.choice(['SOTRAMINE', 'Fournisseur A', 'Fournisseur B']),
                'truck_number': f'CAM-{random.randint(100, 999)}',
                'step': 'RECEPTION'
            })
        
        # Générer des mouvements pour le concassage
        for i in range(50):
            days_ago = random.randint(0, 30)
            timestamp = now - timedelta(days=days_ago, 
                                      hours=random.randint(0, 23), 
                                      minutes=random.randint(0, 59))
            
            movement_type = random.choice(['RECEPTION', 'TRANSFER'])
            
            self.data['concassage']['movements'].append({
                'id': i + 1,
                'timestamp': timestamp,
                'quantity': random.uniform(30, 150),
                'quality': random.uniform(25, 35),
                'movement_type': movement_type,
                'step': 'CONCASSAGE'
            })
    
    def get_current_stock(self, step):
        """Retourne le stock actuel pour l'étape spécifiée."""
        step_name = step.name.lower() if isinstance(step, Enum) else step.lower()
        return self.data.get(step_name, {}).get('stock', 0)
    
    def get_stock_movements(self, step=None, movement_type=None, start_date=None, limit=None):
        """Retourne les mouvements de stock filtrés."""
        step_name = step.name.lower() if isinstance(step, Enum) else step.lower()
        movements = self.data.get(step_name, {}).get('movements', [])
        
        # Filtrer par type de mouvement
        if movement_type:
            movement_type_name = movement_type.name if isinstance(movement_type, Enum) else movement_type
            movements = [m for m in movements if m.get('movement_type') == movement_type_name]
        
        # Filtrer par date de début
        if start_date:
            movements = [m for m in movements if m.get('timestamp') >= start_date]
        
        # Trier par date (plus récent en premier)
        movements = sorted(movements, key=lambda m: m.get('timestamp', datetime.now()), reverse=True)
        
        # Limiter le nombre de résultats
        if limit:
            movements = movements[:limit]
        
        return movements
    
    def add_stock_movement(self, **kwargs):
        """Ajoute un mouvement de stock."""
        step = kwargs.get('step')
        step_name = step.name.lower() if isinstance(step, Enum) else step.lower()
        
        movement_type = kwargs.get('movement_type')
        movement_type_name = movement_type.name if isinstance(movement_type, Enum) else movement_type
        
        # Créer le mouvement
        movement = {
            'id': len(self.data.get(step_name, {}).get('movements', [])) + 1,
            'timestamp': kwargs.get('timestamp', datetime.now()),
            'quantity': kwargs.get('quantity', 0),
            'quality': kwargs.get('quality', 0),
            'movement_type': movement_type_name,
            'step': step_name.upper()
        }
        
        # Ajouter des champs spécifiques pour la réception
        if step_name == 'reception':
            movement['supplier'] = kwargs.get('supplier', 'SOTRAMINE')
            movement['truck_number'] = kwargs.get('truck_number', '')
        
        # Ajouter le mouvement
        self.data.get(step_name, {}).get('movements', []).append(movement)
        
        # Mettre à jour le stock
        if movement_type_name == 'RECEPTION':
            self.data[step_name]['stock'] += kwargs.get('quantity', 0)
        elif movement_type_name == 'TRANSFER':
            self.data[step_name]['stock'] -= kwargs.get('quantity', 0)
    
    def close(self):
        """Ferme la connexion à la base de données."""
        # Rien à faire pour cette implémentation factice
        pass