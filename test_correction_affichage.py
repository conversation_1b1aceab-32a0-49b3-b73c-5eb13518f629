#!/usr/bin/env python3
"""
Test pour vérifier que la correction de l'affichage élimine la confusion.
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_correction_affichage():
    """Teste que l'affichage corrigé élimine la confusion."""
    print("🔍 Test de la correction de l'affichage laverie")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Nettoyer et créer des données de test
        session = db_manager.Session()
        try:
            session.execute("DELETE FROM stock_movements")
            session.execute("DELETE FROM productions")
            session.commit()
            print("✅ Base de données nettoyée")
        except Exception as e:
            session.rollback()
            print(f"❌ Erreur nettoyage: {str(e)}")
        finally:
            session.close()
        
        # Scénario de test
        print("\n📊 Scénario de test:")
        
        # 1. Réception
        print("1️⃣ Réception: 1000T")
        db_manager.add_reception(quantity=1000.0, quality=28.0, reception_date=datetime.now())
        
        # 2. Production concassage
        print("2️⃣ Production concassage: 800T produits, 900T consommés")
        db_manager.add_production(
            step=ProcessStepEnum.CONCASSAGE,
            quantity=800.0,
            quality=28.0,
            quantity_used=900.0,
            production_hours=8.0
        )
        
        # 3. Production laverie (première fois)
        print("3️⃣ Production laverie #1: 300T produits, 350T consommés")
        db_manager.add_production(
            step=ProcessStepEnum.LAVERIE,
            quantity=300.0,
            quality=32.0,
            quantity_used=350.0,
            production_hours=5.0
        )
        
        # 4. Production laverie (deuxième fois)
        print("4️⃣ Production laverie #2: 200T produits, 250T consommés")
        db_manager.add_production(
            step=ProcessStepEnum.LAVERIE,
            quantity=200.0,
            quality=32.0,
            quantity_used=250.0,
            production_hours=3.0
        )
        
        print("\n📊 Analyse des valeurs affichées:")
        
        # Valeurs que verrait l'onglet concassage
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        print(f"Stock concassage actuel: {concassage_stock:.1f}T")
        
        # Valeurs que verrait l'onglet laverie
        available_stock = db_manager.get_available_crushing_stock()
        print(f"Stock disponible pour laverie: {available_stock:.1f}T")
        
        laverie_stock = db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        print(f"Stock laverie: {laverie_stock:.1f}T")
        
        stockage_stock = db_manager.get_current_stock(ProcessStepEnum.STOCKAGE_FINAL)
        print(f"Stock stockage final: {stockage_stock:.1f}T")
        
        print("\n🔍 Vérification de la cohérence:")
        
        # Calculer les totaux
        total_concassage_production = 800.0
        total_laverie_consumption = 350.0 + 250.0  # 600T
        total_laverie_production = 300.0 + 200.0   # 500T
        
        print(f"Production totale concassage: {total_concassage_production:.1f}T")
        print(f"Consommation totale laverie: {total_laverie_consumption:.1f}T")
        print(f"Production totale laverie: {total_laverie_production:.1f}T")
        
        # Vérifications
        expected_concassage_remaining = total_concassage_production - total_laverie_consumption
        expected_stockage_final = total_laverie_production
        
        print(f"\n✅ Valeurs attendues:")
        print(f"Stock concassage restant: {expected_concassage_remaining:.1f}T")
        print(f"Stock disponible pour laverie: {expected_concassage_remaining:.1f}T")
        print(f"Stock stockage final: {expected_stockage_final:.1f}T")
        
        print(f"\n📊 Valeurs réelles:")
        print(f"Stock concassage: {concassage_stock:.1f}T")
        print(f"Stock disponible pour laverie: {available_stock:.1f}T")
        print(f"Stock stockage final: {stockage_stock:.1f}T")
        
        # Vérifier la cohérence
        problems = []
        
        if abs(concassage_stock - expected_concassage_remaining) > 0.1:
            problems.append(f"Stock concassage incorrect: {concassage_stock:.1f}T au lieu de {expected_concassage_remaining:.1f}T")
        
        if abs(available_stock - expected_concassage_remaining) > 0.1:
            problems.append(f"Stock disponible incorrect: {available_stock:.1f}T au lieu de {expected_concassage_remaining:.1f}T")
        
        if abs(stockage_stock - expected_stockage_final) > 0.1:
            problems.append(f"Stock stockage incorrect: {stockage_stock:.1f}T au lieu de {expected_stockage_final:.1f}T")
        
        if laverie_stock != 0.0:
            problems.append(f"Stock laverie non nul: {laverie_stock:.1f}T au lieu de 0.0T")
        
        if problems:
            print(f"\n❌ PROBLÈMES DÉTECTÉS:")
            for problem in problems:
                print(f"   {problem}")
            return False
        else:
            print(f"\n✅ COHÉRENCE VÉRIFIÉE:")
            print(f"   ✅ Stock concassage = Stock disponible laverie = {concassage_stock:.1f}T")
            print(f"   ✅ Production laverie = Stock stockage final = {stockage_stock:.1f}T")
            print(f"   ✅ Stock laverie = 0T (pas de stockage intermédiaire)")
            print(f"\n🎯 CONCLUSION:")
            print(f"   Il n'y a PAS de double comptabilisation.")
            print(f"   Le 'Stock Disponible (Concassage)' affiché dans l'onglet laverie")
            print(f"   représente correctement ce qui reste disponible pour traitement.")
            return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale."""
    print("🚀 Test de correction de l'affichage laverie")
    print("=" * 70)
    
    success = test_correction_affichage()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ Affichage corrigé - Pas de double comptabilisation")
        return 0
    else:
        print("❌ Problème persistant dans l'affichage")
        return 1

if __name__ == "__main__":
    sys.exit(main())
