"""
Onglet concassage minimaliste avec affichage simplifié et moins encombré.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QFileDialog, QProgressBar, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import ActionButton
from ui.widgets.minimal_kpi_widgets import (MinimalKPITile, MinimalKPIGrid, 
                                          MinimalProgressBar, MinimalDashboard)
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class MinimalCrushingTab(StandardizedBaseTab):
    """Onglet concassage minimaliste avec affichage simplifié et moins encombré."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🔨 Concassage", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface minimaliste."""
        # Remplacer le layout KPI standard par notre dashboard minimaliste
        self.kpi_group.setTitle("📊 Performance")
        
        # Supprimer le layout existant et créer un nouveau
        self.kpi_layout.removeItem(self.kpi_cards_layout)
        
        # Créer un tableau de bord minimaliste
        self.dashboard = MinimalDashboard()
        
        # Onglet Volumes (limité aux indicateurs essentiels)
        volumes_tab, volumes_layout = self.dashboard.add_tab("Volumes", "📊")
        
        # Grille KPI pour les volumes
        volumes_grid = self.dashboard.add_grid_to_tab(volumes_tab, volumes_layout)
        
        # Ajouter seulement les KPI essentiels
        self.stock_tile = volumes_grid.add_kpi_tile(
            "Stock Actuel", "0", "T", SotramineTheme.PRIMARY)
        self.today_in_tile = volumes_grid.add_kpi_tile(
            "Entrée Aujourd'hui", "0", "T", SotramineTheme.SECONDARY)
        self.today_out_tile = volumes_grid.add_kpi_tile(
            "Sortie Aujourd'hui", "0", "T", SotramineTheme.ACCENT)
        self.month_in_tile = volumes_grid.add_kpi_tile(
            "Entrée Mensuelle", "0", "T", SotramineTheme.SECONDARY)
        self.month_out_tile = volumes_grid.add_kpi_tile(
            "Sortie Mensuelle", "0", "T", SotramineTheme.ACCENT)
        
        # Onglet Rendement (simplifié)
        yield_tab, yield_layout = self.dashboard.add_tab("Rendement", "📈")
        
        # Barres de progression pour le rendement
        yield_bars = self.dashboard.add_progress_bars_to_tab(yield_tab, yield_layout)
        
        # Indicateur de rendement journalier
        self.daily_yield_bar = MinimalProgressBar(
            "Rendement Jour", 0, 0, 100, "%", SotramineTheme.PRIMARY)
        yield_bars.addWidget(self.daily_yield_bar)
        
        # Indicateur de rendement mensuel
        self.monthly_yield_bar = MinimalProgressBar(
            "Rendement Mois", 0, 0, 100, "%", SotramineTheme.SECONDARY)
        yield_bars.addWidget(self.monthly_yield_bar)
        
        # Grille KPI pour les statistiques de rendement
        yield_grid = self.dashboard.add_grid_to_tab(yield_tab, yield_layout)
        
        # Ajouter seulement les KPI essentiels de rendement
        self.daily_avg_in_tile = yield_grid.add_kpi_tile(
            "Moy. Entrée/Jour", "0", "T/j", SotramineTheme.SECONDARY)
        self.daily_avg_out_tile = yield_grid.add_kpi_tile(
            "Moy. Sortie/Jour", "0", "T/j", SotramineTheme.ACCENT)
        
        # Onglet Qualité (très simplifié)
        quality_tab, quality_layout = self.dashboard.add_tab("Qualité", "🔍")
        
        # Grille KPI pour la qualité
        quality_grid = self.dashboard.add_grid_to_tab(quality_tab, quality_layout)
        
        # Ajouter seulement les KPI essentiels de qualité
        self.avg_quality_in_tile = quality_grid.add_kpi_tile(
            "Qualité Entrée", "0", "%", SotramineTheme.SECONDARY)
        self.avg_quality_out_tile = quality_grid.add_kpi_tile(
            "Qualité Sortie", "0", "%", SotramineTheme.ACCENT)
        
        # Ajouter le dashboard au layout
        self.kpi_layout.addWidget(self.dashboard)
        
        # Section Saisie Manuelle (zone gauche) - Simplifiée
        form_layout = self.add_form_section("📝 Saisie")
        
        # Réduire le nombre de champs pour simplifier l'interface
        fields = {
            'movement_type': {
                'label': 'Type:',
                'type': 'combo',
                'options': ['Entrée', 'Sortie'],
                'category': 'Mouvement'
            },
            'quantity': {
                'label': 'Quantité (T):',
                'type': 'number',
                'placeholder': 'Ex: 125.50',
                'category': 'Mouvement'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2',
                'category': 'Qualité'
            },
            'timestamp': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_movement)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche) - Simplifiée
        actions_layout = self.add_actions_section("🔧 Actions")
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau des mouvements (zone droite) - Colonnes réduites
        headers = ["Date/Heure", "Type", "Quantité (T)", "Qualité P2O5 (%)", "Actions"]
        self.data_table = self.add_data_table("📋 Historique", headers, sortable=True, paginated=True)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les indicateurs de performance."""
        try:
            # Dates de référence
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_of_month = today.replace(day=1)
            
            # Stock actuel
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.stock_tile.update_value(f"{current_stock:.1f}")
            
            # Entrées aujourd'hui
            today_in = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.RECEPTION,
                start_date=today
            )
            today_in_total = sum(m['quantity'] for m in today_in)
            self.today_in_tile.update_value(f"{today_in_total:.1f}")
            
            # Sorties aujourd'hui
            today_out = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.TRANSFER,
                start_date=today
            )
            today_out_total = sum(m['quantity'] for m in today_out)
            self.today_out_tile.update_value(f"{today_out_total:.1f}")
            
            # Entrées ce mois
            month_in = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_of_month
            )
            month_in_total = sum(m['quantity'] for m in month_in)
            self.month_in_tile.update_value(f"{month_in_total:.1f}")
            
            # Sorties ce mois
            month_out = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.TRANSFER,
                start_date=start_of_month
            )
            month_out_total = sum(m['quantity'] for m in month_out)
            self.month_out_tile.update_value(f"{month_out_total:.1f}")
            
            # Rendement journalier
            daily_yield = (today_out_total / today_in_total * 100) if today_in_total > 0 else 0
            self.daily_yield_bar.update_value(daily_yield)
            
            # Rendement mensuel
            monthly_yield = (month_out_total / month_in_total * 100) if month_in_total > 0 else 0
            self.monthly_yield_bar.update_value(monthly_yield)
            
            # Moyennes journalières
            days_in_month = (datetime.now() - start_of_month).days + 1
            daily_avg_in = month_in_total / days_in_month if days_in_month > 0 else 0
            daily_avg_out = month_out_total / days_in_month if days_in_month > 0 else 0
            
            self.daily_avg_in_tile.update_value(f"{daily_avg_in:.1f}")
            self.daily_avg_out_tile.update_value(f"{daily_avg_out:.1f}")
            
            # Qualité moyenne entrée/sortie
            qualities_in = [m['quality'] for m in month_in if m['quality']]
            qualities_out = [m['quality'] for m in month_out if m['quality']]
            
            avg_quality_in = sum(qualities_in) / len(qualities_in) if qualities_in else 0
            avg_quality_out = sum(qualities_out) / len(qualities_out) if qualities_out else 0
            
            self.avg_quality_in_tile.update_value(f"{avg_quality_in:.1f}")
            self.avg_quality_out_tile.update_value(f"{avg_quality_out:.1f}")
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des KPI: {str(e)}")
    
    def refresh_data(self):
        """Rafraîchit les données du tableau."""
        try:
            # Récupérer les mouvements de stock
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                limit=100  # Limiter à 100 entrées pour améliorer les performances
            )
            
            # Effacer le tableau
            self.data_table.setRowCount(0)
            
            # Remplir avec les nouvelles données
            for movement in movements:
                row_position = self.data_table.rowCount()
                self.data_table.insertRow(row_position)
                
                # Date/Heure
                date_str = movement.get('timestamp', datetime.now()).strftime('%d/%m/%Y %H:%M')
                self.data_table.set_item_text(row_position, 0, date_str)
                
                # Type de mouvement
                movement_type = movement.get('movement_type', StockMovementType.RECEPTION)
                type_str = "Entrée" if movement_type == StockMovementType.RECEPTION else "Sortie"
                self.data_table.set_item_text(row_position, 1, type_str)
                
                # Quantité
                quantity = movement.get('quantity', 0)
                self.data_table.set_item_text(row_position, 2, f"{quantity:.1f}")
                
                # Qualité
                quality = movement.get('quality', 0)
                self.data_table.set_item_text(row_position, 3, f"{quality:.1f}")
                
                # Actions
                self.data_table.add_action_buttons(row_position, 4, 
                                                 ["edit", "delete"], 
                                                 [self.edit_movement, self.delete_movement])
            
            # Mettre à jour les KPI
            self.update_kpi()
            
        except Exception as e:
            logging.error(f"Erreur lors du rafraîchissement des données: {str(e)}")
    
    def save_movement(self, data):
        """Enregistre un nouveau mouvement de stock."""
        try:
            # Déterminer le type de mouvement
            movement_type = StockMovementType.RECEPTION if data.get('movement_type') == 'Entrée' else StockMovementType.TRANSFER
            
            # Préparer les données
            movement_data = {
                'step': ProcessStepEnum.CONCASSAGE,
                'movement_type': movement_type,
                'quantity': float(data.get('quantity', 0)),
                'quality': float(data.get('quality', 0)),
                'timestamp': data.get('timestamp', datetime.now())
            }
            
            # Enregistrer dans la base de données
            self.db_manager.add_stock_movement(**movement_data)
            
            # Rafraîchir les données
            self.refresh_data()
            
            # Message de confirmation
            QMessageBox.information(self, "Succès", "Mouvement enregistré avec succès.")
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement du mouvement: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'enregistrer le mouvement: {str(e)}")
    
    def edit_movement(self, row):
        """Édite un mouvement existant."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", f"Édition du mouvement à la ligne {row+1}")
    
    def delete_movement(self, row):
        """Supprime un mouvement existant."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", f"Suppression du mouvement à la ligne {row+1}")
    
    def export_detailed_report(self):
        """Exporte un rapport détaillé des mouvements."""
        # Fonctionnalité à implémenter
        QMessageBox.information(self, "Information", "Export de rapport non implémenté")