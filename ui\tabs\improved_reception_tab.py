"""
Onglet réception amélioré avec layout optimisé.
Utilise un splitter horizontal pour désencombrer l'interface.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QGroupBox,
                            QMessageBox, QDateTimeEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QFileDialog, QTabWidget, QGridLayout)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QPalette, QColor, QIcon
from ui.layouts.improved_layout_base import ImprovedLayoutBase, CollapsiblePanel, KPICard
from models.enums import ProcessStepEnum, StockMovementType
from models.database_models import ProcessStep, StockMovement
from sqlalchemy import func
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
import pandas as pd


class ImprovedReceptionTab(ImprovedLayoutBase):
    """Onglet de gestion de la réception avec layout amélioré."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        
        # Widgets de formulaire
        self.reception_date = QDateTimeEdit()
        self.reception_date.setDateTime(QDateTime.currentDateTime())
        self.quantity_received_input = QLineEdit()
        self.quality_input = QLineEdit()
        
        # KPI Cards
        self.stock_card = None
        self.today_card = None
        self.month_card = None
        self.avg_quality_card = None
        
        # Table
        self.reception_table = QTableWidget()
        
        self.init_improved_ui()
        self.setup_timers()
    
    def init_improved_ui(self):
        """Initialise l'interface améliorée."""
        
        # === PANNEAU GAUCHE (Contrôles) ===
        
        # 1. Panel KPI
        kpi_panel, kpi_grid = self.create_kpi_panel("📊 Indicateurs de Performance")
        
        # Créer les cartes KPI
        self.stock_card = KPICard("Stock Concassage", "0.0", "tonnes")
        self.today_card = KPICard("Reçu Aujourd'hui", "0.0", "tonnes")
        self.month_card = KPICard("Reçu ce Mois", "0.0", "tonnes")
        self.avg_quality_card = KPICard("Qualité Moyenne", "0.0", "% P2O5")
        
        # Disposition en grille 2x2
        kpi_grid.addWidget(self.stock_card, 0, 0)
        kpi_grid.addWidget(self.today_card, 0, 1)
        kpi_grid.addWidget(self.month_card, 1, 0)
        kpi_grid.addWidget(self.avg_quality_card, 1, 1)
        
        self.add_to_left_panel(kpi_panel)
        
        # 2. Panel Saisie Rapide
        form_panel, form_layout = self.create_form_panel("📝 Saisie Rapide")
        
        form_layout.addRow("Date de réception:", self.reception_date)
        
        self.quantity_received_input.setPlaceholderText("Ex: 125.5")
        form_layout.addRow("Quantité (tonnes):", self.quantity_received_input)
        
        self.quality_input.setPlaceholderText("Ex: 29.8")
        form_layout.addRow("Qualité P2O5 (%):", self.quality_input)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("✅ Ajouter")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        add_button.clicked.connect(self.add_reception)
        
        clear_button = QPushButton("🗑️ Vider")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(clear_button)
        
        form_layout.addRow("", buttons_layout)
        
        self.add_to_left_panel(form_panel)
        
        # 3. Panel Outils
        tools_panel, tools_layout = self.create_form_panel("🛠️ Outils")
        
        import_button = QPushButton("📊 Importer Excel")
        import_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        import_button.clicked.connect(self.import_from_excel)
        
        export_button = QPushButton("📤 Exporter Excel")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #047857;
            }
        """)
        export_button.clicked.connect(self.export_to_excel)
        
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #7C3AED;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6D28D9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        
        tools_layout.addRow(import_button)
        tools_layout.addRow(export_button)
        tools_layout.addRow(refresh_button)
        
        self.add_to_left_panel(tools_panel)
        self.add_stretch_to_left()
        
        # === PANNEAU DROIT (Données) ===
        
        # Panel Table des réceptions
        table_panel = CollapsiblePanel("📋 Historique des Réceptions")
        
        # Configuration de la table
        self.reception_table.setColumnCount(5)
        self.reception_table.setHorizontalHeaderLabels([
            "Date", "Quantité (t)", "Qualité (%)", "Opérateur", "Actions"
        ])
        
        # Style de la table
        self.reception_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #E5E7EB;
                background-color: white;
                alternate-background-color: #F9FAFB;
                selection-background-color: #DBEAFE;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        self.reception_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.reception_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.reception_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.reception_table.setAlternatingRowColors(True)
        
        table_panel.add_widget(self.reception_table)
        self.add_to_right_panel(table_panel)
        
        # Chargement initial des données
        self.refresh_data()
    
    def setup_timers(self):
        """Configure les timers pour la mise à jour automatique."""
        # Timer pour mettre à jour les KPI toutes les 30 secondes
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # 30 secondes
        
        # Première mise à jour immédiate
        self.update_kpi()
    
    def add_reception(self):
        """Ajoute une nouvelle réception."""
        try:
            # Validation des données
            if not self.quantity_received_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une quantité.")
                return
            
            if not self.quality_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une qualité.")
                return
            
            quantity = float(self.quantity_received_input.text())
            quality = float(self.quality_input.text())
            
            if quantity <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive.")
                return
            
            if quality <= 0 or quality > 100:
                QMessageBox.warning(self, "Erreur", "La qualité doit être entre 0 et 100%.")
                return
            
            # Ajouter à la base de données
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.RECEPTION,
                quantity=quantity,
                movement_type=StockMovementType.RECEPTION,
                movement_date=self.reception_date.dateTime().toPyDateTime(),
                quality=quality
            )
            
            # Message de succès
            QMessageBox.information(
                self, "✅ Succès", 
                f"Réception ajoutée: {quantity} tonnes à {quality}% P2O5"
            )
            
            # Vider le formulaire et actualiser
            self.clear_form()
            self.refresh_data()
            self.update_kpi()
            
        except ValueError:
            QMessageBox.critical(self, "Erreur", "Veuillez saisir des valeurs numériques valides.")
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout de réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def clear_form(self):
        """Vide le formulaire de saisie."""
        self.quantity_received_input.clear()
        self.quality_input.clear()
        self.reception_date.setDateTime(QDateTime.currentDateTime())
    
    def update_kpi(self):
        """Met à jour les indicateurs KPI."""
        try:
            # Stock concassage actuel
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_card.update_value(f"{current_stock:.1f}")
            
            # Utiliser les méthodes existantes du db_manager
            # Réceptions aujourd'hui - utiliser une approximation simple
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            self.today_card.update_value("0.0")  # Placeholder
            
            # Réceptions ce mois
            self.month_card.update_value("0.0")  # Placeholder
            
            # Qualité moyenne
            self.avg_quality_card.update_value("--")  # Placeholder
                
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI réception: {str(e)}")
    
    def refresh_data(self):
        """Actualise les données de la table."""
        try:
            # Utiliser les méthodes existantes pour récupérer les données
            # Pour l'instant, afficher un tableau vide avec un message
            self.reception_table.setRowCount(1)
            
            # Message d'information
            self.reception_table.setItem(0, 0, QTableWidgetItem("Aucune donnée"))
            self.reception_table.setItem(0, 1, QTableWidgetItem("--"))
            self.reception_table.setItem(0, 2, QTableWidgetItem("--"))
            self.reception_table.setItem(0, 3, QTableWidgetItem("--"))
            self.reception_table.setItem(0, 4, QTableWidgetItem("--"))
                
        except Exception as e:
            logging.error(f"Erreur actualisation données réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'actualisation: {str(e)}")
    
    def import_from_excel(self):
        """Importe les données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Importer depuis Excel", 
                "", "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
            
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['date', 'quantite', 'qualite']
            if not all(col in df.columns.str.lower() for col in required_columns):
                QMessageBox.warning(
                    self, "Erreur", 
                    "Le fichier doit contenir les colonnes: Date, Quantite, Qualite"
                )
                return
            
            imported_count = 0
            
            for _, row in df.iterrows():
                try:
                    # Extraire les données
                    date = pd.to_datetime(row['date'] if 'date' in row else row['Date'])
                    quantity = float(row['quantite'] if 'quantite' in row else row['Quantite'])
                    quality = float(row['qualite'] if 'qualite' in row else row['Qualite'])
                    
                    # Ajouter à la base de données
                    self.db_manager.add_stock_movement(
                        step=ProcessStepEnum.RECEPTION,
                        quantity=quantity,
                        movement_type=StockMovementType.RECEPTION,
                        movement_date=date.to_pydatetime(),
                        quality=quality
                    )
                    
                    imported_count += 1
                    
                except Exception as e:
                    logging.warning(f"Erreur import ligne: {str(e)}")
                    continue
            
            QMessageBox.information(
                self, "✅ Import Terminé", 
                f"{imported_count} réceptions importées avec succès."
            )
            
            self.refresh_data()
            self.update_kpi()
            
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
    
    def export_to_excel(self):
        """Exporte les données vers un fichier Excel."""
        QMessageBox.information(
            self, "Export Excel", 
            "Fonctionnalité d'export à implémenter avec les méthodes existantes."
        )
