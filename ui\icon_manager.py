"""
Gestionnaire d'icônes pour l'application SOTRAMINE PHOSPHATE.
Crée des icônes cohérentes si elles n'existent pas.
"""

import os
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor, QFont
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtWidgets import QLabel
from ui.professional_theme import SotramineTheme

class IconManager:
    """Gestionnaire d'icônes pour l'application."""
    
    def __init__(self):
        self.icons_dir = os.path.join("resources", "icons")
        self.ensure_icons_directory()
        
    def ensure_icons_directory(self):
        """Assure que le répertoire des icônes existe."""
        if not os.path.exists(self.icons_dir):
            os.makedirs(self.icons_dir, exist_ok=True)
            
    def create_text_icon(self, text, size=64, bg_color=None, text_color=None):
        """Crée une icône avec du texte."""
        if bg_color is None:
            bg_color = QColor(SotramineTheme.PRIMARY)
        if text_color is None:
            text_color = QColor(SotramineTheme.TEXT_WHITE)
            
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dessiner le fond circulaire
        painter.setBrush(bg_color)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, size, size)
        
        # Dessiner le texte
        painter.setPen(text_color)
        font = QFont("Arial", int(size * 0.4), QFont.Bold)
        painter.setFont(font)
        painter.drawText(0, 0, size, size, Qt.AlignCenter, text)
        
        painter.end()
        return QIcon(pixmap)
    
    def create_symbol_icon(self, symbol, size=64, bg_color=None, text_color=None):
        """Crée une icône avec un symbole Unicode."""
        if bg_color is None:
            bg_color = QColor(SotramineTheme.PRIMARY)
        if text_color is None:
            text_color = QColor(SotramineTheme.TEXT_WHITE)
            
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dessiner le fond avec coins arrondis
        painter.setBrush(bg_color)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(2, 2, size-4, size-4, 8, 8)
        
        # Dessiner le symbole
        painter.setPen(text_color)
        font = QFont("Segoe UI Symbol", int(size * 0.5), QFont.Bold)
        painter.setFont(font)
        painter.drawText(0, 0, size, size, Qt.AlignCenter, symbol)
        
        painter.end()
        return QIcon(pixmap)
    
    def get_tab_icon(self, tab_name):
        """Retourne l'icône appropriée pour un onglet."""
        icons = {
            "Réception": self.create_symbol_icon("📥", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Concassage": self.create_symbol_icon("🔨", bg_color=QColor(SotramineTheme.ACCENT)),
            "Laverie": self.create_symbol_icon("💧", bg_color=QColor("#00ACC1")),
            "Stocks": self.create_symbol_icon("📦", bg_color=QColor(SotramineTheme.PRIMARY)),
            "Compteurs Horaires": self.create_symbol_icon("⏱️", bg_color=QColor("#9C27B0")),
            "Consommations": self.create_symbol_icon("⚡", bg_color=QColor("#FF5722")),
            "Rapports": self.create_symbol_icon("📊", bg_color=QColor("#607D8B")),
            "Paramètres": self.create_symbol_icon("⚙️", bg_color=QColor(SotramineTheme.GRAY_600))
        }
        
        return icons.get(tab_name, self.create_text_icon(tab_name[0]))
    
    def get_action_icon(self, action_name):
        """Retourne l'icône appropriée pour une action."""
        icons = {
            "Ajouter": self.create_symbol_icon("➕", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Modifier": self.create_symbol_icon("✏️", bg_color=QColor(SotramineTheme.ACCENT)),
            "Supprimer": self.create_symbol_icon("🗑️", bg_color=QColor(SotramineTheme.DANGER)),
            "Actualiser": self.create_symbol_icon("🔄", bg_color=QColor(SotramineTheme.PRIMARY)),
            "Rechercher": self.create_symbol_icon("🔍", bg_color=QColor(SotramineTheme.PRIMARY)),
            "Exporter": self.create_symbol_icon("📤", bg_color=QColor(SotramineTheme.PRIMARY)),
            "Importer": self.create_symbol_icon("📥", bg_color=QColor(SotramineTheme.SECONDARY)),
            "PDF": self.create_symbol_icon("📄", bg_color=QColor(SotramineTheme.DANGER)),
            "Excel": self.create_symbol_icon("📊", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Enregistrer": self.create_symbol_icon("💾", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Annuler": self.create_symbol_icon("❌", bg_color=QColor(SotramineTheme.GRAY_500)),
            "Valider": self.create_symbol_icon("✅", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Arrêt": self.create_symbol_icon("⏹️", bg_color=QColor(SotramineTheme.DANGER)),
            "Démarrage": self.create_symbol_icon("▶️", bg_color=QColor(SotramineTheme.SECONDARY)),
            "Pause": self.create_symbol_icon("⏸️", bg_color=QColor(SotramineTheme.ACCENT))
        }
        
        return icons.get(action_name, self.create_text_icon(action_name[0]))
    
    def get_status_icon(self, status):
        """Retourne l'icône de statut appropriée."""
        icons = {
            "En cours": self.create_symbol_icon("▶️", size=24, bg_color=QColor(SotramineTheme.SECONDARY)),
            "Arrêté": self.create_symbol_icon("⏹️", size=24, bg_color=QColor(SotramineTheme.DANGER)),
            "Maintenance": self.create_symbol_icon("🔧", size=24, bg_color=QColor(SotramineTheme.ACCENT)),
            "OK": self.create_symbol_icon("✅", size=24, bg_color=QColor(SotramineTheme.SECONDARY)),
            "Attention": self.create_symbol_icon("⚠️", size=24, bg_color=QColor(SotramineTheme.ACCENT)),
            "Erreur": self.create_symbol_icon("❌", size=24, bg_color=QColor(SotramineTheme.DANGER))
        }
        
        return icons.get(status, self.create_text_icon("?", size=24))

# Instance globale
icon_manager = IconManager()
