"""
Widgets de performance simplifiés sous forme de liste avec valeurs alignées.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QScrollArea, QSizePolicy, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPen
from ui.professional_theme import SotramineTheme

class SimplePerformanceList(QFrame):
    """Liste simple de performances avec valeurs alignées."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.items = []
        
        # Style du cadre
        self.setFrameShape(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {SotramineTheme.BG_CARD};
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 4px;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(2)
        
        # Titre (si fourni)
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: 10pt;
                font-weight: bold;
                color: {SotramineTheme.TEXT_PRIMARY};
                padding-bottom: 5px;
                border-bottom: 1px solid {SotramineTheme.GRAY_300};
            """)
            self.layout.addWidget(title_label)
        
        # Conteneur pour les éléments
        self.items_layout = QVBoxLayout()
        self.items_layout.setContentsMargins(0, 5, 0, 0)
        self.items_layout.setSpacing(1)  # Espacement minimal entre les lignes
        
        self.layout.addLayout(self.items_layout)
        
    def add_item(self, label, value, unit="", color=None, icon=""):
        """Ajoute un élément à la liste."""
        item = SimplePerformanceItem(label, value, unit, color, icon)
        self.items.append(item)
        self.items_layout.addWidget(item)
        return item
        
    def clear(self):
        """Supprime tous les éléments de la liste."""
        for item in self.items:
            self.items_layout.removeWidget(item)
            item.deleteLater()
        self.items = []


class SimplePerformanceItem(QFrame):
    """Élément simple de performance avec label et valeur alignés."""
    
    def __init__(self, label, value, unit="", color=None, icon="", parent=None):
        super().__init__(parent)
        self.label = label
        self.value = value
        self.unit = unit
        self.color = color or SotramineTheme.TEXT_PRIMARY
        self.icon = icon
        
        self.setFixedHeight(28)  # Hauteur fixe réduite
        self.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border: none;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de l'élément."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(5)
        
        # Icône (si fournie)
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 10pt;
                color: {self.color};
                margin-right: 2px;
            """)
            layout.addWidget(icon_label)
        
        # Label
        label_text = QLabel(self.label)
        label_text.setStyleSheet(f"""
            font-size: 9pt;
            color: {SotramineTheme.TEXT_MUTED};
        """)
        layout.addWidget(label_text)
        
        layout.addStretch()
        
        # Valeur
        self.value_label = QLabel(str(self.value))
        self.value_label.setStyleSheet(f"""
            font-size: 9pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 8pt;
                color: {SotramineTheme.TEXT_MUTED};
                margin-left: 2px;
            """)
            layout.addWidget(unit_label)
    
    def update_value(self, value):
        """Met à jour la valeur de l'élément."""
        self.value = value
        self.value_label.setText(str(value))


class SimplePerformanceGrid(QFrame):
    """Grille simple de performances avec plusieurs colonnes."""
    
    def __init__(self, title="", columns=2, parent=None):
        super().__init__(parent)
        self.title = title
        self.columns = columns
        self.items = []
        self.row_count = 0
        
        # Style du cadre
        self.setFrameShape(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {SotramineTheme.BG_CARD};
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 4px;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(2)
        
        # Titre (si fourni)
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: 10pt;
                font-weight: bold;
                color: {SotramineTheme.TEXT_PRIMARY};
                padding-bottom: 5px;
                border-bottom: 1px solid {SotramineTheme.GRAY_300};
            """)
            self.layout.addWidget(title_label)
        
        # Grille pour les éléments
        self.grid_layout = QGridLayout()
        self.grid_layout.setContentsMargins(0, 5, 0, 0)
        self.grid_layout.setHorizontalSpacing(15)  # Espacement horizontal entre les colonnes
        self.grid_layout.setVerticalSpacing(1)     # Espacement vertical minimal
        
        self.layout.addLayout(self.grid_layout)
        
    def add_item(self, label, value, unit="", color=None, icon=""):
        """Ajoute un élément à la grille."""
        item = SimplePerformanceItem(label, value, unit, color, icon)
        self.items.append(item)
        
        row, col = divmod(len(self.items) - 1, self.columns)
        self.grid_layout.addWidget(item, row, col)
        
        if row + 1 > self.row_count:
            self.row_count = row + 1
            
        return item
        
    def clear(self):
        """Supprime tous les éléments de la grille."""
        for item in self.items:
            self.grid_layout.removeWidget(item)
            item.deleteLater()
        self.items = []
        self.row_count = 0


class SimplePerformanceSection(QWidget):
    """Section de performances avec titre et plusieurs listes."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.lists = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(10)
        
        # Titre de la section (si fourni)
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: 11pt;
                font-weight: bold;
                color: {SotramineTheme.PRIMARY};
                padding: 2px 0;
            """)
            self.layout.addWidget(title_label)
    
    def add_list(self, title=""):
        """Ajoute une liste de performances à la section."""
        perf_list = SimplePerformanceList(title)
        self.lists.append(perf_list)
        self.layout.addWidget(perf_list)
        return perf_list
    
    def add_grid(self, title="", columns=2):
        """Ajoute une grille de performances à la section."""
        perf_grid = SimplePerformanceGrid(title, columns)
        self.lists.append(perf_grid)
        self.layout.addWidget(perf_grid)
        return perf_grid


class SimplePerformanceTab(QWidget):
    """Onglet de performances avec plusieurs sections organisées en colonnes."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.sections = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Scroll area pour permettre le défilement
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        
        # Widget de contenu pour le scroll area
        self.content = QWidget()
        self.content_layout = QHBoxLayout(self.content)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(10)
        
        # Colonnes pour les sections
        self.left_column = QVBoxLayout()
        self.left_column.setContentsMargins(0, 0, 0, 0)
        self.left_column.setSpacing(15)
        
        self.right_column = QVBoxLayout()
        self.right_column.setContentsMargins(0, 0, 0, 0)
        self.right_column.setSpacing(15)
        
        self.content_layout.addLayout(self.left_column, 1)
        self.content_layout.addLayout(self.right_column, 1)
        
        self.scroll_area.setWidget(self.content)
        self.layout.addWidget(self.scroll_area)
    
    def add_section_left(self, title=""):
        """Ajoute une section à la colonne gauche."""
        section = SimplePerformanceSection(title)
        self.sections.append(section)
        self.left_column.addWidget(section)
        return section
    
    def add_section_right(self, title=""):
        """Ajoute une section à la colonne droite."""
        section = SimplePerformanceSection(title)
        self.sections.append(section)
        self.right_column.addWidget(section)
        return section