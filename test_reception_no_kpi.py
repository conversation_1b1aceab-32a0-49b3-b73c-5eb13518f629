#!/usr/bin/env python3
"""
Test pour vérifier que l'onglet réception fonctionne sans les KPI.
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_reception_tab():
    """Teste l'onglet réception sans KPI."""
    print("🔍 Test de l'onglet réception sans KPI...")

    try:
        # Test des imports
        from database.database_manager import DatabaseManager
        from ui.tabs.optimized_reception_tab import OptimizedReceptionTab
        print("✅ Imports réussis")

        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ Base de données initialisée")

        # Créer une application Qt minimale pour le test
        from PyQt5.QtWidgets import QApplication
        import sys
        app = QApplication(sys.argv)

        # Créer l'onglet réception
        reception_tab = OptimizedReceptionTab(db_manager)
        print("✅ Onglet réception créé sans erreur")
        
        # Vérifier que les méthodes essentielles existent
        if hasattr(reception_tab, 'refresh_data'):
            print("✅ Méthode refresh_data présente")
        else:
            print("❌ Méthode refresh_data manquante")
            return False
            
        if hasattr(reception_tab, 'setup_interface'):
            print("✅ Méthode setup_interface présente")
        else:
            print("❌ Méthode setup_interface manquante")
            return False
        
        # Vérifier que les KPI ont été supprimés
        if hasattr(reception_tab, 'update_kpi'):
            print("❌ Méthode update_kpi encore présente")
            return False
        else:
            print("✅ Méthode update_kpi supprimée")
            
        if hasattr(reception_tab, 'total_reception_card'):
            print("❌ KPI total_reception_card encore présent")
            return False
        else:
            print("✅ KPI total_reception_card supprimé")
            
        if hasattr(reception_tab, 'current_stock_card'):
            print("❌ KPI current_stock_card encore présent")
            return False
        else:
            print("✅ KPI current_stock_card supprimé")
        
        # Test de la méthode refresh_data
        try:
            reception_tab.refresh_data()
            print("✅ Méthode refresh_data fonctionne")
        except Exception as e:
            print(f"❌ Erreur dans refresh_data: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test de l'onglet réception sans KPI")
    print("=" * 50)
    
    success = test_reception_tab()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test réussi ! L'onglet réception fonctionne sans KPI.")
        return 0
    else:
        print("❌ Test échoué. Des corrections sont nécessaires.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
