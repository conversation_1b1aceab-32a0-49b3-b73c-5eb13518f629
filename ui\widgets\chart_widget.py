"""
Widget pour afficher des graphiques dans l'application.
Version modifiée : Les graphiques ont été désactivés.
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton, QSizePolicy
from PyQt5.QtCore import Qt
from datetime import datetime, timedelta

# Couleurs pour les widgets (conservées pour compatibilité)
CHART_COLORS = [
    "#1976D2",  # Bleu
    "#388E3C",  # Vert
    "#F57C00",  # Orange
    "#9C27B0",  # Violet
    "#00ACC1",  # Cyan
    "#FF5722",  # Orange foncé
    "#607D8B",  # Bleu gris
    "#8BC34A",  # Vert clair
]

# Style pour les widgets (conservé pour compatibilité)
CHART_STYLE = {
    "figure.facecolor": "#FFFFFF",
    "axes.facecolor": "#FFFFFF",
    "axes.edgecolor": "#757575",
    "axes.labelcolor": "#212121",
    "axes.grid": True,
    "grid.color": "#EEEEEE",
    "text.color": "#212121",
    "xtick.color": "#757575",
    "ytick.color": "#757575",
    "lines.linewidth": 2.5,
    "font.size": 10,
    "axes.titlesize": 14,
    "axes.labelsize": 12,
}

class EmptyWidget(QWidget):
    """Widget vide qui remplace les graphiques."""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """Initialise le widget vide."""
        super().__init__(parent)
        self.setParent(parent)
        
        # Mise en page
        layout = QVBoxLayout(self)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        layout.addWidget(info_label)
        
        # Ajuster la taille
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.updateGeometry()

class BarChartWidget(QWidget):
    """Widget pour afficher un graphique à barres (désactivé)."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique à barres."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Cette année"])
        header_layout.addWidget(QLabel("Période:"))
        header_layout.addWidget(self.period_combo)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        header_layout.addWidget(self.export_button)
        
        self.layout.addLayout(header_layout)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        self.layout.addWidget(info_label)
        
        # Définir une taille minimale
        self.setMinimumHeight(100)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    
    def create_chart(self):
        """Méthode vide pour compatibilité."""
        pass
    
    def update_chart(self, data=None, categories=None):
        """Méthode vide pour compatibilité."""
        pass
    
    def export_chart(self):
        """Méthode vide pour compatibilité."""
        pass

class LineChartWidget(QWidget):
    """Widget pour afficher un graphique linéaire (désactivé)."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique linéaire."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItems(["7 jours", "30 jours", "90 jours", "1 an"])
        header_layout.addWidget(QLabel("Période:"))
        header_layout.addWidget(self.period_combo)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        header_layout.addWidget(self.export_button)
        
        self.layout.addLayout(header_layout)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        self.layout.addWidget(info_label)
        
        # Définir une taille minimale
        self.setMinimumHeight(100)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    
    def create_chart(self):
        """Méthode vide pour compatibilité."""
        pass
    
    def update_chart(self, data=None, dates=None):
        """Méthode vide pour compatibilité."""
        pass
    
    def export_chart(self):
        """Méthode vide pour compatibilité."""
        pass

class MatplotlibBarChart(QWidget):
    """Widget pour afficher un graphique à barres avec Matplotlib (désactivé)."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique à barres."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Cette année"])
        header_layout.addWidget(QLabel("Période:"))
        header_layout.addWidget(self.period_combo)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        header_layout.addWidget(self.export_button)
        
        self.layout.addLayout(header_layout)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        self.layout.addWidget(info_label)
        
        # Définir une taille minimale
        self.setMinimumHeight(100)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # Données initiales (pour compatibilité)
        self.categories = []
        self.data = []
    
    def update_chart(self, data=None, categories=None):
        """Méthode vide pour compatibilité."""
        if data is not None:
            self.data = data
        if categories is not None:
            self.categories = categories
    
    def export_chart(self):
        """Méthode vide pour compatibilité."""
        pass

class MatplotlibLineChart(QWidget):
    """Widget pour afficher un graphique linéaire avec Matplotlib (désactivé)."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique linéaire."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItems(["7 jours", "30 jours", "90 jours", "1 an"])
        header_layout.addWidget(QLabel("Période:"))
        header_layout.addWidget(self.period_combo)
        
        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        header_layout.addWidget(self.export_button)
        
        self.layout.addLayout(header_layout)
        
        # Message indiquant que les graphiques sont désactivés
        info_label = QLabel("Graphiques désactivés")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #757575; padding: 20px;")
        self.layout.addWidget(info_label)
        
        # Définir une taille minimale
        self.setMinimumHeight(100)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # Données initiales (pour compatibilité)
        self.dates = []
        self.data = []
    
    def update_chart(self, data=None, dates=None):
        """Méthode vide pour compatibilité."""
        if data is not None:
            self.data = data
        if dates is not None:
            self.dates = dates
    
    def export_chart(self):
        """Méthode vide pour compatibilité."""
        pass