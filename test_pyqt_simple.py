import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
import numpy as np
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtWidgets import QLabel
import io

class MainWindow(QMainWindow):
    def __init__(self):
        print("Initializing main window...")
        super().__init__()
        
        # Create the main widget
        print("Creating central widget...")
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        print("Creating layout...")
        layout = QVBoxLayout(central_widget)
        
        # Create a label to display the plot
        print("Creating label...")
        self.label = QLabel()
        layout.addWidget(self.label)
        
        # Create a button to generate the plot
        print("Creating button...")
        button = QPushButton("Generate Plot")
        button.clicked.connect(self.generate_plot)
        layout.addWidget(button)
        
        # Set window properties
        self.setWindowTitle("PyQt5 with Matplotlib (Agg)")
        self.setGeometry(100, 100, 800, 600)
        print("Window initialized")
    
    def generate_plot(self):
        print("Generating plot...")
        # Create a figure and plot
        fig = Figure(figsize=(5, 4), dpi=100)
        ax = fig.add_subplot(111)
        
        # Generate data
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        # Plot data
        ax.plot(x, y)
        ax.set_title('Sine Wave')
        ax.set_xlabel('x')
        ax.set_ylabel('sin(x)')
        
        # Convert plot to image
        buf = io.BytesIO()
        fig.savefig(buf, format='png')
        buf.seek(0)
        
        # Create QImage from buffer
        image = QImage.fromData(buf.getvalue())
        pixmap = QPixmap.fromImage(image)
        
        # Display the image
        self.label.setPixmap(pixmap)
        print("Plot displayed")

def main():
    try:
        print("Creating application...")
        app = QApplication(sys.argv)
        print("Application created")
        
        print("Creating main window...")
        window = MainWindow()
        print("Main window created")
        
        print("Showing window...")
        window.show()
        print("Window shown")
        
        print("Starting event loop...")
        return app.exec_()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("Starting PyQt5 with Matplotlib (Agg) test...")
    try:
        exit_code = main()
        print(f"Application exited with code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        print(f"Unhandled exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)