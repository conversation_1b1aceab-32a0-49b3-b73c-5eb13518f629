import sys
import os
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, 
                            QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGroupBox,
                            QSizePolicy)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON><PERSON><PERSON><PERSON>, QColor, QPen
from database.database_manager import DatabaseManager
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Couleurs pour les graphiques
CHART_COLORS = [
    "#1976D2",  # Bleu
    "#388E3C",  # Vert
    "#F57C00",  # Orange
    "#9C27B0",  # Violet
    "#00ACC1",  # Cyan
    "#FF5722",  # Orange foncé
    "#607D8B",  # Bleu gris
    "#8BC34A",  # Vert clair
]

class MatplotlibCanvas(FigureCanvas):
    """Canvas Matplotlib pour l'intégration avec PyQt."""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """Initialise le canvas."""
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        
        super().__init__(self.fig)
        self.setParent(parent)
        
        # Ajuster la taille
        FigureCanvas.setSizePolicy(self, 
                                  QSizePolicy.Expanding, 
                                  QSizePolicy.Expanding)
        FigureCanvas.updateGeometry(self)

class GaugeWidget(QWidget):
    """Widget de jauge circulaire pour afficher une valeur en pourcentage."""
    
    def __init__(self, parent=None, min_value=0, max_value=100, value=0, title="", unit=""):
        """Initialise le widget de jauge."""
        super().__init__(parent)
        self._min_value = min_value
        self._max_value = max_value
        self._value = value
        self._title = title
        self._unit = unit
        
        # Couleurs
        self._background_color = QColor("#F5F5F5")
        self._track_color = QColor("#E0E0E0")
        self._progress_color = QColor("#1976D2")  # Bleu par défaut
        self._text_color = QColor("#212121")
        
        # Configuration
        self._start_angle = 135
        self._span_angle = 270
        self._line_width = 15
        
        # Seuils pour les couleurs
        self._low_threshold = 25
        self._medium_threshold = 75
        
        # Mise en page
        self._layout = QVBoxLayout(self)
        self._title_label = QLabel(self._title)
        self._title_label.setAlignment(Qt.AlignCenter)
        self._layout.addWidget(self._title_label)
        
        # Taille minimale
        self.setMinimumSize(150, 150)
        
        # Mise à jour des couleurs
        self._update_colors()
    
    def _update_colors(self):
        """Met à jour les couleurs en fonction de la valeur."""
        percentage = self.get_percentage()
        
        if percentage < self._low_threshold:
            self._progress_color = QColor("#D32F2F")  # Rouge pour niveau bas
        elif percentage < self._medium_threshold:
            self._progress_color = QColor("#F57C00")  # Orange pour niveau moyen
        else:
            self._progress_color = QColor("#388E3C")  # Vert pour niveau élevé
    
    def get_percentage(self):
        """Calcule le pourcentage de la valeur actuelle."""
        range_value = self._max_value - self._min_value
        if range_value == 0:
            return 0
        return ((self._value - self._min_value) / range_value) * 100
    
    def set_value(self, value):
        """Définit la valeur actuelle."""
        self._value = max(self._min_value, min(self._max_value, value))
        self._update_colors()
        self.update()
    
    def paintEvent(self, event):
        """Dessine la jauge."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Calculer le rectangle pour la jauge
        rect = self.rect()
        side = min(rect.width(), rect.height())
        margin = self._line_width + 10
        gauge_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # Dessiner le fond
        painter.fillRect(rect, self._background_color)
        
        # Dessiner la piste
        painter.setPen(QPen(self._track_color, self._line_width, Qt.SolidLine))
        painter.drawArc(gauge_rect, self._start_angle * 16, self._span_angle * 16)
        
        # Dessiner la progression
        percentage = self.get_percentage()
        span = (percentage / 100) * self._span_angle
        painter.setPen(QPen(self._progress_color, self._line_width, Qt.SolidLine))
        painter.drawArc(gauge_rect, self._start_angle * 16, span * 16)
        
        # Dessiner la valeur
        painter.setPen(self._text_color)
        font = painter.font()
        font.setPointSize(14)
        font.setBold(True)
        painter.setFont(font)
        
        value_text = f"{self._value:.1f} {self._unit}"
        painter.drawText(gauge_rect, Qt.AlignCenter, value_text)

class MatplotlibBarChart(QWidget):
    """Widget pour afficher un graphique à barres avec Matplotlib."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique à barres."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.layout.addWidget(self.title_label)
        
        # Canvas Matplotlib
        self.canvas = MatplotlibCanvas(self, width=5, height=4, dpi=100)
        self.layout.addWidget(self.canvas)
        
        # Données initiales
        self.categories = ["Catégorie 1", "Catégorie 2", "Catégorie 3", "Catégorie 4"]
        self.data = [
            {"name": "Série 1", "values": [10, 15, 8, 12]},
            {"name": "Série 2", "values": [5, 8, 12, 7]}
        ]
        
        # Mettre à jour le graphique
        self.update_chart()
    
    def update_chart(self):
        """Met à jour le graphique avec les données actuelles."""
        # Effacer le graphique
        self.canvas.axes.clear()
        
        # Configurer le graphique
        x = np.arange(len(self.categories))
        width = 0.8 / len(self.data)  # Largeur des barres
        
        # Tracer les barres
        for i, series in enumerate(self.data):
            offset = width * i - width * (len(self.data) - 1) / 2
            bars = self.canvas.axes.bar(x + offset, series["values"], width, 
                                       label=series["name"], 
                                       color=CHART_COLORS[i % len(CHART_COLORS)])
            
            # Ajouter les valeurs au-dessus des barres
            for bar in bars:
                height = bar.get_height()
                self.canvas.axes.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                                     f'{height:.1f}', ha='center', va='bottom')
        
        # Configurer les axes
        self.canvas.axes.set_xlabel(self.x_label)
        self.canvas.axes.set_ylabel(self.y_label)
        self.canvas.axes.set_title(self.title)
        self.canvas.axes.set_xticks(x)
        self.canvas.axes.set_xticklabels(self.categories)
        self.canvas.axes.legend()
        
        # Ajuster la mise en page
        self.canvas.fig.tight_layout()
        
        # Rafraîchir le canvas
        self.canvas.draw()

class MatplotlibLineChart(QWidget):
    """Widget pour afficher un graphique linéaire avec Matplotlib."""
    
    def __init__(self, parent=None, title="", x_label="", y_label=""):
        """Initialise le widget de graphique linéaire."""
        super().__init__(parent)
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        
        # Mise en page
        self.layout = QVBoxLayout(self)
        
        # En-tête avec titre
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.layout.addWidget(self.title_label)
        
        # Canvas Matplotlib
        self.canvas = MatplotlibCanvas(self, width=5, height=4, dpi=100)
        self.layout.addWidget(self.canvas)
        
        # Données initiales
        today = datetime.now()
        self.dates = [(today - timedelta(days=i)).strftime("%d/%m") for i in range(7, 0, -1)]
        self.data = [
            {"name": "Série 1", "values": [10, 12, 15, 13, 17, 19, 18]},
            {"name": "Série 2", "values": [5, 7, 8, 9, 11, 10, 12]}
        ]
        
        # Mettre à jour le graphique
        self.update_chart()
    
    def update_chart(self):
        """Met à jour le graphique avec les données actuelles."""
        # Effacer le graphique
        self.canvas.axes.clear()
        
        # Tracer les lignes
        for i, series in enumerate(self.data):
            self.canvas.axes.plot(self.dates, series["values"], 
                                 marker='o', 
                                 linewidth=2, 
                                 label=series["name"],
                                 color=CHART_COLORS[i % len(CHART_COLORS)])
        
        # Configurer les axes
        self.canvas.axes.set_xlabel(self.x_label)
        self.canvas.axes.set_ylabel(self.y_label)
        self.canvas.axes.set_title(self.title)
        
        # Rotation des étiquettes de l'axe X pour une meilleure lisibilité
        self.canvas.axes.tick_params(axis='x', rotation=45)
        
        # Ajouter une grille
        self.canvas.axes.grid(True, linestyle='--', alpha=0.7)
        
        # Ajouter une légende
        self.canvas.axes.legend()
        
        # Ajuster la mise en page
        self.canvas.fig.tight_layout()
        
        # Rafraîchir le canvas
        self.canvas.draw()

class ChartsWindow(QMainWindow):
    """Fenêtre principale pour les graphiques."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("Graphiques Phosphate")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        layout = QVBoxLayout(central_widget)
        
        # Groupe des jauges
        gauges_group = QGroupBox("Niveaux de stock")
        gauges_layout = QHBoxLayout()
        
        # Jauges
        self.reception_gauge = GaugeWidget(title="Stock Réception", unit="t")
        self.reception_gauge.set_value(500)
        gauges_layout.addWidget(self.reception_gauge)
        
        self.crushing_gauge = GaugeWidget(title="Stock Concassage", unit="t")
        self.crushing_gauge.set_value(250)
        gauges_layout.addWidget(self.crushing_gauge)
        
        self.laverie_gauge = GaugeWidget(title="Stock Laverie", unit="t")
        self.laverie_gauge.set_value(150)
        gauges_layout.addWidget(self.laverie_gauge)
        
        gauges_group.setLayout(gauges_layout)
        layout.addWidget(gauges_group)
        
        # Groupe des graphiques
        charts_group = QGroupBox("Graphiques de production")
        charts_layout = QHBoxLayout()
        
        # Graphique à barres
        self.bar_chart = MatplotlibBarChart(
            title="Production par étape",
            x_label="Étape",
            y_label="Quantité (tonnes)"
        )
        charts_layout.addWidget(self.bar_chart)
        
        # Graphique linéaire
        self.line_chart = MatplotlibLineChart(
            title="Évolution de la production",
            x_label="Date",
            y_label="Quantité (tonnes)"
        )
        charts_layout.addWidget(self.line_chart)
        
        charts_group.setLayout(charts_layout)
        layout.addWidget(charts_group)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("Rafraîchir les graphiques")
        refresh_button.clicked.connect(self.refresh_charts)
        layout.addWidget(refresh_button)
        
        # Appliquer le style
        self.apply_style()
    
    def refresh_charts(self):
        """Rafraîchit les graphiques."""
        logger.debug("Rafraîchissement des graphiques")
        
        # Mettre à jour les jauges avec des valeurs aléatoires
        import random
        self.reception_gauge.set_value(random.randint(300, 700))
        self.crushing_gauge.set_value(random.randint(150, 350))
        self.laverie_gauge.set_value(random.randint(50, 250))
        
        # Mettre à jour le graphique à barres
        self.bar_chart.categories = ["Réception", "Concassage", "Laverie"]
        self.bar_chart.data = [
            {"name": "Production", "values": [random.randint(500, 1000), random.randint(400, 800), random.randint(300, 600)]}
        ]
        self.bar_chart.update_chart()
        
        # Mettre à jour le graphique linéaire
        today = datetime.now()
        self.line_chart.dates = [(today - timedelta(days=i)).strftime("%d/%m") for i in range(7, 0, -1)]
        self.line_chart.data = [
            {"name": "Réception", "values": [random.randint(100, 200) for _ in range(7)]},
            {"name": "Concassage", "values": [random.randint(80, 180) for _ in range(7)]},
            {"name": "Laverie", "values": [random.randint(60, 160) for _ in range(7)]}
        ]
        self.line_chart.update_chart()
    
    def apply_style(self):
        """Applique le style à l'application."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 1em;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)

def main():
    """Point d'entrée principal de l'application."""
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.debug("Base de données initialisée")
        
        # Créer et afficher la fenêtre principale
        window = ChartsWindow(db_manager)
        window.show()
        logger.debug("Interface graphique affichée")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"Erreur lors du démarrage de l'application: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()