"""change_quality_column_to_string

Revision ID: change_quality_column_to_string
Revises: d9780a85eb0c
Create Date: 2025-06-13

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'change_quality_column_to_string'
down_revision = 'd9780a85eb0c'
branch_labels = None
depends_on = None


def upgrade():
    # Créer une table temporaire avec la nouvelle structure
    op.execute('''
    CREATE TABLE productions_temp (
        id INTEGER NOT NULL, 
        quantity FLOAT NOT NULL, 
        quality VARCHAR(50), 
        quantity_used FLOAT, 
        production_hours FLOAT, 
        production_date DATETIME, 
        step_id INTEGER, 
        PRIMARY KEY (id), 
        FOREIGN KEY(step_id) REFERENCES process_steps (id)
    )
    ''')
    
    # Copier les données de l'ancienne table vers la nouvelle
    op.execute('''
    INSERT INTO productions_temp (id, quantity, quality, quantity_used, production_hours, production_date, step_id)
    SELECT id, quantity, quality, quantity_used, production_hours, production_date, step_id FROM productions
    ''')
    
    # Supprimer l'ancienne table
    op.execute('DROP TABLE productions')
    
    # Renommer la table temporaire
    op.execute('ALTER TABLE productions_temp RENAME TO productions')


def downgrade():
    # Cette opération n'est pas réversible sans perte de données
    pass