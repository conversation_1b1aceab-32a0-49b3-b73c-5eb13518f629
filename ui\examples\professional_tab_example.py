"""
Exemple d'onglet utilisant les widgets professionnels.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QScrollArea, QSplitter, QGroupBox)
from PyQt5.QtCore import Qt, QTimer
from ui.widgets.professional_widgets import (KPICard, StatusIndicator, 
                                           ProfessionalTable, ActionButton,
                                           SectionHeader, DashboardWidget)
from ui.icon_manager import icon_manager

class ProfessionalTabExample(QWidget):
    """Exemple d'onglet avec design professionnel."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)
        
        # En-tête de section
        header = SectionHeader(
            "Production en Temps Réel", 
            "Surveillance et contrôle des opérations de production"
        )
        main_layout.addWidget(header)
        
        # Splitter pour diviser l'écran
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Zone gauche - Tableau de bord
        self.setup_dashboard_area(splitter)
        
        # Zone droite - Contrôles et données
        self.setup_controls_area(splitter)
        
        # Configurer les proportions du splitter
        splitter.setSizes([400, 800])
        
    def setup_dashboard_area(self, parent):
        """Configure la zone de tableau de bord."""
        # Widget de conteneur pour le dashboard
        dashboard_widget = QWidget()
        dashboard_layout = QVBoxLayout(dashboard_widget)
        dashboard_layout.setContentsMargins(10, 10, 10, 10)
        
        # Titre du dashboard
        dashboard_header = SectionHeader("Indicateurs Clés", "KPI en temps réel")
        dashboard_layout.addWidget(dashboard_header)
        
        # Grille de KPI
        kpi_grid = QGridLayout()
        kpi_grid.setSpacing(10)
        
        # Exemples de cartes KPI
        production_card = KPICard("Production Totale", "1,247", "T", trend=2.5)
        kpi_grid.addWidget(production_card, 0, 0)
        
        efficacite_card = KPICard("Efficacité", "89.2", "%", trend=-1.2)
        kpi_grid.addWidget(efficacite_card, 0, 1)
        
        qualite_card = KPICard("Qualité P2O5", "31.8", "%", trend=0.5)
        kpi_grid.addWidget(qualite_card, 1, 0)
        
        temps_arret_card = KPICard("Temps d'Arrêt", "47", "min", trend=-8.3)
        kpi_grid.addWidget(temps_arret_card, 1, 1)
        
        dashboard_layout.addLayout(kpi_grid)
        
        # Section des indicateurs de statut
        status_header = SectionHeader("Statut des Équipements", "")
        dashboard_layout.addWidget(status_header)
        
        # Indicateurs de statut
        concasseur_status = StatusIndicator("Concasseur Principal", "En cours")
        dashboard_layout.addWidget(concasseur_status)
        
        laverie_status = StatusIndicator("Laverie", "En cours")
        dashboard_layout.addWidget(laverie_status)
        
        convoyeur_status = StatusIndicator("Convoyeur B1", "Maintenance")
        dashboard_layout.addWidget(convoyeur_status)
        
        dashboard_layout.addStretch()
        
        # Scroll area pour le dashboard
        scroll_area = QScrollArea()
        scroll_area.setWidget(dashboard_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumWidth(350)
        parent.addWidget(scroll_area)
        
    def setup_controls_area(self, parent):
        """Configure la zone de contrôles et données."""
        controls_widget = QWidget()
        controls_layout = QVBoxLayout(controls_widget)
        controls_layout.setContentsMargins(15, 10, 15, 10)
        controls_layout.setSpacing(15)
        
        # Barre d'actions
        actions_layout = QHBoxLayout()
        
        refresh_btn = ActionButton("Actualiser", "Actualiser", "primary")
        actions_layout.addWidget(refresh_btn)
        
        add_btn = ActionButton("Nouveau", "Ajouter", "success")
        actions_layout.addWidget(add_btn)
        
        export_btn = ActionButton("Exporter", "Exporter", "primary")
        actions_layout.addWidget(export_btn)
        
        actions_layout.addStretch()
        
        stop_btn = ActionButton("Arrêt d'Urgence", "Arrêt", "danger")
        actions_layout.addWidget(stop_btn)
        
        controls_layout.addLayout(actions_layout)
        
        # GroupBox pour les données de production
        production_group = QGroupBox("Données de Production")
        production_layout = QVBoxLayout(production_group)
        
        # Tableau professionnel
        self.production_table = ProfessionalTable()
        self.production_table.setColumnCount(6)
        self.production_table.setHorizontalHeaderLabels([
            "Heure", "Étape", "Quantité (T)", "Qualité (%)", "Rendement", "Statut"
        ])
        
        # Données d'exemple
        sample_data = [
            ["08:00", "Concassage", "125.5", "92.3", "15.7 T/h", "En cours"],
            ["08:00", "Laverie", "98.2", "31.8", "12.3 T/h", "En cours"],
            ["08:00", "Transport", "110.1", "-", "13.8 T/h", "En cours"],
            ["07:45", "Concassage", "118.9", "91.8", "15.2 T/h", "Terminé"],
            ["07:45", "Laverie", "95.1", "31.5", "12.1 T/h", "Terminé"],
        ]
        
        self.production_table.setRowCount(len(sample_data))
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.production_table.setItem(row, col, 
                    self.production_table.item(row, col) or 
                    self.production_table.__class__.itemClass()(value))
        
        production_layout.addWidget(self.production_table)
        controls_layout.addWidget(production_group)
        
        parent.addWidget(controls_widget)
        
    def refresh_data(self):
        """Actualise les données affichées."""
        # Ici on mettrait la logique de mise à jour des données
        pass
