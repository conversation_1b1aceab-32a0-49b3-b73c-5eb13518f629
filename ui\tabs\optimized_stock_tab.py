"""
Onglet stock optimisé avec tableau de bord temps réel et gestion avancée.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QComboBox, QLabel, QFrame)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QPainter, QColor
from datetime import datetime, timedelta
import logging

from .optimized_base import BaseOptimizedTab, StatusWidget
from ui.widgets.professional_widgets import KPILine, KPICard, StatusIndicator, ActionButton
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class StockLevelIndicator(QFrame):
    """Indicateur visuel du niveau de stock."""
    
    def __init__(self, title, capacity=1000, parent=None):
        super().__init__(parent)
        self.title = title
        self.capacity = capacity
        self.current_level = 0
        self.quality = None
        
        self.setProperty("class", "card")
        self.setMinimumHeight(200)
        self.setMaximumHeight(250)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            text-align: center;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Zone de visualisation du tank
        self.tank_widget = QLabel()
        self.tank_widget.setMinimumHeight(120)
        self.tank_widget.setStyleSheet("border: 2px solid #E5E7EB; border-radius: 8px;")
        layout.addWidget(self.tank_widget)
        
        # Informations textuelles
        self.info_label = QLabel("0 / 1000 T (0%)")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet(f"""
            font-size: 12pt;
            font-weight: bold;
            color: {SotramineTheme.TEXT_PRIMARY};
            padding: 5px;
        """)
        layout.addWidget(self.info_label)
        
        # Label qualité
        self.quality_label = QLabel("Qualité: -")
        self.quality_label.setAlignment(Qt.AlignCenter)
        self.quality_label.setStyleSheet(f"""
            font-size: 10pt;
            color: {SotramineTheme.TEXT_SECONDARY};
        """)
        layout.addWidget(self.quality_label)
        
        self.update_display()
        
    def set_level(self, level, quality=None):
        """Met à jour le niveau et la qualité."""
        self.current_level = max(0, level)
        self.quality = quality
        self.update_display()
        
    def update_display(self):
        """Met à jour l'affichage visuel."""
        # Calculer le pourcentage
        percentage = min(100, (self.current_level / self.capacity) * 100)
        
        # Mettre à jour le texte
        self.info_label.setText(f"{self.current_level:.1f} / {self.capacity} T ({percentage:.1f}%)")
        
        # Mettre à jour la qualité
        if self.quality is not None:
            self.quality_label.setText(f"Qualité P2O5: {self.quality:.1f}%")
        else:
            self.quality_label.setText("Qualité: -")
        
        # Créer l'image du réservoir
        pixmap = QPixmap(self.tank_widget.size())
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dessiner le contour du réservoir
        tank_rect = pixmap.rect().adjusted(10, 10, -10, -10)
        painter.setPen(QColor(SotramineTheme.GRAY_400))
        painter.setBrush(QColor(SotramineTheme.GRAY_100))
        painter.drawRoundedRect(tank_rect, 8, 8)
        
        # Dessiner le niveau de remplissage
        if percentage > 0:
            fill_height = int((tank_rect.height() - 4) * percentage / 100)
            fill_rect = tank_rect.adjusted(2, tank_rect.height() - fill_height - 2, -2, -2)
            
            # Couleur selon le niveau
            if percentage < 20:
                fill_color = QColor(SotramineTheme.DANGER)
            elif percentage < 50:
                fill_color = QColor(SotramineTheme.ACCENT)
            else:
                fill_color = QColor(SotramineTheme.SECONDARY)
                
            painter.setBrush(fill_color)
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(fill_rect, 6, 6)
        
        painter.end()
        self.tank_widget.setPixmap(pixmap)

class OptimizedStockTab(BaseOptimizedTab):
    """Onglet stock optimisé."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📦 Gestion des Stocks", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface spécialisée."""
        # Section Vue d'ensemble (remplace les KPI normaux)
        overview_layout = QVBoxLayout()
        
        # Titre de la section
        overview_title = QLabel("📊 Vue d'Ensemble des Stocks")
        overview_title.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {SotramineTheme.PRIMARY};
            margin-bottom: 15px;
        """)
        overview_layout.addWidget(overview_title)
        
        # KPI de stock en lignes simples
        stock_kpi_layout = QVBoxLayout()
        stock_kpi_layout.setSpacing(0)
        
        # Stocks par étape
        self.reception_stock_line = KPILine("📥 Stock Réception", "0", "T")
        self.crushing_stock_line = KPILine("🔨 Stock Concassage", "0", "T")
        self.laverie_stock_line = KPILine("💧 Stock Laverie", "0", "T")
        
        # Qualité par étape
        self.reception_quality_line = KPILine("📥 Qualité Réception", "0", "%P2O5")
        self.crushing_quality_line = KPILine("🔨 Qualité Concassage", "0", "%P2O5")
        self.laverie_quality_line = KPILine("💧 Qualité Laverie", "0", "%P2O5")
        
        # Ajouter les KPI au layout
        stock_kpi_layout.addWidget(self.reception_stock_line)
        stock_kpi_layout.addWidget(self.reception_quality_line)
        stock_kpi_layout.addWidget(self.crushing_stock_line)
        stock_kpi_layout.addWidget(self.crushing_quality_line)
        stock_kpi_layout.addWidget(self.laverie_stock_line)
        stock_kpi_layout.addWidget(self.laverie_quality_line)
        
        overview_layout.addLayout(stock_kpi_layout)
        
        # Ajouter un espace
        overview_layout.addSpacing(20)
        
        # Ajouter la section overview au layout des contrôles
        self.controls_layout.addLayout(overview_layout)
        
        # Section Filtres et Actions
        actions_layout = self.add_actions_section("🔧 Actions & Filtres")
        
        # Filtres
        filter_frame = QFrame()
        filter_frame.setProperty("class", "card")
        filter_layout = QVBoxLayout(filter_frame)
        
        filter_title = QLabel("🔍 Filtres")
        filter_title.setStyleSheet("font-weight: bold; font-size: 11pt;")
        filter_layout.addWidget(filter_title)
        
        # Filtre par étape
        step_layout = QHBoxLayout()
        step_layout.addWidget(QLabel("Étape:"))
        
        self.step_filter = QComboBox()
        self.step_filter.addItems(["Toutes", "Réception", "Concassage", "Laverie"])
        self.step_filter.currentTextChanged.connect(self.refresh_data)
        step_layout.addWidget(self.step_filter)
        
        filter_layout.addLayout(step_layout)
        
        # Filtre par type
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type:"))
        
        self.type_filter = QComboBox()
        self.type_filter.addItems(["Tous", "Réception", "Production", "Consommation", "Transfert"])
        self.type_filter.currentTextChanged.connect(self.refresh_data)
        type_layout.addWidget(self.type_filter)
        
        filter_layout.addLayout(type_layout)
        
        # Filtre par période
        period_layout = QHBoxLayout()
        period_layout.addWidget(QLabel("Période:"))
        
        self.period_filter = QComboBox()
        self.period_filter.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Tout"])
        self.period_filter.currentTextChanged.connect(self.refresh_data)
        period_layout.addWidget(self.period_filter)
        
        filter_layout.addLayout(period_layout)
        
        actions_layout.addWidget(filter_frame)
        
        # Boutons d'action
        refresh_btn = ActionButton("🔄 Actualiser", "Actualiser", "primary")
        refresh_btn.clicked.connect(self.refresh_all_data)
        actions_layout.addWidget(refresh_btn)
        
        export_btn = ActionButton("📤 Exporter", "Exporter", "primary")
        export_btn.clicked.connect(self.export_stock_data)
        actions_layout.addWidget(export_btn)
        
        # Section Alertes
        alerts_layout = QVBoxLayout()
        alerts_title = QLabel("⚠️ Alertes Stock")
        alerts_title.setStyleSheet("font-weight: bold; font-size: 11pt; margin-top: 10px;")
        alerts_layout.addWidget(alerts_title)
        
        self.alerts_frame = QFrame()
        self.alerts_frame.setProperty("class", "card")
        self.alerts_layout = QVBoxLayout(self.alerts_frame)
        
        alerts_layout.addWidget(self.alerts_frame)
        actions_layout.addLayout(alerts_layout)
        
        self.add_stretch()
        
        # Tableau des mouvements (zone droite)
        headers = ["Date/Heure", "Étape", "Type", "Quantité (T)", "Qualité P2O5 (%)", "Référence"]
        self.data_table = self.add_data_table("📋 Mouvements de Stock", headers)
        
        # Charger les données initiales
        self.refresh_all_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour."""
        # Timer pour les stocks
        self.stock_timer = QTimer()
        self.stock_timer.timeout.connect(self.update_stock_levels)
        self.stock_timer.start(15000)  # Mise à jour toutes les 15 secondes
        
        # Timer pour les données - DÉSACTIVÉ
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.refresh_data)
        # self.data_timer.start(60000)  # DÉSACTIVÉ
        
    def update_stock_levels(self):
        """Met à jour les niveaux de stock en temps réel."""
        try:
            # Stock de réception
            reception_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.RECEPTION)
            self.reception_stock_line.update_value(f"{reception_data['stock']:.1f}")
            
            if reception_data['quality'] is not None:
                self.reception_quality_line.update_value(f"{reception_data['quality']:.1f}")
            else:
                self.reception_quality_line.update_value("--")
            
            # Stock de concassage
            crushing_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.CONCASSAGE)
            self.crushing_stock_line.update_value(f"{crushing_data['stock']:.1f}")
            
            if crushing_data['quality'] is not None:
                self.crushing_quality_line.update_value(f"{crushing_data['quality']:.1f}")
            else:
                self.crushing_quality_line.update_value("--")
            
            # Stock de laverie
            laverie_data = self.db_manager.get_stock_with_quality(ProcessStepEnum.LAVERIE)
            self.laverie_stock_line.update_value(f"{laverie_data['stock']:.1f}")
            
            if laverie_data['quality'] is not None:
                self.laverie_quality_line.update_value(f"{laverie_data['quality']:.1f}")
            else:
                self.laverie_quality_line.update_value("--")
            
            # Mettre à jour les alertes
            self.update_alerts(reception_data, crushing_data, laverie_data)
            
        except Exception as e:
            logging.error(f"Erreur mise à jour niveaux stock: {str(e)}")
            
    def update_alerts(self, reception_data, crushing_data, laverie_data):
        """Met à jour les alertes de stock."""
        # Nettoyer les alertes existantes
        for i in reversed(range(self.alerts_layout.count())):
            self.alerts_layout.itemAt(i).widget().setParent(None)
            
        alerts = []
        
        # Vérifier les seuils critiques
        if reception_data['stock'] < 100:
            alerts.append(("🔴 Stock Réception Critique", f"{reception_data['stock']:.1f}T < 100T"))
            self.reception_stock_line.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-weight: bold;")
        else:
            self.reception_stock_line.setStyleSheet("")
            
        if crushing_data['stock'] < 50:
            alerts.append(("🔴 Stock Concassage Critique", f"{crushing_data['stock']:.1f}T < 50T"))
            self.crushing_stock_line.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-weight: bold;")
        else:
            self.crushing_stock_line.setStyleSheet("")
            
        if laverie_data['stock'] < 30:
            alerts.append(("🔴 Stock Laverie Critique", f"{laverie_data['stock']:.1f}T < 30T"))
            self.laverie_stock_line.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-weight: bold;")
        else:
            self.laverie_stock_line.setStyleSheet("")
            
        # Vérifier la qualité
        if laverie_data['quality'] and laverie_data['quality'] < 30:
            alerts.append(("🟡 Qualité Faible", f"P2O5: {laverie_data['quality']:.1f}% < 30%"))
            self.laverie_quality_line.setStyleSheet(f"color: {SotramineTheme.ACCENT}; font-weight: bold;")
            
        # Afficher les alertes
        if alerts:
            for title, message in alerts:
                alert_indicator = StatusIndicator(title, message)
                self.alerts_layout.addWidget(alert_indicator)
        else:
            no_alert = QLabel("✅ Aucune alerte")
            no_alert.setStyleSheet("color: green; font-weight: bold; padding: 10px;")
            self.alerts_layout.addWidget(no_alert)
            
    def refresh_all_data(self):
        """Actualise toutes les données."""
        self.update_stock_levels()
        self.refresh_data()
        
    def refresh_data(self):
        """Actualise le tableau des mouvements."""
        try:
            # Récupérer les filtres
            step_filter = self.step_filter.currentText()
            type_filter = self.type_filter.currentText()
            period_filter = self.period_filter.currentText()
            
            # Convertir les filtres
            step = None
            if step_filter != "Toutes":
                step_map = {
                    "Réception": ProcessStepEnum.RECEPTION,
                    "Concassage": ProcessStepEnum.CONCASSAGE,
                    "Laverie": ProcessStepEnum.LAVERIE
                }
                step = step_map.get(step_filter)
                
            movement_type = None
            if type_filter != "Tous":
                type_map = {
                    "Réception": StockMovementType.RECEPTION,
                    "Production": StockMovementType.PRODUCTION,
                    "Consommation": StockMovementType.CONSUMPTION,
                    "Transfert": StockMovementType.TRANSFER
                }
                movement_type = type_map.get(type_filter)
                
            # Calculer les dates
            end_date = datetime.now()
            if period_filter == "Aujourd'hui":
                start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period_filter == "Cette semaine":
                start_date = end_date - timedelta(days=end_date.weekday())
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period_filter == "Ce mois":
                start_date = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            else:  # Tout
                start_date = end_date - timedelta(days=30)
                
            # Récupérer les mouvements
            movements = self.db_manager.get_stock_movements(
                step=step,
                movement_type=movement_type,
                start_date=start_date,
                end_date=end_date,
                limit=100  # Limiter à 100 derniers mouvements
            )
            
            self.populate_table(movements)
            
        except Exception as e:
            logging.error(f"Erreur actualisation données stock: {str(e)}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors de l'actualisation: {str(e)}")
            
    def populate_table(self, movements):
        """Remplit le tableau avec les mouvements."""
        self.data_table.setRowCount(len(movements))
        
        for row, movement in enumerate(movements):
            # Date/Heure
            self.data_table.setItem(row, 0,
                self.data_table.itemClass()(movement['date'].strftime("%d/%m/%Y %H:%M")))
            
            # Étape
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(movement['step']))
            
            # Type
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(movement['movement_type']))
            
            # Quantité (avec signe pour les consommations)
            quantity = movement['quantity']
            if movement['movement_type'] == 'Consommation':
                quantity_str = f"-{abs(quantity):.2f}"
            else:
                quantity_str = f"+{quantity:.2f}"
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()(quantity_str))
            
            # Qualité
            quality = f"{movement['quality']:.1f}" if movement['quality'] else "-"
            self.data_table.setItem(row, 4,
                self.data_table.itemClass()(quality))
            
            # Référence
            ref = str(movement.get('reference_id', '')) if movement.get('reference_id') else "-"
            self.data_table.setItem(row, 5,
                self.data_table.itemClass()(ref))
                
    def export_stock_data(self):
        """Exporte les données de stock."""
        try:
            QMessageBox.information(self, "📤 Export", 
                "Fonctionnalité d'export en cours de développement")
        except Exception as e:
            logging.error(f"Erreur export: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")
