import matplotlib
matplotlib.use('Agg')  # Use the Agg backend (non-interactive)
import matplotlib.pyplot as plt
import numpy as np

def main():
    try:
        print("Creating figure...")
        fig, ax = plt.subplots()
        
        print("Creating plot...")
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        
        print("Setting labels...")
        ax.set_xlabel('x')
        ax.set_ylabel('y')
        ax.set_title('Simple Plot with Agg Backend')
        
        print("Saving figure...")
        plt.savefig('test_agg_plot.png')
        print("Figure saved to test_agg_plot.png")
        
        return 0
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("Starting matplotlib Agg test...")
    exit_code = main()
    print(f"Test completed with exit code: {exit_code}")