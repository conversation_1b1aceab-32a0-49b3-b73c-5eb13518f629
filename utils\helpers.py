from datetime import datetime, timedelta
from typing import Union, Optional, List, Dict, Any
import logging
from decimal import Decimal, ROUND_HALF_UP
import locale
from pathlib import Path

def setup_logging(log_file: str = "app.log", level: str = "INFO") -> None:
    """Configure le système de logging."""
    numeric_level = getattr(logging, level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Niveau de log invalide: {level}")
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

def format_number(number: Union[int, float, Decimal], 
                 decimal_places: int = 2,
                 decimal_separator: str = ",",
                 thousand_separator: str = " ") -> str:
    """Formate un nombre avec les séparateurs spécifiés."""
    try:
        # Convertir en Decimal pour une meilleure précision
        if isinstance(number, (int, float)):
            number = Decimal(str(number))
        
        # Arrondir au nombre de décimales spécifié
        number = number.quantize(Decimal('0.' + '0' * decimal_places), rounding=ROUND_HALF_UP)
        
        # Convertir en chaîne avec le format local
        locale.setlocale(locale.LC_ALL, '')
        formatted = locale.format_string("%.{}f".format(decimal_places), number, grouping=True)
        
        # Remplacer les séparateurs par ceux spécifiés
        formatted = formatted.replace(",", decimal_separator)
        formatted = formatted.replace(" ", thousand_separator)
        
        return formatted
    except Exception as e:
        logging.error(f"Erreur lors du formatage du nombre {number}: {e}")
        return str(number)

def parse_date(date_str: str, format: str = "%d/%m/%Y") -> Optional[datetime]:
    """Parse une chaîne de date selon le format spécifié."""
    try:
        return datetime.strptime(date_str, format)
    except ValueError as e:
        logging.error(f"Erreur lors du parsing de la date {date_str}: {e}")
        return None

def format_date(date: datetime, format: str = "%d/%m/%Y") -> str:
    """Formate une date selon le format spécifié."""
    try:
        return date.strftime(format)
    except Exception as e:
        logging.error(f"Erreur lors du formatage de la date {date}: {e}")
        return str(date)

def calculate_duration(start_time: datetime, end_time: datetime) -> float:
    """Calcule la durée en heures entre deux dates."""
    try:
        duration = end_time - start_time
        return duration.total_seconds() / 3600
    except Exception as e:
        logging.error(f"Erreur lors du calcul de la durée: {e}")
        return 0.0

def calculate_efficiency(produced: float, used: float) -> float:
    """Calcule le rendement en pourcentage."""
    try:
        if used <= 0:
            return 0.0
        return (produced / used) * 100
    except Exception as e:
        logging.error(f"Erreur lors du calcul du rendement: {e}")
        return 0.0

def calculate_availability(total_hours: float, downtime_hours: float) -> float:
    """Calcule la disponibilité en pourcentage."""
    try:
        if total_hours <= 0:
            return 0.0
        return ((total_hours - downtime_hours) / total_hours) * 100
    except Exception as e:
        logging.error(f"Erreur lors du calcul de la disponibilité: {e}")
        return 0.0

def validate_quantity(quantity: float, min_value: float = 0, max_value: float = float('inf')) -> bool:
    """Valide une quantité selon les limites spécifiées."""
    try:
        return min_value <= quantity <= max_value
    except Exception as e:
        logging.error(f"Erreur lors de la validation de la quantité {quantity}: {e}")
        return False

def validate_quality(quality: float, min_value: float = 0, max_value: float = 100) -> bool:
    """Valide une qualité selon les limites spécifiées."""
    try:
        return min_value <= quality <= max_value
    except Exception as e:
        logging.error(f"Erreur lors de la validation de la qualité {quality}: {e}")
        return False

def ensure_directory(path: Union[str, Path]) -> bool:
    """Crée un répertoire s'il n'existe pas."""
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Erreur lors de la création du répertoire {path}: {e}")
        return False

def get_file_extension(filename: str) -> str:
    """Récupère l'extension d'un fichier."""
    try:
        return Path(filename).suffix.lower()
    except Exception as e:
        logging.error(f"Erreur lors de la récupération de l'extension du fichier {filename}: {e}")
        return ""

def is_valid_file(filename: str, allowed_extensions: List[str]) -> bool:
    """Vérifie si un fichier a une extension valide."""
    try:
        return get_file_extension(filename) in allowed_extensions
    except Exception as e:
        logging.error(f"Erreur lors de la validation du fichier {filename}: {e}")
        return False

def format_duration(minutes: int) -> str:
    """Formate une durée en minutes en format lisible."""
    try:
        hours = minutes // 60
        remaining_minutes = minutes % 60
        if hours > 0:
            return f"{hours}h {remaining_minutes}min"
        return f"{remaining_minutes}min"
    except Exception as e:
        logging.error(f"Erreur lors du formatage de la durée {minutes}: {e}")
        return str(minutes)

def calculate_daily_stats(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calcule les statistiques journalières à partir d'une liste de données."""
    try:
        if not data:
            return {
                "total_quantity": 0,
                "average_quality": 0,
                "total_hours": 0,
                "downtime_hours": 0,
                "efficiency": 0,
                "availability": 0
            }
        
        total_quantity = sum(item.get("quantity", 0) for item in data)
        total_quality = sum(item.get("quality", 0) for item in data)
        total_hours = sum(item.get("hours", 0) for item in data)
        total_downtime = sum(item.get("downtime", 0) for item in data)
        total_used = sum(item.get("quantity_used", 0) for item in data)
        
        count = len(data)
        average_quality = total_quality / count if count > 0 else 0
        efficiency = calculate_efficiency(total_quantity, total_used)
        availability = calculate_availability(total_hours, total_downtime)
        
        return {
            "total_quantity": total_quantity,
            "average_quality": average_quality,
            "total_hours": total_hours,
            "downtime_hours": total_downtime,
            "efficiency": efficiency,
            "availability": availability
        }
    except Exception as e:
        logging.error(f"Erreur lors du calcul des statistiques journalières: {e}")
        return {
            "total_quantity": 0,
            "average_quality": 0,
            "total_hours": 0,
            "downtime_hours": 0,
            "efficiency": 0,
            "availability": 0
        } 