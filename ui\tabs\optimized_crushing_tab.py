"""
Onglet concassage optimisé avec interface moderne et contrôle de production.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QSlider,
                            QSpinBox, QCheckBox, QGroupBox, QButtonGroup)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from datetime import datetime, timedelta
import logging

from .base_optimized import BaseOptimizedTab, QuickInputWidget, StatusWidget
from ui.widgets.professional_widgets import KPILine, StatusIndicator, ActionButton
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class OptimizedCrushingTab(BaseOptimizedTab):
    """Onglet concassage optimisé."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🔨 Concassage & Broyage", parent)
        self.is_running = False
        self.current_rate = 0
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface spécialisée."""
        # Section KPI Production
        kpi_layout = self.add_kpi_section("📊 Performance de Production")
        
        kpi_container = QVBoxLayout()
        kpi_container.setSpacing(0)
        
        self.stock_input_line = KPILine("Stock Entrée", "0", "T")
        self.production_line = KPILine("Production Jour", "0", "T") 
        self.efficiency_line = KPILine("Efficacité", "0", "%")
        self.output_line = KPILine("Stock Sortie", "0", "T")
        
        kpi_container.addWidget(self.stock_input_line)
        kpi_container.addWidget(self.production_line)
        kpi_container.addWidget(self.efficiency_line)
        kpi_container.addWidget(self.output_line)
        
        kpi_layout.addLayout(kpi_container)
        
        # Section Saisie Manuelle
        form_layout = self.add_form_section("📝 Saisie Production")
        
        fields = [
            {
                'name': 'quantity_produced',
                'label': 'Quantité Produite',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'quantity_consumed',
                'label': 'Quantité Consommée',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'production_hours',
                'label': 'Heures de Production',
                'type': 'double',
                'suffix': 'h'
            },
            {
                'name': 'quality',
                'label': 'Qualité Sortie',
                'type': 'double',
                'suffix': '%'
            }
        ]

        self.quick_input = QuickInputWidget(fields)
        self.quick_input.data_saved.connect(self.save_production)
        form_layout.addWidget(self.quick_input)
        
        self.add_stretch()
        
        # Tableau de production (zone droite)
        headers = ["Date/Heure", "Produit (T)", "Consommé (T)", "Heures", "Rendement (T/h)", "Efficacité (%)"]
        self.data_table = self.add_data_table("📈 Historique de Production", headers)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers - KPI seulement."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)
        

    def update_kpi(self):
        """Met à jour les KPI."""
        try:
            # Stock d'entrée (total des réceptions)
            # Récupérer le total des réceptions
            receptions = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.RECEPTION
            )
            total_receptions = sum(m['quantity'] for m in receptions)
            self.stock_input_line.update_value(f"{total_receptions:.1f}")
            
            # Stock de sortie (concassage)
            output_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.output_line.update_value(f"{output_stock:.1f}")
            
            # Production du jour
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=today
            )
            today_total = sum(p['quantity'] for p in today_productions)
            self.production_line.update_value(f"{today_total:.1f}")
            
            # Efficacité (production vs consommation)
            today_consumed = sum(p['quantity_used'] for p in today_productions if p['quantity_used'])
            efficiency = (today_total / today_consumed * 100) if today_consumed > 0 else 0
            self.efficiency_line.update_value(f"{efficiency:.1f}")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI concassage: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données."""
        try:
            # Dernières productions (7 derniers jours)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date
            )
            
            self.populate_table(productions)
            
        except Exception as e:
            logging.error(f"Erreur actualisation données concassage: {str(e)}")
            
    def populate_table(self, productions):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(productions))
        
        for row, production in enumerate(productions):
            # Date/Heure
            self.data_table.setItem(row, 0,
                self.data_table.itemClass()(production['production_date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité produite
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{production['quantity']:.2f}"))
            
            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))
            
            # Heures
            hours = production.get('production_hours', 0)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()(f"{hours:.2f}" if hours else "-"))
            
            # Rendement
            if hours and hours > 0:
                rendement = production['quantity'] / hours
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()(f"{rendement:.2f}"))
            else:
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()("-"))
            
            # Efficacité
            if consumed and consumed > 0:
                efficacity = (production['quantity'] / consumed) * 100
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()(f"{efficacity:.1f}%"))
            else:
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()("-"))
                    
    def save_production(self, data):
        """Sauvegarde une production manuelle."""
        try:
            # Validation
            if not data.get('quantity_produced') or float(data['quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return
                
            quantity_produced = float(data['quantity_produced'])
            quantity_consumed = float(data.get('quantity_consumed', 0))
            hours = float(data.get('production_hours', 0))
            quality_str = data.get('quality', '30.0')
            
            # Convertir la qualité en nombre (enlever le % si présent)
            if quality_str and isinstance(quality_str, str):
                quality_str = quality_str.replace('%', '').strip()
            
            quality_value = float(quality_str) if quality_str else None
            
            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality_value}%" if quality_value is not None else None
            )
            
            # Mouvements de stock
            if quantity_consumed > 0:
                # Vérifier le stock disponible avant de consommer
                available_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
                logging.info(f"Vérification du stock pour l'étape {ProcessStepEnum.CONCASSAGE.value}")
                logging.info(f"Stock disponible: {available_stock}, Quantité demandée: {quantity_consumed}")
                
                if available_stock < quantity_consumed:
                    raise ValueError(f"Stock insuffisant. Disponible: {available_stock}, Demandé: {quantity_consumed}")
                
                # Consommer du stock de concassage
                self.db_manager.add_stock_movement(
                    step=ProcessStepEnum.CONCASSAGE,
                    quantity=-quantity_consumed,
                    movement_type=StockMovementType.CONSUMPTION,
                    movement_date=datetime.now()
                )
            
            # Ajouter au stock de concassage
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                movement_type=StockMovementType.PRODUCTION,
                movement_date=datetime.now(),
                quality=quality_value
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Succès",
                f"Production de {quantity_produced:.1f}T enregistrée")
                
        except ValueError as e:
            error_msg = str(e)
            if "Stock insuffisant" in error_msg:
                QMessageBox.warning(self, "Stock Insuffisant", error_msg)
            else:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
