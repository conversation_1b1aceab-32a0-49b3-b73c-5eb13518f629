"""
Onglet réception optimisé avec interface moderne et performance améliorée.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, 
                            QFileDialog, QProgressBar)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from PyQt5.QtGui import QIcon
from datetime import datetime, timedelta
import logging
import pandas as pd

from .optimized_base import BaseOptimizedTab, QuickInputWidget, DataLoader, StatusWidget
from ui.widgets.professional_widgets import ActionButton
from models.enums import ProcessStepEnum, StockMovementType

class OptimizedReceptionTab(BaseOptimizedTab):
    """Onglet réception optimisé."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📥 Réception Phosphate", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface spécialisée."""
        # Section Saisie Rapide
        form_layout = self.add_form_section("⚡ Saisie Rapide")
        
        fields = {
            'quantity': {
                'label': 'Quantité (T):',
                'type': 'number',
                'placeholder': 'Ex: 125.50'
            },
            'quality': {
                'label': 'Qualité P2O5 (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2'
            },
            'reception_date': {
                'label': 'Date/Heure:',
                'type': 'datetime'
            },
            'supplier': {
                'label': 'Fournisseur:',
                'type': 'combo',
                'options': ['SOTRAMINE', 'Fournisseur A', 'Fournisseur B', 'Autre']
            }
        }
        
        self.quick_input = QuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_reception)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions
        actions_layout = self.add_actions_section("🔧 Actions Avancées")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Importer", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export
        export_btn = ActionButton("📤 Exporter Données", "Exporter", "primary")
        export_btn.clicked.connect(self.export_data)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau des données (zone droite)
        headers = ["Date/Heure", "Quantité (T)", "Qualité P2O5 (%)", "Fournisseur", "Actions"]
        self.data_table = self.add_data_table("📋 Historique des Réceptions", headers)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers de mise à jour - DÉSACTIVÉS pour réduire la charge."""
        # Timer pour le tableau - DÉSACTIVÉ
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.refresh_data)
        # self.data_timer.start(120000)  # DÉSACTIVÉ
        

    def refresh_data(self):
        """Actualise les données du tableau."""
        try:
            # Charger les 50 dernières réceptions
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 derniers jours
            
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.RECEPTION,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date,
                limit=50
            )
            
            # Mettre à jour le tableau
            self.populate_table(movements)
            
        except Exception as e:
            logging.error(f"Erreur actualisation données réception: {str(e)}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors de l'actualisation: {str(e)}")
            
    def populate_table(self, movements):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(movements))
        
        for row, movement in enumerate(movements):
            # Date/Heure
            self.data_table.setItem(row, 0, 
                self.data_table.itemClass()(movement['date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{movement['quantity']:.2f}"))
            
            # Qualité
            quality = f"{movement['quality']:.1f}" if movement['quality'] else "-"
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(quality))
            
            # Fournisseur (simulé pour l'exemple)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()("SOTRAMINE"))
            
            # Actions (boutons)
            self.data_table.setItem(row, 4,
                self.data_table.itemClass()("📝 Modifier | 🗑️ Supprimer"))
                
    def save_reception(self, data):
        """Sauvegarde une nouvelle réception."""
        try:
            # Validation des données
            if not data.get('quantity') or float(data['quantity']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité doit être positive")
                return
                
            quantity = float(data['quantity'])
            quality = float(data.get('quality', 0)) if data.get('quality') else None
            reception_date = data.get('reception_date', datetime.now())
            
            # Récupérer le stock actuel (sans vérification de capacité maximale)
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            new_stock_level = current_stock + quantity
            
            logging.info(f"Stock actuel={current_stock}T, Ajout={quantity}T, Nouveau niveau={new_stock_level}T")
            
            # Utiliser la méthode add_reception qui transfère automatiquement vers le concassage
            # au lieu d'ajouter directement un mouvement de stock à la réception
            self.db_manager.add_reception(
                quantity=quantity,
                quality=quality,
                reception_date=reception_date
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()

            # Actualiser les données
            self.refresh_data()
            
            # Message de confirmation
            QMessageBox.information(self, "✅ Succès", 
                f"Réception de {quantity:.1f}T enregistrée avec succès et transférée au concassage")
                
        except ValueError as e:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde réception: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
            
    def import_from_excel(self):
        """Importe des données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "Importer fichier Excel", 
                "", 
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['Quantité', 'Date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "Erreur", 
                    f"Colonnes manquantes: {', '.join(missing_columns)}")
                return
            
            # Informations sur l'import (sans vérification de capacité)
            total_import_quantity = df['Quantité'].sum()
            current_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            new_stock_level = current_stock + total_import_quantity
            
            logging.info(f"Import Excel: Stock actuel={current_stock}T, " +
                         f"Total à importer={total_import_quantity}T, Nouveau niveau={new_stock_level}T")
                
            # Progress bar
            progress = QProgressBar()
            progress.setMaximum(len(df))
            progress.show()
            
            imported_count = 0
            
            for index, row in df.iterrows():
                try:
                    quantity = float(row['Quantité'])
                    date = pd.to_datetime(row['Date']).to_pydatetime()
                    quality = float(row.get('Qualité', 0)) if pd.notna(row.get('Qualité')) else None
                    
                    self.db_manager.add_reception(
                        quantity=quantity,
                        quality=quality,
                        reception_date=date
                    )
                    
                    imported_count += 1
                    progress.setValue(index + 1)
                    
                except Exception as e:
                    logging.warning(f"Erreur ligne {index + 1}: {str(e)}")
                    continue
                    
            progress.close()
            
            # Actualiser les données
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Import Terminé", 
                f"{imported_count} réceptions importées avec succès et transférées au concassage")
                
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
            
    def export_data(self):
        """Exporte les données vers Excel."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter vers Excel",
                f"receptions_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            # Récupérer les données du dernier mois
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            movements = self.db_manager.get_stock_movements(
                step=ProcessStepEnum.CONCASSAGE,
                movement_type=StockMovementType.RECEPTION,
                start_date=start_date,
                end_date=end_date
            )
            
            # Créer DataFrame
            data = []
            for movement in movements:
                data.append({
                    'Date': movement['date'].strftime('%d/%m/%Y %H:%M'),
                    'Quantité (T)': movement['quantity'],
                    'Qualité P2O5 (%)': movement['quality'] if movement['quality'] else '',
                    'Fournisseur': 'SOTRAMINE'  # Simulé
                })
                
            df = pd.DataFrame(data)
            
            # Sauvegarder
            df.to_excel(file_path, index=False, sheet_name='Réceptions')
            
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Données exportées vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")
