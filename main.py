import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

import logging
import sqlite3
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from database.database_manager import DatabaseManager
from ui.main_window import MainWindow
from ui.theme_manager import apply_theme
from utils.cache_manager import clean_expired_cache
from config.dashboard_config import DashboardConfig

def setup_logging():
    """Configure le système de logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log'),
            logging.StreamHandler()
        ]
    )


    # No need to close connection here, it's handled in the calling function

def setup_config_files():
    """Initialise les fichiers de configuration."""
    # S'assurer que le dossier de configuration existe
    os.makedirs("config", exist_ok=True)
    
    # Initialiser la configuration des tableaux de bord
    dashboard_config = DashboardConfig()
    dashboard_config.load_config()
    
    # Nettoyer le cache expiré
    clean_expired_cache()

def main():
    """Point d'entrée principal de l'application."""
    try:
        # Configurer le logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Démarrage de l'application")
        
        # Initialiser les fichiers de configuration
        setup_config_files()
        logger.info("Fichiers de configuration initialisés")
        
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setStyle('Fusion')  # Style moderne et cohérent
        
        # Appliquer le thème
        apply_theme(app)
        logger.info("Thème appliqué")
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.info("Base de données initialisée")
        
        # Créer et afficher la fenêtre principale
        window = MainWindow(db_manager)
        window.show()
        logger.info("Interface graphique affichée")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"Erreur lors du démarrage de l'application: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()