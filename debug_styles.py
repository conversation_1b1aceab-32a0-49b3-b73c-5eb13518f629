import sys
import os
import traceback

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

try:
    print("Importing styles...")
    from ui.styles import PRIMARY_COLOR
    print(f"PRIMARY_COLOR = {PRIMARY_COLOR}")
    print("Styles imported successfully!")
except Exception as e:
    print(f"Error importing styles: {str(e)}")
    traceback.print_exc()