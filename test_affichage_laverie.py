#!/usr/bin/env python3
"""
Test pour vérifier l'affichage dans l'onglet laverie et identifier la source du problème.
"""

import sys
import os
from datetime import datetime, timedelta

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_affichage_laverie():
    """Teste l'affichage des données dans l'onglet laverie."""
    print("🔍 Test de l'affichage dans l'onglet laverie")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Nettoyer et créer des données de test
        session = db_manager.Session()
        try:
            session.execute("DELETE FROM stock_movements")
            session.execute("DELETE FROM productions")
            session.commit()
            print("✅ Base de données nettoyée")
        except Exception as e:
            session.rollback()
            print(f"❌ Erreur nettoyage: {str(e)}")
        finally:
            session.close()
        
        # Créer un scénario de test
        print("\n📊 Création du scénario de test:")
        
        # 1. Réception
        print("1️⃣ Ajout réception: 1000T")
        reception_id = db_manager.add_reception(
            quantity=1000.0,
            quality=28.0,
            reception_date=datetime.now()
        )
        
        # 2. Production concassage
        print("2️⃣ Production concassage: 800T produits, 900T consommés")
        concassage_id = db_manager.add_production(
            step=ProcessStepEnum.CONCASSAGE,
            quantity=800.0,
            quality=28.0,
            quantity_used=900.0,
            production_hours=8.0,
            production_date=datetime.now()
        )
        
        # 3. Production laverie
        print("3️⃣ Production laverie: 600T produits, 700T consommés")
        laverie_id = db_manager.add_production(
            step=ProcessStepEnum.LAVERIE,
            quantity=600.0,
            quality=32.0,
            quantity_used=700.0,
            production_hours=10.0,
            production_date=datetime.now()
        )
        
        # Vérifier les stocks
        print("\n📊 Stocks après productions:")
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f}T")
        
        # Tester ce que voit l'onglet laverie
        print("\n🔍 Données vues par l'onglet laverie:")
        
        # Récupérer les productions de laverie
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        productions = db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_date,
            end_date=end_date
        )
        
        print(f"Nombre de productions laverie trouvées: {len(productions)}")
        
        for i, production in enumerate(productions):
            print(f"\nProduction {i+1}:")
            print(f"  Date: {production['production_date']}")
            print(f"  Quantité produite: {production['quantity']:.2f}T")
            print(f"  Quantité consommée: {production['quantity_used']:.2f}T")
            print(f"  Qualité: {production['quality']}")
            print(f"  Heures: {production['production_hours']:.1f}h")
            print(f"  Étape: {production['step']}")
        
        # Vérifier les mouvements de stock pour la laverie
        print("\n📋 Mouvements de stock pour la laverie:")
        laverie_movements = db_manager.get_stock_movements(ProcessStepEnum.LAVERIE)
        
        print(f"Nombre de mouvements laverie: {len(laverie_movements)}")
        for movement in laverie_movements:
            print(f"  {movement['movement_type']}: {movement['quantity']:+.1f}T")
            print(f"    Date: {movement['date']}")
            if movement.get('source_step'):
                print(f"    Source: {movement['source_step']}")
            if movement.get('destination_step'):
                print(f"    Destination: {movement['destination_step']}")
        
        # Vérifier les mouvements de stock pour le concassage
        print("\n📋 Mouvements de stock pour le concassage:")
        concassage_movements = db_manager.get_stock_movements(ProcessStepEnum.CONCASSAGE)
        
        print(f"Nombre de mouvements concassage: {len(concassage_movements)}")
        for movement in concassage_movements:
            print(f"  {movement['movement_type']}: {movement['quantity']:+.1f}T")
            print(f"    Date: {movement['date']}")
            if movement.get('source_step'):
                print(f"    Source: {movement['source_step']}")
            if movement.get('destination_step'):
                print(f"    Destination: {movement['destination_step']}")
        
        # Vérifier les mouvements de stock pour le stockage final
        print("\n📋 Mouvements de stock pour le stockage final:")
        stockage_movements = db_manager.get_stock_movements(ProcessStepEnum.STOCKAGE_FINAL)
        
        print(f"Nombre de mouvements stockage final: {len(stockage_movements)}")
        for movement in stockage_movements:
            print(f"  {movement['movement_type']}: {movement['quantity']:+.1f}T")
            print(f"    Date: {movement['date']}")
            if movement.get('source_step'):
                print(f"    Source: {movement['source_step']}")
            if movement.get('destination_step'):
                print(f"    Destination: {movement['destination_step']}")
        
        # Analyser le problème potentiel
        print("\n🔍 ANALYSE DU PROBLÈME POTENTIEL:")
        
        # Vérifier si la production concassage apparaît comme entrée laverie
        concassage_production = 800.0
        laverie_consumption = 700.0
        laverie_production = 600.0
        stockage_final = 600.0
        
        print(f"Production concassage: {concassage_production:.1f}T")
        print(f"Consommation laverie: {laverie_consumption:.1f}T")
        print(f"Production laverie: {laverie_production:.1f}T")
        print(f"Stockage final: {stockage_final:.1f}T")
        
        # Le problème pourrait être dans l'affichage
        if concassage_production != laverie_consumption:
            print(f"⚠️  La production concassage ({concassage_production:.1f}T) ne correspond pas")
            print(f"    à la consommation laverie ({laverie_consumption:.1f}T)")
            print(f"    Ceci est normal car il peut y avoir des pertes de process")
        
        if laverie_production == stockage_final:
            print(f"✅ La production laverie ({laverie_production:.1f}T) correspond")
            print(f"    au stockage final ({stockage_final:.1f}T) - Flux correct")
        else:
            print(f"❌ La production laverie ({laverie_production:.1f}T) ne correspond pas")
            print(f"    au stockage final ({stockage_final:.1f}T) - Problème détecté")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale."""
    print("🚀 Test de l'affichage dans l'onglet laverie")
    print("=" * 70)
    
    success = test_affichage_laverie()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ Test terminé - Vérifiez l'analyse ci-dessus")
        return 0
    else:
        print("❌ Erreur lors du test")
        return 1

if __name__ == "__main__":
    sys.exit(main())
