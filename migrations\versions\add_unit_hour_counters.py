"""add_unit_hour_counters

Revision ID: add_unit_hour_counters
Revises: change_quality_column_to_string
Create Date: 2025-06-14

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_unit_hour_counters'
down_revision = 'change_quality_column_to_string'
branch_labels = None
depends_on = None


def upgrade():
    # Créer la table des compteurs horaires des unités
    op.create_table(
        'unit_hour_counters',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('reading_date', sa.DateTime(), nullable=False),
        sa.Column('hours', sa.Float(), nullable=False),
        sa.Column('step_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['step_id'], ['process_steps.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    # Supprimer la table
    op.drop_table('unit_hour_counters')