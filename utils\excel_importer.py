"""
Module pour l'importation des données Excel.
"""
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional

def validate_excel_data(df: pd.DataFrame) -> List[str]:
    """Valide les données du DataFrame et retourne une liste d'erreurs."""
    errors = []
    
    # Vérifier les colonnes requises
    required_columns = ['Date', 'Quantité', 'Qualité']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"Colonnes manquantes: {', '.join(missing_columns)}")
        return errors
    
    # Vérifier chaque ligne
    for idx, row in df.iterrows():
        line_num = idx + 2  # +2 car Excel commence à 1 et il y a l'en-tête
        
        # Vérifier la date
        if pd.isna(row['Date']):
            errors.append(f"Ligne {line_num}: Date manquante")
        else:
            try:
                if isinstance(row['Date'], str):
                    datetime.strptime(row['Date'], "%d/%m/%Y %H:%M")
            except ValueError:
                errors.append(f"Ligne {line_num}: Format de date invalide (utilisez JJ/MM/AAAA HH:mm)")
        
        # Vérifier la quantité
        if pd.isna(row['Quantité']):
            errors.append(f"Ligne {line_num}: Quantité manquante")
        elif not isinstance(row['Quantité'], (int, float)) or row['Quantité'] <= 0:
            errors.append(f"Ligne {line_num}: Quantité invalide ({row['Quantité']})")
        
        # Vérifier la qualité
        if not pd.isna(row['Qualité']):
            try:
                quality = float(row['Qualité'])
                if quality < 0 or quality > 100:
                    errors.append(f"Ligne {line_num}: Qualité doit être entre 0 et 100 ({quality})")
            except ValueError:
                errors.append(f"Ligne {line_num}: Qualité invalide ({row['Qualité']})")
    
    return errors

def read_reception_excel(file_path: str) -> List[Dict[str, Any]]:
    """Lit un fichier Excel contenant des données de réception."""
    try:
        # Lire le fichier Excel
        df = pd.read_excel(file_path)
        
        # Valider les données
        errors = validate_excel_data(df)
        if errors:
            raise ValueError("\n".join(errors))
        
        # Convertir les données en liste de dictionnaires
        receptions = []
        for _, row in df.iterrows():
            # Convertir la date
            date = pd.to_datetime(row['Date']).to_pydatetime() if isinstance(row['Date'], str) else row['Date']
            
            # Convertir la quantité
            quantity = float(row['Quantité'])
            
            # Convertir la qualité
            quality = float(row['Qualité']) if not pd.isna(row['Qualité']) else None
            
            receptions.append({
                'reception_date': date,
                'quantity': quantity,
                'quality': quality
            })
        
        return receptions
        
    except pd.errors.EmptyDataError:
        raise ValueError("Le fichier Excel est vide")
    except pd.errors.ParserError as e:
        raise ValueError(f"Impossible de lire le fichier Excel: {str(e)}")
    except Exception as e:
        if isinstance(e, ValueError):
            raise
        raise Exception(f"Erreur lors de la lecture du fichier Excel: {str(e)}")
