#!/usr/bin/env python3
"""
Test de l'application après nettoyage des répétitions.
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_imports():
    """Teste que tous les imports fonctionnent."""
    print("🔍 Test des imports après nettoyage...")
    
    try:
        # Test des imports principaux
        from database.database_manager import DatabaseManager
        print("✅ DatabaseManager importé")
        
        from ui.main_window import MainWindow
        print("✅ MainWindow importé")
        
        # Test des onglets optimisés
        from ui.tabs.optimized_reception_tab import OptimizedReceptionTab
        from ui.tabs.optimized_crushing_tab import OptimizedCrushingTab
        from ui.tabs.optimized_laverie_tab import OptimizedLaverieTab
        from ui.tabs.optimized_stock_tab import OptimizedStockTab
        from ui.tabs.optimized_reports_tab import OptimizedReportsTab
        print("✅ Tous les onglets optimisés importés")
        
        # Test de la base optimisée
        from ui.tabs.base_optimized import BaseOptimizedTab, QuickInputWidget
        print("✅ Base optimisée importée")
        
        # Test des widgets professionnels
        from ui.widgets.professional_widgets import KPICard, StatusIndicator
        print("✅ Widgets professionnels importés")
        
        # Test des utilitaires
        from utils.cache_manager import cache_manager
        from utils.pdf_generator import PDFGenerator
        from utils.excel_importer import ExcelImporter
        print("✅ Utilitaires importés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """Teste la base de données."""
    print("\n🔍 Test de la base de données...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Test des stocks
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"✅ Stock {step.value}: {stock:.1f}T")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {str(e)}")
        return False

def test_ui_creation():
    """Teste la création des composants UI."""
    print("\n🔍 Test de création des composants UI...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.tabs.optimized_reception_tab import OptimizedReceptionTab
        
        # Créer une application Qt minimale
        app = QApplication(sys.argv)
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Créer un onglet de test
        reception_tab = OptimizedReceptionTab(db_manager)
        print("✅ Onglet réception créé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création UI: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def count_files():
    """Compte les fichiers restants après nettoyage."""
    print("\n📊 Comptage des fichiers après nettoyage...")
    
    file_counts = {
        'onglets': 0,
        'widgets': 0,
        'tests': 0,
        'utils': 0
    }
    
    # Compter les onglets
    tabs_dir = 'ui/tabs'
    if os.path.exists(tabs_dir):
        for file in os.listdir(tabs_dir):
            if file.endswith('.py') and file != '__init__.py':
                file_counts['onglets'] += 1
    
    # Compter les widgets
    widgets_dir = 'ui/widgets'
    if os.path.exists(widgets_dir):
        for file in os.listdir(widgets_dir):
            if file.endswith('.py') and file != '__init__.py':
                file_counts['widgets'] += 1
    
    # Compter les tests
    for file in os.listdir('.'):
        if file.startswith('test_') and file.endswith('.py'):
            file_counts['tests'] += 1
    
    # Compter les utilitaires
    utils_dir = 'utils'
    if os.path.exists(utils_dir):
        for file in os.listdir(utils_dir):
            if file.endswith('.py') and file != '__init__.py':
                file_counts['utils'] += 1
    
    print(f"📁 Onglets restants: {file_counts['onglets']}")
    print(f"📁 Widgets restants: {file_counts['widgets']}")
    print(f"📁 Tests restants: {file_counts['tests']}")
    print(f"📁 Utilitaires restants: {file_counts['utils']}")
    
    return file_counts

def main():
    """Fonction principale de test."""
    print("🚀 Test de l'application après nettoyage des répétitions")
    print("=" * 70)
    
    tests = [
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Création UI", test_ui_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Compter les fichiers
    file_counts = count_files()
    
    # Résumé des résultats
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DU NETTOYAGE")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ OK" if result else "❌ ÉCHEC"
        print(f"{test_name:<20} : {status}")
        if result:
            passed += 1
    
    print(f"\nTests réussis: {passed}/{total}")
    
    # Statistiques de nettoyage
    total_files = sum(file_counts.values())
    print(f"\n📈 Fichiers restants après nettoyage:")
    print(f"  Total: {total_files} fichiers")
    print(f"  Onglets: {file_counts['onglets']} (optimisés uniquement)")
    print(f"  Widgets: {file_counts['widgets']} (professionnels uniquement)")
    print(f"  Tests: {file_counts['tests']} (essentiels uniquement)")
    print(f"  Utilitaires: {file_counts['utils']} (nécessaires uniquement)")
    
    if passed == total:
        print("\n🎉 Application nettoyée avec succès !")
        print("✅ Toutes les répétitions ont été éliminées")
        print("✅ L'application fonctionne correctement")
        return 0
    else:
        print("\n⚠️ Des problèmes persistent après le nettoyage.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
