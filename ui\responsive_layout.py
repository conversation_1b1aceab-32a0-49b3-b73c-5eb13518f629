"""
Module pour gérer l'adaptation de l'interface à différentes tailles d'écran.
"""
from PyQt5.QtWidgets import QWidget, QLayout, QSizePolicy
from PyQt5.QtCore import Qt, QSize, QRect, QPoint
from PyQt5.QtGui import QResizeEvent
from typing import Dict, List, Tuple, Optional, Any
import logging

class ResponsiveLayout(QLayout):
    """
    Layout qui s'adapte automatiquement à la taille de l'écran.
    Permet de positionner les widgets de manière responsive.
    """
    
    # Breakpoints pour les différentes tailles d'écran
    BREAKPOINTS = {
        "xs": 576,  # Extra small (téléphones)
        "sm": 768,  # Small (tablettes)
        "md": 992,  # Medium (petits écrans)
        "lg": 1200  # Large (grands écrans)
    }
    
    def __init__(self, parent: Optional[QWidget] = None, margin: int = 0, spacing: int = -1):
        """
        Initialise le layout responsive.
        
        Args:
            parent: Widget parent
            margin: Marge autour du layout
            spacing: Espacement entre les widgets
        """
        super().__init__(parent)
        
        self.setContentsMargins(margin, margin, margin, margin)
        self.setSpacing(spacing)
        
        self.items: List[Tuple[QWidget, Dict[str, Any]]] = []
        self.current_size = "lg"  # Taille par défaut
    
    def addWidget(self, widget: QWidget, options: Dict[str, Any] = None) -> None:
        """
        Ajoute un widget au layout avec des options de positionnement.
        
        Args:
            widget: Widget à ajouter
            options: Options de positionnement (col, row, colspan, rowspan, visibility)
        """
        if options is None:
            options = {}
        
        # Options par défaut
        default_options = {
            "col": 0,
            "row": 0,
            "colspan": 1,
            "rowspan": 1,
            "visibility": {"xs": True, "sm": True, "md": True, "lg": True}
        }
        
        # Fusionner les options par défaut avec les options fournies
        merged_options = {**default_options, **options}
        
        self.items.append((widget, merged_options))
        self.addChildWidget(widget)
    
    def addItem(self, item: QLayout) -> None:
        """
        Méthode requise par QLayout, mais non utilisée.
        Utilisez addWidget à la place.
        """
        raise NotImplementedError("Utilisez addWidget à la place")
    
    def count(self) -> int:
        """
        Retourne le nombre d'items dans le layout.
        
        Returns:
            Nombre d'items
        """
        return len(self.items)
    
    def itemAt(self, index: int) -> Optional[QLayout]:
        """
        Retourne l'item à l'index spécifié.
        
        Args:
            index: Index de l'item
            
        Returns:
            Item à l'index spécifié ou None si l'index est hors limites
        """
        if 0 <= index < len(self.items):
            return self.items[index][0]
        return None
    
    def takeAt(self, index: int) -> Optional[QLayout]:
        """
        Retire et retourne l'item à l'index spécifié.
        
        Args:
            index: Index de l'item
            
        Returns:
            Item retiré ou None si l'index est hors limites
        """
        if 0 <= index < len(self.items):
            item = self.items.pop(index)
            return item[0]
        return None
    
    def expandingDirections(self) -> Qt.Orientations:
        """
        Retourne les directions dans lesquelles le layout peut s'étendre.
        
        Returns:
            Directions d'expansion
        """
        return Qt.Horizontal | Qt.Vertical
    
    def hasHeightForWidth(self) -> bool:
        """
        Indique si la hauteur dépend de la largeur.
        
        Returns:
            True si la hauteur dépend de la largeur, False sinon
        """
        return True
    
    def heightForWidth(self, width: int) -> int:
        """
        Calcule la hauteur nécessaire pour une largeur donnée.
        
        Args:
            width: Largeur
            
        Returns:
            Hauteur nécessaire
        """
        return self.doLayout(QRect(0, 0, width, 0), True)
    
    def setGeometry(self, rect: QRect) -> None:
        """
        Définit la géométrie du layout.
        
        Args:
            rect: Rectangle définissant la géométrie
        """
        super().setGeometry(rect)
        self.doLayout(rect, False)
    
    def sizeHint(self) -> QSize:
        """
        Retourne la taille suggérée pour le layout.
        
        Returns:
            Taille suggérée
        """
        return QSize(100, 100)
    
    def minimumSize(self) -> QSize:
        """
        Retourne la taille minimale pour le layout.
        
        Returns:
            Taille minimale
        """
        return QSize(0, 0)
    
    def doLayout(self, rect: QRect, test_only: bool = False) -> int:
        """
        Effectue le positionnement des widgets.
        
        Args:
            rect: Rectangle définissant la géométrie
            test_only: Si True, calcule seulement la hauteur sans positionner les widgets
            
        Returns:
            Hauteur totale du layout
        """
        # Déterminer la taille actuelle
        width = rect.width()
        for size, breakpoint in sorted(self.BREAKPOINTS.items(), key=lambda x: x[1]):
            if width <= breakpoint:
                self.current_size = size
                break
        else:
            self.current_size = "lg"
        
        # Calculer le nombre de colonnes en fonction de la taille
        if self.current_size == "xs":
            num_cols = 1
        elif self.current_size == "sm":
            num_cols = 2
        elif self.current_size == "md":
            num_cols = 3
        else:  # lg
            num_cols = 4
        
        # Calculer la largeur d'une colonne
        col_width = rect.width() / num_cols
        
        # Calculer la hauteur de chaque ligne
        row_heights = {}
        max_row = 0
        
        # Première passe : calculer la hauteur de chaque ligne
        for widget, options in self.items:
            # Vérifier si le widget est visible pour cette taille
            if not options["visibility"].get(self.current_size, True):
                widget.hide()
                continue
            
            widget.show()
            
            row = options["row"]
            rowspan = options["rowspan"]
            max_row = max(max_row, row + rowspan - 1)
            
            # Calculer la largeur du widget
            widget_width = col_width * options["colspan"]
            
            # Obtenir la hauteur préférée du widget pour cette largeur
            widget_height = widget.heightForWidth(int(widget_width)) if widget.hasHeightForWidth() else widget.sizeHint().height()
            
            # Mettre à jour la hauteur de la ligne si nécessaire
            for r in range(row, row + rowspan):
                row_heights[r] = max(row_heights.get(r, 0), widget_height / rowspan)
        
        # Calculer la hauteur totale
        total_height = sum(row_heights.values())
        
        if test_only:
            return total_height
        
        # Deuxième passe : positionner les widgets
        for widget, options in self.items:
            # Vérifier si le widget est visible pour cette taille
            if not options["visibility"].get(self.current_size, True):
                continue
            
            col = min(options["col"], num_cols - options["colspan"])
            row = options["row"]
            colspan = options["colspan"]
            rowspan = options["rowspan"]
            
            # Calculer la position et la taille du widget
            x = rect.x() + col * col_width
            y = rect.y() + sum(row_heights.get(r, 0) for r in range(row))
            width = col_width * colspan
            height = sum(row_heights.get(r, 0) for r in range(row, row + rowspan))
            
            # Positionner le widget
            widget.setGeometry(int(x), int(y), int(width), int(height))
        
        return total_height
    
    def update_visibility(self, widget: QWidget, size: str, visible: bool) -> None:
        """
        Met à jour la visibilité d'un widget pour une taille d'écran donnée.
        
        Args:
            widget: Widget à mettre à jour
            size: Taille d'écran (xs, sm, md, lg)
            visible: True pour rendre visible, False pour masquer
        """
        for i, (w, options) in enumerate(self.items):
            if w == widget:
                options["visibility"][size] = visible
                break
        
        self.update()
    
    def update_position(self, widget: QWidget, col: int, row: int, colspan: int = 1, rowspan: int = 1) -> None:
        """
        Met à jour la position d'un widget.
        
        Args:
            widget: Widget à mettre à jour
            col: Colonne
            row: Ligne
            colspan: Nombre de colonnes occupées
            rowspan: Nombre de lignes occupées
        """
        for i, (w, options) in enumerate(self.items):
            if w == widget:
                options["col"] = col
                options["row"] = row
                options["colspan"] = colspan
                options["rowspan"] = rowspan
                break
        
        self.update()

class ResponsiveWidget(QWidget):
    """Widget qui s'adapte automatiquement à la taille de l'écran."""
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        Initialise le widget responsive.
        
        Args:
            parent: Widget parent
        """
        super().__init__(parent)
        
        self.responsive_layout = ResponsiveLayout(self)
        self.setLayout(self.responsive_layout)
        
        # Taille actuelle de l'écran
        self.current_size = "lg"
    
    def resizeEvent(self, event: QResizeEvent) -> None:
        """
        Gère l'événement de redimensionnement.
        
        Args:
            event: Événement de redimensionnement
        """
        super().resizeEvent(event)
        
        # Déterminer la nouvelle taille
        width = event.size().width()
        new_size = "lg"
        
        for size, breakpoint in sorted(ResponsiveLayout.BREAKPOINTS.items(), key=lambda x: x[1]):
            if width <= breakpoint:
                new_size = size
                break
        
        # Si la taille a changé, émettre un signal
        if new_size != self.current_size:
            self.current_size = new_size
            self.on_size_changed(new_size)
    
    def on_size_changed(self, size: str) -> None:
        """
        Méthode appelée lorsque la taille de l'écran change.
        À surcharger dans les classes dérivées.
        
        Args:
            size: Nouvelle taille (xs, sm, md, lg)
        """
        pass
    
    def add_widget(self, widget: QWidget, col: int, row: int, colspan: int = 1, rowspan: int = 1,
                  visibility: Dict[str, bool] = None) -> None:
        """
        Ajoute un widget au layout.
        
        Args:
            widget: Widget à ajouter
            col: Colonne
            row: Ligne
            colspan: Nombre de colonnes occupées
            rowspan: Nombre de lignes occupées
            visibility: Visibilité par taille d'écran
        """
        if visibility is None:
            visibility = {"xs": True, "sm": True, "md": True, "lg": True}
        
        options = {
            "col": col,
            "row": row,
            "colspan": colspan,
            "rowspan": rowspan,
            "visibility": visibility
        }
        
        self.responsive_layout.addWidget(widget, options)
    
    def update_widget_visibility(self, widget: QWidget, size: str, visible: bool) -> None:
        """
        Met à jour la visibilité d'un widget pour une taille d'écran donnée.
        
        Args:
            widget: Widget à mettre à jour
            size: Taille d'écran (xs, sm, md, lg)
            visible: True pour rendre visible, False pour masquer
        """
        self.responsive_layout.update_visibility(widget, size, visible)
    
    def update_widget_position(self, widget: QWidget, col: int, row: int, colspan: int = 1, rowspan: int = 1) -> None:
        """
        Met à jour la position d'un widget.
        
        Args:
            widget: Widget à mettre à jour
            col: Colonne
            row: Ligne
            colspan: Nombre de colonnes occupées
            rowspan: Nombre de lignes occupées
        """
        self.responsive_layout.update_position(widget, col, row, colspan, rowspan)
    
    def get_current_size(self) -> str:
        """
        Retourne la taille actuelle de l'écran.
        
        Returns:
            Taille actuelle (xs, sm, md, lg)
        """
        return self.current_size