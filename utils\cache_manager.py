"""
Gestionnaire de cache pour optimiser les requêtes fréquentes.
"""
import time
import logging
from typing import Dict, Any, Callable, Optional, Tuple
from functools import wraps
from datetime import datetime, timedelta

class CacheManager:
    """Gestionnaire de cache pour stocker les résultats des requêtes fréquentes."""
    
    def __init__(self, default_ttl: int = 300):
        """
        Initialise le gestionnaire de cache.
        
        Args:
            default_ttl: Durée de vie par défaut des entrées en secondes (5 minutes par défaut)
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self.stats = {
            "hits": 0,
            "misses": 0,
            "entries": 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """
        Récupère une valeur du cache.
        
        Args:
            key: Clé de l'entrée à récupérer
            
        Returns:
            La valeur associée à la clé ou None si la clé n'existe pas ou est expirée
        """
        if key in self.cache:
            entry = self.cache[key]
            # Vérifier si l'entrée est expirée
            if entry["expiry"] > time.time():
                self.stats["hits"] += 1
                return entry["value"]
            else:
                # Supprimer l'entrée expirée
                del self.cache[key]
        
        self.stats["misses"] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Ajoute ou met à jour une entrée dans le cache.
        
        Args:
            key: Clé de l'entrée
            value: Valeur à stocker
            ttl: Durée de vie en secondes (utilise la valeur par défaut si None)
        """
        if ttl is None:
            ttl = self.default_ttl
        
        self.cache[key] = {
            "value": value,
            "expiry": time.time() + ttl
        }
        
        # Mettre à jour les statistiques
        if key not in self.cache:
            self.stats["entries"] += 1
    
    def delete(self, key: str) -> bool:
        """
        Supprime une entrée du cache.
        
        Args:
            key: Clé de l'entrée à supprimer
            
        Returns:
            True si l'entrée a été supprimée, False sinon
        """
        if key in self.cache:
            del self.cache[key]
            self.stats["entries"] -= 1
            return True
        return False
    
    def clear(self) -> None:
        """Vide le cache."""
        self.cache.clear()
        self.stats["entries"] = 0
    
    def clean_expired(self) -> int:
        """
        Supprime les entrées expirées du cache.
        
        Returns:
            Nombre d'entrées supprimées
        """
        now = time.time()
        expired_keys = [k for k, v in self.cache.items() if v["expiry"] <= now]
        
        for key in expired_keys:
            del self.cache[key]
        
        self.stats["entries"] -= len(expired_keys)
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Récupère les statistiques du cache.
        
        Returns:
            Dictionnaire contenant les statistiques du cache
        """
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_ratio = self.stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "entries": self.stats["entries"],
            "hit_ratio": hit_ratio,
            "size": len(self.cache)
        }
    
    def cached(self, ttl: Optional[int] = None):
        """
        Décorateur pour mettre en cache le résultat d'une fonction.
        
        Args:
            ttl: Durée de vie en secondes (utilise la valeur par défaut si None)
            
        Returns:
            Décorateur qui met en cache le résultat de la fonction
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Générer une clé unique basée sur la fonction et ses arguments
                key_parts = [func.__name__]
                key_parts.extend([str(arg) for arg in args])
                key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
                cache_key = ":".join(key_parts)
                
                # Vérifier si le résultat est dans le cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Exécuter la fonction et mettre en cache le résultat
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                
                return result
            return wrapper
        return decorator

# Instance globale du gestionnaire de cache
cache_manager = CacheManager()

def cached(ttl: Optional[int] = None):
    """
    Décorateur pour mettre en cache le résultat d'une fonction.
    
    Args:
        ttl: Durée de vie en secondes (utilise la valeur par défaut si None)
        
    Returns:
        Décorateur qui met en cache le résultat de la fonction
    """
    return cache_manager.cached(ttl)

def invalidate_cache_for(prefix: str) -> int:
    """
    Invalide toutes les entrées du cache commençant par un préfixe.
    
    Args:
        prefix: Préfixe des clés à invalider
        
    Returns:
        Nombre d'entrées invalidées
    """
    keys_to_delete = [k for k in cache_manager.cache.keys() if k.startswith(prefix)]
    for key in keys_to_delete:
        cache_manager.delete(key)
    return len(keys_to_delete)

def get_cache_stats() -> Dict[str, Any]:
    """
    Récupère les statistiques du cache.
    
    Returns:
        Dictionnaire contenant les statistiques du cache
    """
    return cache_manager.get_stats()

def clear_cache() -> None:
    """Vide le cache."""
    cache_manager.clear()

def clean_expired_cache() -> int:
    """
    Supprime les entrées expirées du cache.
    
    Returns:
        Nombre d'entrées supprimées
    """
    return cache_manager.clean_expired()