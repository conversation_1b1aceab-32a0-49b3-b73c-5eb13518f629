from datetime import datetime
from typing import Union, Optional, List, Dict, Any
from .exceptions import ValidationError
from .constants import MIN_QUALITY, MAX_QUALITY, MIN_QUANTITY, MAX_QUANTITY

def validate_quantity(quantity: Union[int, float], 
                     min_value: float = MIN_QUANTITY,
                     max_value: float = MAX_QUANTITY) -> None:
    """Valide une quantité."""
    if not isinstance(quantity, (int, float)):
        raise ValidationError("La quantité doit être un nombre.")
    
    if quantity < min_value or quantity > max_value:
        raise ValidationError(f"La quantité doit être comprise entre {min_value} et {max_value}.")

def validate_quality(quality: Union[int, float],
                    min_value: float = MIN_QUALITY,
                    max_value: float = MAX_QUALITY) -> None:
    """Valide une qualité."""
    if not isinstance(quality, (int, float)):
        raise ValidationError("La qualité doit être un nombre.")
    
    if quality < min_value or quality > max_value:
        raise ValidationError(f"La qualité doit être comprise entre {min_value} et {max_value}.")

def validate_date(date: Union[str, datetime], format: str = "%d/%m/%Y") -> None:
    """Valide une date."""
    if isinstance(date, str):
        try:
            datetime.strptime(date, format)
        except ValueError:
            raise ValidationError(f"La date doit être au format {format}.")
    elif not isinstance(date, datetime):
        raise ValidationError("La date doit être une chaîne ou un objet datetime.")

def validate_time(time: Union[str, datetime], format: str = "%H:%M") -> None:
    """Valide une heure."""
    if isinstance(time, str):
        try:
            datetime.strptime(time, format)
        except ValueError:
            raise ValidationError(f"L'heure doit être au format {format}.")
    elif not isinstance(time, datetime):
        raise ValidationError("L'heure doit être une chaîne ou un objet datetime.")

def validate_duration(duration: Union[int, float]) -> None:
    """Valide une durée."""
    if not isinstance(duration, (int, float)):
        raise ValidationError("La durée doit être un nombre.")
    
    if duration <= 0:
        raise ValidationError("La durée doit être positive.")

def validate_file_extension(filename: str, allowed_extensions: List[str]) -> None:
    """Valide l'extension d'un fichier."""
    if not filename:
        raise ValidationError("Le nom du fichier ne peut pas être vide.")
    
    if not any(filename.lower().endswith(ext) for ext in allowed_extensions):
        raise ValidationError(f"Le fichier doit avoir une extension valide ({', '.join(allowed_extensions)}).")

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """Valide la présence des champs requis."""
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        raise ValidationError(f"Les champs suivants sont requis : {', '.join(missing_fields)}.")

def validate_numeric_fields(data: Dict[str, Any], numeric_fields: List[str]) -> None:
    """Valide que les champs sont numériques."""
    non_numeric_fields = [
        field for field in numeric_fields
        if field in data and not isinstance(data[field], (int, float))
    ]
    if non_numeric_fields:
        raise ValidationError(f"Les champs suivants doivent être numériques : {', '.join(non_numeric_fields)}.")

def validate_date_range(start_date: datetime, end_date: datetime) -> None:
    """Valide une plage de dates."""
    if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
        raise ValidationError("Les dates doivent être des objets datetime.")
    
    if start_date > end_date:
        raise ValidationError("La date de début doit être antérieure à la date de fin.")

def validate_production_data(data: Dict[str, Any]) -> None:
    """Valide les données de production."""
    required_fields = ["quantity", "quality", "production_hours"]
    validate_required_fields(data, required_fields)
    
    numeric_fields = ["quantity", "quality", "production_hours"]
    validate_numeric_fields(data, numeric_fields)
    
    validate_quantity(data["quantity"])
    validate_quality(data["quality"])
    validate_duration(data["production_hours"])

def validate_downtime_data(data: Dict[str, Any]) -> None:
    """Valide les données d'arrêt."""
    required_fields = ["reason", "duration"]
    validate_required_fields(data, required_fields)
    
    numeric_fields = ["duration"]
    validate_numeric_fields(data, numeric_fields)
    
    validate_duration(data["duration"])

def validate_stock_movement_data(data: Dict[str, Any]) -> None:
    """Valide les données de mouvement de stock."""
    required_fields = ["quantity", "movement_type"]
    validate_required_fields(data, required_fields)
    
    numeric_fields = ["quantity"]
    validate_numeric_fields(data, numeric_fields)
    
    validate_quantity(data["quantity"])
    
    if data["movement_type"] not in ["RECEPTION", "PRODUCTION", "CONSUMPTION"]:
        raise ValidationError("Le type de mouvement doit être RECEPTION, PRODUCTION ou CONSUMPTION.") 