# Standard library imports
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging

# Third-party imports
from sqlalchemy import create_engine, func, case, and_, or_
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

# Local imports
from models.database_models import Base, ProcessStep, Production, Downtime, StockMovement, ResourceConsumption, UnitHourCounter
from models.enums import ProcessStepEnum, ResourceType, StockMovementType, QualityGrade
from core.interfaces.database_interface import DatabaseInterface
from utils.cache_manager import cache_manager, invalidate_cache_for

class DatabaseManager(DatabaseInterface):
    """Gestionnaire de base de données utilisant SQLAlchemy."""
    
    def __init__(self, db_url: str = "sqlite:///phosphate.db"):
        """Initialise le gestionnaire de base de données."""
        self.engine = create_engine(db_url)
        self.Session = sessionmaker(bind=self.engine)
    
    def initialize_database(self) -> None:
        """Initialise la base de données avec les tables nécessaires et les relations."""
        # Créer toutes les tables définies dans Base
        Base.metadata.create_all(self.engine)
        
        # Initialiser les process steps une fois les tables créées
        self._initialize_process_steps()
        
        # Créer les relations de mouvement de stock si nécessaire et si elle n'existe pas
        try:
            with self._get_session() as session:
                # Effectuer une requête simple pour vérifier si la table mouvement de stock existe
                session.execute("SELECT COUNT(*) FROM stock_movements LIMIT 1")
        except SQLAlchemyError as e:
            # Si la table n'existe pas, on la crée avec les relations
            logging.error(f"Table stock_movements n'existe pas encore: {str(e)}")
            # Créer la table avec les relations
            session = self.Session()
            try:
                session.execute("CREATE TABLE stock_movements (id INTEGER PRIMARY KEY, step_id INTEGER, quantity REAL, movement_type TEXT, movement_date DATETIME, reference_id INTEGER, quality REAL, source_step_id INTEGER, destination_step_id INTEGER, FOREIGN KEY(source_step_id) REFERENCES process_steps(id), FOREIGN KEY(destination_step_id) REFERENCES process_steps(id))")
                session.commit()
                logging.info("Table stock_movements avec relations créée manuellement")
            except Exception as inner_e:
                session.rollback()
                logging.error(f"Erreur lors de la création manuelle de stock_movements: {str(inner_e)}")
            finally:
                session.close()
                
        # Vérifier que toutes les étapes du processus sont bien initialisées
        with self._get_session() as session:
            for step in ProcessStepEnum:
                if not session.query(ProcessStep).filter_by(name=step.value).first():
                    process_step = ProcessStep(name=step.value)
                    session.add(process_step)
                    logging.info(f"Étape {step.value} ajoutée à la base de données")
            session.commit()
    
    def _initialize_process_steps(self) -> None:
        """Initialise les étapes du processus si elles n'existent pas."""
        with self._get_session() as session:
            for step in ProcessStepEnum:
                if not session.query(ProcessStep).filter_by(name=step.value).first():
                    process_step = ProcessStep(name=step.value)
                    session.add(process_step)
            session.commit()
    
    def _get_session(self):
        """Context manager pour la gestion automatique des sessions."""
        class SessionManager:
            def __init__(self, Session):
                self.Session = Session
                self.session = None
            
            def __enter__(self):
                self.session = self.Session()
                return self.session
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                if exc_type:
                    self.session.rollback()
                    logging.error(f"Erreur SQL: {exc_val}")
                    raise Exception(f"Erreur lors de l'opération: {str(exc_val)}")
                self.session.close()
        
        return SessionManager(self.Session)
    
    def _get_process_step(self, session: Session, step: ProcessStepEnum) -> ProcessStep:
        """Récupère une étape du processus."""
        process_step = session.query(ProcessStep).filter_by(name=step.value).first()
        if not process_step:
            raise ValueError(f"Étape '{step.value}' non trouvée")
        return process_step
        
    def _convert_quality_to_numeric(self, quality_value) -> Optional[float]:
        """Convertit une valeur de qualité (chaîne ou nombre) en valeur numérique.
        
        Args:
            quality_value: Valeur de qualité à convertir (chaîne, float, int ou None)
            
        Returns:
            Valeur numérique de la qualité ou None si impossible à convertir
        """
        if quality_value is None:
            return None
        
        try:
            # Si c'est une chaîne, essayer de la convertir
            if isinstance(quality_value, str):
                # Vérifier si c'est un nombre
                if quality_value.replace('.', '', 1).isdigit():
                    return float(quality_value)
                # Sinon, c'est probablement une énumération
                else:
                    quality_mapping = {
                        QualityGrade.HIGH.value: 90,
                        QualityGrade.MEDIUM.value: 70,
                        QualityGrade.LOW.value: 50
                    }
                    return quality_mapping.get(quality_value, None)
            # Si c'est déjà un nombre
            elif isinstance(quality_value, (int, float)):
                return float(quality_value)
            return None
        except (ValueError, TypeError):
            logging.warning(f"Impossible de convertir la qualité '{quality_value}' en valeur numérique")
            return None
    
    def add_production(self, step: ProcessStepEnum, quantity: float, quality: Optional[float] = None,
                      quantity_used: Optional[float] = None, production_hours: Optional[float] = None,
                      production_date: Optional[datetime] = None) -> int:
        """Ajoute une nouvelle production selon le flux: RECEPTION → CONCASSAGE → LAVERIE → STOCKAGE_FINAL."""
        session = self.Session()
        try:
            # Récupérer l'étape
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
                
            # Gérer la qualité différemment selon l'étape
            quality_value = None
            if quality is not None:
                if step in [ProcessStepEnum.LAVERIE, ProcessStepEnum.CONCASSAGE]:
                    # Pour la laverie et le concassage, la qualité est un pourcentage P2O5 (float)
                    # Nettoyer la valeur si elle contient un symbole '%'
                    if isinstance(quality, str) and '%' in quality:
                        quality = quality.replace('%', '').strip()
                    # Convertir en float pour la base de données
                    try:
                        quality_value = float(quality)
                    except (ValueError, TypeError):
                        logging.warning(f"Impossible de convertir la qualité '{quality}' en nombre")
                        quality_value = None
                else:
                    # Pour les autres étapes, la qualité est une énumération
                    try:
                        # Si c'est un nombre, le convertir en grade de qualité
                        if isinstance(quality, (int, float)):
                            # Convertir le nombre en grade selon une échelle
                            if quality >= 80:
                                quality_value = QualityGrade.HIGH.value
                            elif quality >= 50:
                                quality_value = QualityGrade.MEDIUM.value
                            else:
                                quality_value = QualityGrade.LOW.value
                        # Si c'est une chaîne, vérifier si c'est un grade valide
                        elif isinstance(quality, str):
                            # Vérifier si c'est une valeur d'énumération valide
                            if quality in [q.value for q in QualityGrade]:
                                quality_value = quality
                            # Vérifier si c'est un nombre sous forme de chaîne
                            elif quality.replace('.', '', 1).isdigit():
                                # Convertir en float puis en grade
                                quality_float = float(quality)
                                if quality_float >= 80:
                                    quality_value = QualityGrade.HIGH.value
                                elif quality_float >= 50:
                                    quality_value = QualityGrade.MEDIUM.value
                                else:
                                    quality_value = QualityGrade.LOW.value
                            else:
                                quality_value = QualityGrade.MEDIUM.value
                        else:
                            quality_value = QualityGrade.MEDIUM.value
                    except (ValueError, TypeError):
                        # Valeur par défaut en cas d'erreur
                        quality_value = QualityGrade.MEDIUM.value
                        logging.warning(f"Qualité invalide: {quality}, utilisation de la valeur par défaut: {quality_value}")

            # Créer la production
            production = Production(
                step_id=process_step.id,
                quantity=quantity,
                quality=quality_value,
                quantity_used=quantity_used,
                production_hours=production_hours,
                production_date=production_date or datetime.utcnow()
            )
            session.add(production)

            # Gérer les mouvements de stock selon l'étape du processus
            if step == ProcessStepEnum.CONCASSAGE:
                # Le concassage consomme de la matière de la réception
                if quantity_used:
                    reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
                    if reception_step:
                        # Consommation de la matière de la réception
                        stock_movement = StockMovement(
                            step_id=reception_step.id,  # La consommation est imputée à la réception
                            quantity=-quantity_used,
                            movement_type=StockMovementType.CONSUMPTION,
                            reference_id=production.id,
                            source_step_id=reception_step.id,
                            destination_step_id=process_step.id  # Destination = concassage
                        )
                        session.add(stock_movement)
                        logging.info(f"Mouvement de consommation créé: {quantity_used} tonnes de la réception vers le concassage")
                
                # Production du concassage (disponible pour la laverie)
                stock_movement_produced = StockMovement(
                    step_id=process_step.id,
                    quantity=quantity,
                    movement_type=StockMovementType.PRODUCTION,
                    reference_id=production.id,
                    source_step_id=process_step.id,
                    destination_step_id=process_step.id  # Reste dans le concassage jusqu'à utilisation par la laverie
                )
                session.add(stock_movement_produced)
                logging.info(f"Mouvement de production créé: {quantity} tonnes pour {step.value}")
                
            elif step == ProcessStepEnum.LAVERIE:
                # La laverie consomme de la matière du concassage
                if quantity_used:
                    concassage_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.CONCASSAGE.value).first()
                    if concassage_step:
                        # Consommation de la matière du concassage
                        stock_movement = StockMovement(
                            step_id=concassage_step.id,  # La consommation est imputée au concassage
                            quantity=-quantity_used,
                            movement_type=StockMovementType.CONSUMPTION,
                            reference_id=production.id,
                            source_step_id=concassage_step.id,
                            destination_step_id=process_step.id  # Destination = laverie
                        )
                        session.add(stock_movement)
                        logging.info(f"Mouvement de consommation créé: {quantity_used} tonnes du concassage vers la laverie")
                
                # La laverie transfère directement au stockage final (pas de stock intermédiaire)
                stockage_final_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.STOCKAGE_FINAL.value).first()
                if stockage_final_step:
                    # Transfert direct vers le stockage final (la laverie ne stocke pas)
                    stock_movement_transfer = StockMovement(
                        step_id=stockage_final_step.id,
                        quantity=quantity,
                        movement_type=StockMovementType.TRANSFER,
                        reference_id=production.id,
                        source_step_id=process_step.id,  # Source = laverie
                        destination_step_id=stockage_final_step.id,  # Destination = stockage final
                        quality=quality_value  # Conserver la qualité P2O5
                    )
                    session.add(stock_movement_transfer)
                    logging.info(f"Transfert direct créé: {quantity} tonnes de la laverie vers le stockage final")
            else:
                # Pour les autres étapes, comportement standard
                if quantity_used:
                    # Consommation interne
                    stock_movement = StockMovement(
                        step_id=process_step.id,
                        quantity=-quantity_used,
                        movement_type=StockMovementType.CONSUMPTION,
                        reference_id=production.id,
                        source_step_id=process_step.id,
                        destination_step_id=None  # Consommation interne
                    )
                    session.add(stock_movement)
                    logging.info(f"Mouvement de consommation interne créé: {quantity_used} tonnes pour {step.value}")

                # Production standard
                stock_movement_produced = StockMovement(
                    step_id=process_step.id,
                    quantity=quantity,
                    movement_type=StockMovementType.PRODUCTION,
                    reference_id=production.id,
                    source_step_id=process_step.id,
                    destination_step_id=process_step.id
                )
                session.add(stock_movement_produced)
                logging.info(f"Mouvement de production créé: {quantity} tonnes pour {step.value}")
            
            # Ajouter automatiquement un compteur horaire si des heures de production sont spécifiées
            if production_hours and production_hours > 0:
                # Créer le relevé de compteur horaire
                hour_counter = UnitHourCounter(
                    hours=production_hours,
                    reading_date=production_date or datetime.utcnow(),
                    step_id=process_step.id
                )
                session.add(hour_counter)
                logging.info(f"Compteur horaire ajouté automatiquement pour {step.value}: {production_hours} heures")

            session.commit()

            # Invalider le cache des stocks après modification
            invalidate_cache_for("stock_")

            return production.id

        except SQLAlchemyError as e:
            session.rollback()
            logging.error(f"Erreur SQL lors de l'ajout de la production: {str(e)}")
            raise Exception(f"Erreur lors de l'ajout de la production: {str(e)}")
        except Exception as e:
            session.rollback()
            logging.error(f"Erreur lors de l'ajout de la production: {str(e)}")
            raise
        finally:
            session.close()




    
    def add_downtime(self, step: ProcessStepEnum, reason: str, duration: float,
                    downtime_date: Optional[datetime] = None, description: Optional[str] = None) -> int:
        """Ajoute un nouvel arrêt de production.
        
        Args:
            step: Étape du processus concernée par l'arrêt
            reason: Raison de l'arrêt
            duration: Durée de l'arrêt en minutes
            downtime_date: Date de l'arrêt (par défaut: maintenant)
            description: Description détaillée de l'arrêt
            
        Returns:
            ID de l'arrêt créé
        """
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            downtime = Downtime(
                step_id=process_step.id,
                reason=reason,
                duration=duration,
                downtime_date=downtime_date or datetime.utcnow(),
                description=description or ""  # Ensure description is never None
            )
            session.add(downtime)
            session.commit()
            return downtime.id
            
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de l'ajout de l'arrêt: {str(e)}")
        finally:
            session.close()
    
    def add_stock_movement(self, step: ProcessStepEnum, quantity: float,
                          movement_type: StockMovementType, reference_id: Optional[int] = None,
                          movement_date: Optional[datetime] = None, quality: Optional[float] = None,
                          source_step: Optional[ProcessStepEnum] = None, 
                          destination_step: Optional[ProcessStepEnum] = None) -> int:
        """Ajoute un nouveau mouvement de stock.
        
        Args:
            step: Étape du processus concernée par le mouvement
            quantity: Quantité (positive pour entrée, négative pour sortie)
            movement_type: Type de mouvement (RECEPTION, PRODUCTION, CONSUMPTION, TRANSFER)
            reference_id: ID de référence (ex: ID de production associée)
            movement_date: Date du mouvement (par défaut: maintenant)
            quality: Qualité du stock (pourcentage P2O5 ou grade)
            source_step: Étape source du mouvement
            destination_step: Étape destination du mouvement
            
        Returns:
            ID du mouvement créé
        """
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            # Récupérer les étapes source et destination
            source_step_id = None
            if source_step:
                source_process_step = session.query(ProcessStep).filter_by(name=source_step.value).first()
                if source_process_step:
                    source_step_id = source_process_step.id
                    
            destination_step_id = None
            if destination_step:
                destination_process_step = session.query(ProcessStep).filter_by(name=destination_step.value).first()
                if destination_process_step:
                    destination_step_id = destination_process_step.id
            
            movement = StockMovement(
                step_id=process_step.id,
                quantity=quantity,
                movement_type=movement_type,
                reference_id=reference_id,
                movement_date=movement_date or datetime.utcnow(),
                quality=quality,
                source_step_id=source_step_id,
                destination_step_id=destination_step_id
            )
            session.add(movement)
            session.commit()
            return movement.id
            
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de l'ajout du mouvement de stock: {str(e)}")
        finally:
            session.close()
    
    def add_resource_consumption(self, step: ProcessStepEnum, resource_type: ResourceType,
                               quantity: float, consumption_date: Optional[datetime] = None) -> int:
        """Ajoute une nouvelle consommation de ressource.
        
        Args:
            step: Étape du processus concernée par la consommation
            resource_type: Type de ressource consommée
            quantity: Quantité consommée
            consumption_date: Date de la consommation (par défaut: maintenant)
            
        Returns:
            ID de la consommation créée
        """
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            consumption = ResourceConsumption(
                step_id=process_step.id,
                resource_type=resource_type,
                quantity=quantity,
                consumption_date=consumption_date or datetime.utcnow()
            )
            session.add(consumption)
            session.commit()
            return consumption.id
            
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de l'ajout de la consommation: {str(e)}")
        finally:
            session.close()
            
    def get_resource_consumptions(self, resource_type: Optional[ResourceType] = None,
                                start_date: Optional[datetime] = None,
                                end_date: Optional[datetime] = None,
                                step: Optional[ProcessStepEnum] = None) -> List[Dict[str, Any]]:
        """Récupère les consommations de ressources."""
        session = self.Session()
        try:
            query = session.query(ResourceConsumption).outerjoin(ProcessStep)
            
            if resource_type:
                query = query.filter(ResourceConsumption.resource_type == resource_type)
            if start_date:
                query = query.filter(ResourceConsumption.consumption_date >= start_date)
            if end_date:
                query = query.filter(ResourceConsumption.consumption_date <= end_date)
            if step:
                process_step = session.query(ProcessStep).filter_by(name=step.value).first()
                if not process_step:
                    raise ValueError(f"Étape '{step.value}' non trouvée")
                query = query.filter(ResourceConsumption.step_id == process_step.id)
            
            consumptions = query.order_by(ResourceConsumption.consumption_date.desc()).all()
            
            # Convertir les objets ResourceConsumption en dictionnaires
            result = []
            for consumption in consumptions:
                result.append({
                    'id': consumption.id,
                    'resource_type': consumption.resource_type.value,
                    'quantity': consumption.quantity,
                    'consumption_date': consumption.consumption_date,
                    'step_id': consumption.step_id,
                    'step': consumption.step.name if consumption.step else None
                })
            
            return result
            
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des consommations: {str(e)}")
        finally:
            session.close()
    
    def get_production_summary(self, step: Optional[ProcessStepEnum] = None,
                             start_date: Optional[datetime] = None,
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des productions."""
        session = self.Session()
        try:
            query = session.query(Production).join(ProcessStep)
            
            if step:
                query = query.filter(ProcessStep.name == step.value)
            if start_date:
                query = query.filter(Production.production_date >= start_date)
            if end_date:
                query = query.filter(Production.production_date <= end_date)
            
            productions = query.all()
            
            # Convertir les objets Production en dictionnaires
            result = []
            for production in productions:
                result.append({
                    'id': production.id,
                    'quantity': production.quantity,
                    'quality': production.quality,  # Maintenant c'est une chaîne de caractères
                    'quantity_used': production.quantity_used,
                    'production_hours': production.production_hours,
                    'production_date': production.production_date,
                    'step_id': production.step_id,
                    'step': production.step.name
                })
            
            return result
            
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des productions: {str(e)}")
        finally:
            session.close()
    
    def get_downtime_summary(self, step: Optional[ProcessStepEnum] = None,
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des arrêts."""
        session = self.Session()
        try:
            query = session.query(Downtime).join(ProcessStep)
            
            if step:
                query = query.filter(ProcessStep.name == step.value)
            if start_date:
                query = query.filter(Downtime.downtime_date >= start_date)
            if end_date:
                query = query.filter(Downtime.downtime_date <= end_date)
            
            downtimes = query.all()
            
            # Convertir les objets Downtime en dictionnaires
            result = []
            for downtime in downtimes:
                result.append({
                    'id': downtime.id,
                    'reason': downtime.reason,
                    'duration': downtime.duration,
                    'downtime_date': downtime.downtime_date,
                    'description': downtime.description,
                    'step_id': downtime.step_id,
                    'step': downtime.step.name
                })
            
            return result
            
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des arrêts: {str(e)}")
        finally:
            session.close()


    def get_stock_with_quality(self, step: ProcessStepEnum) -> Dict[str, Any]:
        """Récupère le stock actuel avec la qualité moyenne pour une étape donnée."""
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            # Récupérer le stock total
            current_stock = self.get_current_stock(step)
            
            # Calculer la qualité moyenne pondérée pour les stocks actuels
            # On regarde les mouvements entrants avec qualité non nulle
            movements_query = session.query(StockMovement).filter(
                StockMovement.step_id == process_step.id,
                StockMovement.movement_type.in_([StockMovementType.RECEPTION, StockMovementType.TRANSFER, StockMovementType.PRODUCTION]),
                StockMovement.quality.isnot(None),
                StockMovement.quality > 0
            ).order_by(StockMovement.movement_date.desc()).limit(10)  # Les 10 derniers mouvements avec qualité
            
            movements = movements_query.all()
            
            total_weighted_quality = 0.0
            total_quantity = 0.0
            
            for movement in movements:
                if movement.quality is not None and movement.quantity > 0:
                    quality_value = self._convert_quality_to_numeric(movement.quality)
                    if quality_value is not None:
                        total_weighted_quality += quality_value * movement.quantity
                        total_quantity += movement.quantity
            
            average_quality = total_weighted_quality / total_quantity if total_quantity > 0 else None
            
            return {
                'stock': current_stock,
                'quality': average_quality,
                'has_quality_data': average_quality is not None
            }
            
        except Exception as e:
            logging.error(f"Erreur lors de la récupération du stock avec qualité: {str(e)}")
            return {
                'stock': 0.0,
                'quality': None,
                'has_quality_data': False
            }
        finally:
            session.close()
    
    def get_stock_movements(self, step: Optional[ProcessStepEnum] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           movement_type: Optional[object] = None,
                           limit: Optional[int] = None,
                           **kwargs) -> List[Dict[str, Any]]:
        """Récupère l'historique des mouvements de stock.
        
        Args:
            step: Filtrer par étape (optionnel)
            start_date: Date de début (optionnel)
            end_date: Date de fin (optionnel)
            movement_type: Type de mouvement (optionnel)
            limit: Nombre maximum de résultats (optionnel)
            
        Returns:
            Liste des mouvements de stock sous forme de dictionnaires
        """
        session = self.Session()
        try:
            query = session.query(StockMovement).join(ProcessStep, StockMovement.step_id == ProcessStep.id)
            
            if step:
                query = query.filter(ProcessStep.name == step.value)
            if start_date:
                query = query.filter(StockMovement.movement_date >= start_date)
            if end_date:
                query = query.filter(StockMovement.movement_date <= end_date)
            if movement_type:
                query = query.filter(StockMovement.movement_type == movement_type)
            
            # Trier par date décroissante (plus récent d'abord)
            query = query.order_by(StockMovement.movement_date.desc())
            
            # Limiter le nombre de résultats si demandé
            if limit:
                query = query.limit(limit)
            
            movements = query.all()
            
            # Convertir les objets StockMovement en dictionnaires
            result = []
            for movement in movements:
                # Récupérer les noms des étapes source et destination si disponibles
                source_step_name = None
                if movement.source_step_id:
                    source_step = session.query(ProcessStep).get(movement.source_step_id)
                    if source_step:
                        source_step_name = source_step.name
                
                destination_step_name = None
                if movement.destination_step_id:
                    destination_step = session.query(ProcessStep).get(movement.destination_step_id)
                    if destination_step:
                        destination_step_name = destination_step.name
                
                result.append({
                    'id': movement.id,
                    'step_id': movement.step_id,
                    'quantity': movement.quantity,
                    'quality': movement.quality,
                    'movement_type': movement.movement_type.value if hasattr(movement.movement_type, 'value') else movement.movement_type,
                    'date': movement.movement_date,
                    'reference_id': movement.reference_id,
                    'source_step_id': movement.source_step_id,
                    'source_step': source_step_name,
                    'destination_step_id': movement.destination_step_id,
                    'destination_step': destination_step_name,
                    'step': movement.step.name
                })
            
            return result
            
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des mouvements: {str(e)}")
        finally:
            session.close()
    
    def get_resource_consumption_summary(self, step: Optional[ProcessStepEnum] = None,
                                       resource_type: Optional[ResourceType] = None,
                                       start_date: Optional[datetime] = None,
                                       end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des consommations de ressources."""
        session = self.Session()
        try:
            query = session.query(ResourceConsumption).join(ProcessStep)
            
            if step:
                query = query.filter(ProcessStep.name == step.value)
            if resource_type:
                query = query.filter(ResourceConsumption.resource_type == resource_type)
            if start_date:
                query = query.filter(ResourceConsumption.consumption_date >= start_date)
            if end_date:
                query = query.filter(ResourceConsumption.consumption_date <= end_date)
            
            consumptions = query.all()
            return [{
                'id': c.id,
                'date': c.consumption_date,
                'resource_type': c.resource_type,
                'quantity': c.quantity,
                'step': c.step.name
            } for c in consumptions]
            
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des consommations: {str(e)}")
        finally:
            session.close()

    def add_reception(self, quantity: float, quality: Optional[float] = None, reception_date: Optional[datetime] = None) -> int:
        """Ajoute une nouvelle réception de phosphate dans l'étape RECEPTION."""
        session = self.Session()
        try:
            # 1. Obtenir l'étape de réception
            reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
            
            if not reception_step:
                raise ValueError("Étape de réception non trouvée")
            
            current_date = reception_date or datetime.utcnow()
            
            logging.info(f"Ajout d'une réception de {quantity} tonnes")
            
            # 2. Créer le mouvement de stock pour la réception
            stock_movement = StockMovement(
                step_id=reception_step.id,  # Le stock est enregistré dans l'étape RECEPTION
                quantity=quantity,
                movement_type=StockMovementType.RECEPTION,
                movement_date=current_date,
                quality=quality,  # Stocker la qualité dans le mouvement
                source_step_id=None,  # Pas de source (vient de l'extérieur)
                destination_step_id=reception_step.id  # Destination = réception
            )
            session.add(stock_movement)
            
            # Log le mouvement
            logging.info(f"Création du mouvement de réception: step_id={stock_movement.step_id}, "
                        f"quantity={stock_movement.quantity}, "
                        f"type={stock_movement.movement_type}")
            
            session.commit()

            # Invalider le cache des stocks après modification
            invalidate_cache_for("stock_")

            # Vérifier le stock après l'ajout
            reception_stock = self.get_current_stock(ProcessStepEnum.RECEPTION)
            logging.info(f"Stock disponible en réception après ajout: {reception_stock}")

            return stock_movement.id
            
        except SQLAlchemyError as e:
            session.rollback()
            logging.error(f"Erreur lors de l'ajout de la réception: {str(e)}")
            raise Exception(f"Erreur lors de l'ajout de la réception: {str(e)}")
        finally:
            session.close()

    def get_receptions(self) -> List[Dict[str, Any]]:
        """Récupère l'historique des réceptions."""
        session = self.Session()
        try:
            receptions = session.query(StockMovement).filter_by(movement_type=StockMovementType.RECEPTION).all()
            return [{
                'id': r.id,
                'reception_date': r.movement_date,
                'quantity': r.quantity,
                'quality': r.quality, # Utiliser la qualité stockée dans StockMovement
                'step': r.step.name
            } for r in receptions]
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération des réceptions: {str(e)}")
        finally:
            session.close()

    def get_reception_by_id(self, reception_id: int) -> Optional[Dict[str, Any]]:
        """Récupère une réception par son ID."""
        session = self.Session()
        try:
            reception = session.query(StockMovement).filter_by(id=reception_id, movement_type=StockMovementType.RECEPTION).first()
            if reception:
                return {
                    'id': reception.id,
                    'reception_date': reception.movement_date,
                    'quantity': reception.quantity,
                    'quality': reception.quality, # Utiliser la qualité stockée dans StockMovement
                    'step': reception.step.name
                }
            return None
        except SQLAlchemyError as e:
            raise Exception(f"Erreur lors de la récupération de la réception par ID: {str(e)}")
        finally:
            session.close()

    def delete_reception(self, reception_id: int) -> None:
        """Supprime une réception par son ID."""
        session = self.Session()
        try:
            reception = session.query(StockMovement).filter_by(id=reception_id, movement_type=StockMovementType.RECEPTION).first()
            if reception:
                session.delete(reception)
                session.commit()
            else:
                raise ValueError(f"Réception avec ID {reception_id} non trouvée.")
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de la suppression de la réception: {str(e)}")
        finally:
            session.close()
            
    def delete_production(self, production_id: int) -> None:
        """Supprime une production par son ID."""
        session = self.Session()
        try:
            # Trouver la production
            production = session.query(Production).filter_by(id=production_id).first()
            if not production:
                raise ValueError(f"Production avec ID {production_id} non trouvée.")
                
            # Supprimer les mouvements de stock associés
            stock_movements = session.query(StockMovement).filter_by(reference_id=production_id).all()
            for movement in stock_movements:
                session.delete(movement)
                
            # Supprimer la production
            session.delete(production)
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de la suppression de la production: {str(e)}")
        finally:
            session.close()
            
    def delete_downtime(self, downtime_id: int) -> None:
        """Supprime un arrêt par son ID."""
        session = self.Session()
        try:
            downtime = session.query(Downtime).filter_by(id=downtime_id).first()
            if downtime:
                session.delete(downtime)
                session.commit()
            else:
                raise ValueError(f"Arrêt avec ID {downtime_id} non trouvé.")
        except SQLAlchemyError as e:
            session.rollback()
            raise Exception(f"Erreur lors de la suppression de l'arrêt: {str(e)}")
        finally:
            session.close()

    def import_receptions_from_excel(self, file_path: str) -> List[int]:
        """
        Importe des réceptions depuis un fichier Excel.
        
        Args:
            file_path: Chemin vers le fichier Excel
        
        Returns:
            List[int]: Liste des IDs des réceptions importées
        """
        from utils.excel_importer import read_reception_excel
        imported_ids = []
        
        try:
            # Lire le fichier Excel
            receptions = read_reception_excel(file_path)
            
            # Importer chaque réception
            session = self.Session()
            try:
                reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
                
                if not reception_step:
                    raise ValueError("L'étape de réception n'est pas configurée")
                
                for reception_data in receptions:
                    # Créer le mouvement de stock pour la réception
                    stock_movement = StockMovement(
                        step_id=reception_step.id,  # Le stock va à la réception
                        quantity=reception_data['quantity'],
                        movement_type=StockMovementType.RECEPTION,
                        movement_date=reception_data['reception_date'],
                        quality=reception_data.get('quality'),  # Stocker la qualité si disponible
                        source_step_id=None,  # Pas de source (vient de l'extérieur)
                        destination_step_id=reception_step.id  # La destination est la réception
                    )
                    session.add(stock_movement)
                    imported_ids.append(stock_movement.id)
                    
                session.commit()
                return imported_ids
                
            except SQLAlchemyError as e:
                session.rollback()
                logging.error(f"Erreur SQL lors de l'importation des réceptions: {str(e)}", exc_info=True)
                raise Exception(f"Erreur lors de l'importation des réceptions: {str(e)}")
            except Exception as e:
                session.rollback()
                logging.error(f"Erreur lors de l'importation des réceptions: {str(e)}", exc_info=True)
                raise
            finally:
                session.close()
                
        except Exception as e:
            raise Exception(f"Erreur lors de la lecture du fichier Excel: {str(e)}")

    def get_available_crushing_stock(self) -> float:
        """Récupère le stock disponible venant du concassage pour la laverie."""
        session = self.Session()
        try:
            crushing_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.CONCASSAGE.value).first()
            if not crushing_step:
                return 0.0
            
            logging.info(f"Calcul du stock disponible du concassage pour la laverie")
            
            # 1. Stock produit par le concassage
            stock_produced_query = session.query(func.sum(StockMovement.quantity)).filter(
                StockMovement.step_id == crushing_step.id,
                StockMovement.movement_type == StockMovementType.PRODUCTION
            )
            stock_produced = stock_produced_query.scalar() or 0.0
            
            logging.info(f"Stock produit par le concassage: {stock_produced}")
            
            # 2. Stock déjà consommé par la laverie
            laverie_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.LAVERIE.value).first()
            consumed_by_laverie = 0.0
            
            if laverie_step:
                consumed_by_laverie_query = session.query(func.sum(func.abs(StockMovement.quantity))).filter(
                    StockMovement.step_id == crushing_step.id,
                    StockMovement.movement_type == StockMovementType.CONSUMPTION,
                    StockMovement.destination_step_id == laverie_step.id
                )
                consumed_by_laverie = consumed_by_laverie_query.scalar() or 0.0
            
            logging.info(f"Déjà consommé par la laverie: {consumed_by_laverie}")
            
            # 3. Stock disponible = stock produit - consommé par laverie
            available_stock = stock_produced - consumed_by_laverie
            
            logging.info(f"Stock disponible pour la laverie: {available_stock}")
            
            return max(0.0, available_stock)
        finally:
            session.close()
            
    def get_current_stock(self, step: ProcessStepEnum) -> float:
        """Calcule le stock actuel pour une étape donnée.

        Args:
            step: Étape du processus pour laquelle calculer le stock

        Returns:
            Stock actuel en tonnes (toujours positif ou nul)
        """
        # Utiliser le cache pour éviter les requêtes répétitives
        cache_key = f"stock_{step.value}"
        cached_stock = cache_manager.get(cache_key)
        if cached_stock is not None:
            return cached_stock

        with self._get_session() as session:
            # Récupérer l'étape
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            logging.info(f"Calcul du stock actuel pour l'étape {step.value}")
            
            # Calculer le stock actuel selon l'étape
            if step == ProcessStepEnum.RECEPTION:
                # Pour la réception: entrées (réceptions) - sorties (consommations vers concassage)
                # 1. Entrées (réceptions)
                receptions = (session.query(func.sum(StockMovement.quantity))
                            .filter(StockMovement.step_id == process_step.id)
                            .filter(StockMovement.movement_type == StockMovementType.RECEPTION)
                            .scalar() or 0.0)
                
                # 2. Sorties (consommations vers concassage)
                concassage_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.CONCASSAGE.value).first()
                consommations = 0.0
                if concassage_step:
                    consommations = (session.query(func.sum(func.abs(StockMovement.quantity)))
                                    .filter(StockMovement.step_id == process_step.id)
                                    .filter(StockMovement.movement_type == StockMovementType.CONSUMPTION)
                                    .filter(StockMovement.destination_step_id == concassage_step.id)
                                    .scalar() or 0.0)
                
                total = receptions - consommations
                logging.info(f"Stock réception: Entrées {receptions} - Sorties {consommations} = {total}")
                
            elif step == ProcessStepEnum.CONCASSAGE:
                # Pour le concassage: productions + entrées (de la réception) - sorties (vers laverie)
                # 1. Productions
                productions = (session.query(func.sum(StockMovement.quantity))
                              .filter(StockMovement.step_id == process_step.id)
                              .filter(StockMovement.movement_type == StockMovementType.PRODUCTION)
                              .scalar() or 0.0)
                
                # 2. Sorties (consommations vers laverie)
                laverie_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.LAVERIE.value).first()
                consommations = 0.0
                if laverie_step:
                    consommations = (session.query(func.sum(func.abs(StockMovement.quantity)))
                                    .filter(StockMovement.step_id == process_step.id)
                                    .filter(StockMovement.movement_type == StockMovementType.CONSUMPTION)
                                    .filter(StockMovement.destination_step_id == laverie_step.id)
                                    .scalar() or 0.0)
                
                total = productions - consommations
                logging.info(f"Stock concassage: Productions {productions} - Consommations {consommations} = {total}")
                
            elif step == ProcessStepEnum.LAVERIE:
                # La laverie ne stocke pas - elle transfère tout directement au stockage final
                total = 0.0
                logging.info(f"Stock Laverie: {total} (pas de stockage intermédiaire)")

            elif step == ProcessStepEnum.STOCKAGE_FINAL:
                # Pour le stockage final: tous les mouvements (principalement des transferts de la laverie)
                total = (session.query(func.sum(StockMovement.quantity))
                        .filter(StockMovement.step_id == process_step.id)
                        .scalar() or 0.0)
                logging.info(f"Stock stockage final: {total}")

            else:
                # Pour les autres étapes: somme de tous les mouvements
                total = (session.query(func.sum(StockMovement.quantity))
                        .filter(StockMovement.step_id == process_step.id)
                        .scalar() or 0.0)
                logging.info(f"Stock {step.value}: {total}")
                
            result = round(float(max(0.0, total)), 2)

            # Mettre en cache le résultat pour 60 secondes
            cache_manager.set(cache_key, result, ttl=60)

            return result
            
    def transfer_from_reception_to_crushing(self, quantity: float, quality: Optional[float] = None) -> int:
        """
        Transfère du stock de la réception vers le concassage.
        
        Args:
            quantity: Quantité à transférer
            quality: Qualité du stock (optionnel)
            
        Returns:
            ID du mouvement de stock créé
        """
        session = self.Session()
        try:
            # 1. Vérifier que les étapes existent
            reception_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.RECEPTION.value).first()
            crushing_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.CONCASSAGE.value).first()
            
            if not reception_step or not crushing_step:
                raise ValueError("Étapes du processus non trouvées")
            
            # 2. Vérifier qu'il y a assez de stock en réception
            reception_stock = self.get_current_stock(ProcessStepEnum.RECEPTION)
            if reception_stock < quantity:
                raise ValueError(f"Stock insuffisant en réception: {reception_stock} < {quantity}")
            
            # 3. Créer le mouvement de consommation depuis la réception
            consumption_movement = StockMovement(
                step_id=reception_step.id,
                quantity=-quantity,  # Négatif car c'est une consommation
                movement_type=StockMovementType.CONSUMPTION,
                movement_date=datetime.utcnow(),
                quality=quality,
                source_step_id=reception_step.id,
                destination_step_id=crushing_step.id
            )
            session.add(consumption_movement)
            
            # 4. Créer le mouvement de production pour le concassage
            production_movement = StockMovement(
                step_id=crushing_step.id,
                quantity=quantity,
                movement_type=StockMovementType.PRODUCTION,
                movement_date=datetime.utcnow(),
                quality=quality,
                source_step_id=reception_step.id,
                destination_step_id=crushing_step.id
            )
            session.add(production_movement)
            
            session.commit()
            
            logging.info(f"Transfert de {quantity} tonnes de la réception vers le concassage effectué")
            
            return production_movement.id
            
        except SQLAlchemyError as e:
            session.rollback()
            logging.error(f"Erreur SQL lors du transfert: {str(e)}")
            raise Exception(f"Erreur lors du transfert: {str(e)}")
        except Exception as e:
            session.rollback()
            logging.error(f"Erreur lors du transfert: {str(e)}")
            raise
        finally:
            session.close()
            
    def get_final_stock(self) -> Dict[str, Any]:
        """Récupère le stock disponible dans le stockage final avec sa qualité P2O5."""
        session = self.Session()
        try:
            stockage_final_step = session.query(ProcessStep).filter_by(name=ProcessStepEnum.STOCKAGE_FINAL.value).first()
            if not stockage_final_step:
                return {"stock": 0.0, "quality": None, "has_quality_data": False}
            
            logging.info(f"Calcul du stock disponible dans le stockage final")
            
            # 1. Stock total dans le stockage final
            stock_query = session.query(func.sum(StockMovement.quantity)).filter(
                StockMovement.step_id == stockage_final_step.id
            )
            total_stock = stock_query.scalar() or 0.0
            
            logging.info(f"Stock total dans le stockage final: {total_stock}")
            
            # 2. Calculer la qualité moyenne pondérée
            movements_query = session.query(StockMovement).filter(
                StockMovement.step_id == stockage_final_step.id,
                StockMovement.quality.isnot(None)
            ).order_by(StockMovement.movement_date.desc())
            
            movements = movements_query.all()
            
            total_weighted_quality = 0.0
            total_quantity = 0.0
            
            for movement in movements:
                if movement.quality is not None and movement.quantity > 0:
                    quality_value = self._convert_quality_to_numeric(movement.quality)
                    if quality_value is not None:
                        total_weighted_quality += quality_value * movement.quantity
                        total_quantity += movement.quantity
            
            average_quality = total_weighted_quality / total_quantity if total_quantity > 0 else None
            
            return {
                "stock": max(0.0, total_stock),
                "quality": average_quality,
                "has_quality_data": average_quality is not None
            }
        except Exception as e:
            logging.error(f"Erreur lors de la récupération du stock final: {str(e)}")
            return {
                "stock": 0.0,
                "quality": None,
                "has_quality_data": False
            }
        finally:
            session.close()
    def get_daily_production_report(self, step: ProcessStepEnum, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Génère un rapport de production quotidien pour une étape."""
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                return {'quantity': 0, 'quality': '-', 'hours': 0}
            
            # Récupérer les productions pour la période
            productions = session.query(Production).filter(
                Production.step_id == process_step.id,
                Production.production_date >= start_date,
                Production.production_date <= end_date
            ).all()
            
            total_quantity = sum(p.quantity or 0 for p in productions)
            total_hours = sum(p.production_hours or 0 for p in productions)
            
            # Calculer la qualité moyenne pondérée
            weighted_quality = 0
            total_qty_with_quality = 0
            
            for p in productions:
                if p.quality is not None and p.quantity:
                    quality_val = self._convert_quality_to_numeric(p.quality)
                    if quality_val is not None:
                        weighted_quality += p.quantity * quality_val
                        total_qty_with_quality += p.quantity
            
            avg_quality = weighted_quality / total_qty_with_quality if total_qty_with_quality > 0 else 0
            
            return {
                'quantity': total_quantity,
                'quality': f"{avg_quality:.2f}%" if avg_quality > 0 else '-',
                'hours': total_hours
            }
            
        except Exception as e:
            logging.error(f"Erreur lors du rapport quotidien pour {step.value}: {str(e)}")
            return {'quantity': 0, 'quality': '-', 'hours': 0}
        finally:
            session.close()
    
    def get_stock_movements_report(self, step: ProcessStepEnum, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Génère un rapport des mouvements de stock pour une étape et une période."""
        session = self.Session()
        try:
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                return []
            
            # Récupérer les mouvements pour la période
            movements = session.query(StockMovement).filter(
                StockMovement.step_id == process_step.id,
                StockMovement.movement_date >= start_date,
                StockMovement.movement_date <= end_date
            ).order_by(StockMovement.movement_date).all()
            
            result = []
            for movement in movements:
                # Simplifier les types de mouvements pour le rapport
                movement_type = 'ENTREE'
                if movement.movement_type in [StockMovementType.RECEPTION, StockMovementType.PRODUCTION, StockMovementType.TRANSFER]:
                    if movement.quantity > 0:
                        movement_type = 'ENTREE'
                    else:
                        movement_type = 'SORTIE'
                elif movement.movement_type == StockMovementType.CONSUMPTION:
                    movement_type = 'SORTIE'
                
                result.append({
                    'movement_type': movement_type,
                    'quantity': abs(movement.quantity),
                    'date': movement.movement_date
                })
            
            return result
            
        except Exception as e:
            logging.error(f"Erreur lors du rapport mouvements pour {step.value}: {str(e)}")
            return []
        finally:
            session.close()
            
    def add_unit_hour_counter(self, step: ProcessStepEnum, hours: float, 
                             reading_date: Optional[datetime] = None) -> None:
        """Ajoute un nouveau relevé de compteur horaire pour une unité."""
        session = self.Session()
        try:
            # Récupérer l'étape
            process_step = session.query(ProcessStep).filter_by(name=step.value).first()
            if not process_step:
                raise ValueError(f"Étape '{step.value}' non trouvée")
            
            # Créer le relevé
            hour_counter = UnitHourCounter(
                hours=hours,
                reading_date=reading_date or datetime.now(),
                step_id=process_step.id
            )
            
            session.add(hour_counter)
            session.commit()
            
        except SQLAlchemyError as e:
            session.rollback()
            logging.error(f"Erreur lors de l'ajout du relevé de compteur horaire: {str(e)}")
            raise
        finally:
            session.close()
            
    def get_unit_hour_counters(self, step: Optional[ProcessStepEnum] = None,
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère les relevés de compteurs horaires des unités."""
        session = self.Session()
        try:
            query = session.query(UnitHourCounter).join(ProcessStep)
            
            if step:
                query = query.filter(ProcessStep.name == step.value)
            if start_date:
                query = query.filter(UnitHourCounter.reading_date >= start_date)
            if end_date:
                query = query.filter(UnitHourCounter.reading_date <= end_date)
            
            # Trier par date de relevé (du plus récent au plus ancien)
            query = query.order_by(UnitHourCounter.reading_date.desc())
            
            counters = query.all()
            
            # Convertir les objets UnitHourCounter en dictionnaires
            result = []
            for counter in counters:
                result.append({
                    'id': counter.id,
                    'hours': counter.hours,
                    'reading_date': counter.reading_date,
                    'step_id': counter.step_id,
                    'step': session.query(ProcessStep).get(counter.step_id).name
                })
            
            return result
            
        except SQLAlchemyError as e:
            logging.error(f"Erreur lors de la récupération des compteurs horaires: {str(e)}")
            return []
        finally:
            session.close()
            
    def delete_unit_hour_counter(self, counter_id: int) -> None:
        """Supprime un relevé de compteur horaire."""
        session = self.Session()
        try:
            counter = session.query(UnitHourCounter).get(counter_id)
            if counter:
                session.delete(counter)
                session.commit()
            else:
                raise ValueError(f"Relevé de compteur horaire avec ID {counter_id} non trouvé")
                
        except SQLAlchemyError as e:
            session.rollback()
            logging.error(f"Erreur lors de la suppression du relevé de compteur horaire: {str(e)}")
            raise
        finally:
            session.close()