# SOTRAMINE PHOSPHATE - Application de Suivi de Production

## Description Générale

L'application SOTRAMINE PHOSPHATE est un logiciel de gestion et de suivi de production conçu spécifiquement pour l'industrie du phosphate. Elle permet de suivre et d'optimiser l'ensemble du processus de production, depuis la réception des matières premières jusqu'à la transformation finale, en passant par les étapes de concassage et de laverie.

## Fonctionnalités Principales

### Suivi des Étapes de Production
- **Réception** : Gestion des entrées de matières premières avec suivi de la qualité et des quantités
- **Concassage** : Suivi des opérations de concassage, des temps de production et des arrêts
- **Laverie** : Gestion des processus de lavage avec indicateurs de performance

### Gestion des Stocks
- Suivi en temps réel des niveaux de stock à chaque étape du processus
- Traçabilité complète des mouvements de stock (entrées, sorties, transferts)
- Classification par qualité (haute, moyenne, basse)

### Suivi des Performances
- Indicateurs clés de performance (KPI) pour chaque étape du processus
- Mesure de la productivité, du rendement et de la disponibilité des équipements
- Analyse des temps d'arrêt et des causes associées

### Compteurs Horaires
- Suivi du temps de fonctionnement des équipements
- Planification de la maintenance préventive

### Gestion des Consommations
- Suivi des consommations de ressources (énergie, eau, réactifs)
- Analyse de l'efficacité des ressources utilisées

### Rapports et Analyses
- Génération de rapports détaillés sur la production
- Tableaux de bord visuels pour l'aide à la décision
- Exportation des données pour analyses externes

## Interface Utilisateur
L'application dispose d'une interface professionnelle et intuitive, organisée en onglets thématiques permettant un accès rapide aux différentes fonctionnalités. Le design moderne et ergonomique facilite la navigation et l'utilisation quotidienne par les opérateurs et les gestionnaires.

## Avantages
- Centralisation des données de production
- Amélioration de la traçabilité des processus
- Optimisation des performances de production
- Aide à la prise de décision basée sur des données fiables
- Réduction des temps d'arrêt et amélioration de la maintenance

## Technologie
Développée avec des technologies modernes (Python, PyQt5), l'application offre des performances optimales et une grande fiabilité. Son architecture modulaire permet des évolutions et des adaptations selon les besoins spécifiques de l'entreprise.