"""
Package contenant les utilitaires de l'application.
"""

from .constants import *
from .exceptions import *
from .helpers import *
from .logging import *
from .validation import *

__all__ = [
    # Constants
    'APP_NAME',
    'APP_VERSION',
    'APP_AUTHOR',
    'APP_DESCRIPTION',
    'DEFAULT_DB_URL',
    'DEFAULT_DB_ECHO',
    'DEFAULT_WINDOW_WIDTH',
    'DEFAULT_WINDOW_HEIGHT',
    'DEFAULT_REFRESH_INTERVAL',
    'DEFAULT_DATE_FORMAT',
    'DEFAULT_TIME_FORMAT',
    'DEFAULT_DECIMAL_SEPARATOR',
    'DEFAULT_THOUSAND_SEPARATOR',
    'DEFAULT_WORKING_HOURS',
    'MIN_QUALITY',
    'MAX_QUALITY',
    'MIN_QUANTITY',
    'MAX_QUANTITY',
    'ALLOWED_EXPORT_EXTENSIONS',
    'DEFAULT_EXPORT_PATH',
    'DEFAULT_TEMPLATE_PATH',
    'DEFAULT_LOG_LEVEL',
    'DEFAULT_LOG_FILE',
    'DEFAULT_LOG_MAX_SIZE',
    'DEFAULT_LOG_BACKUP_COUNT',
    'Messages',
    'Styles',
    'DEFAULT_CONFIG_PATH',
    'DATABASE_URL',
    
    # Exceptions
    'AppException',
    'DatabaseError',
    'ValidationError',
    'ConfigError',
    'ExportError',
    'ImportError',
    'ResourceError',
    'UIError',
    
    # Helpers
    'setup_logging',
    'format_number',
    'parse_date',
    'format_date',
    'calculate_duration',
    'calculate_efficiency',
    'calculate_availability',
    'validate_quantity',
    'validate_quality',
    'ensure_directory',
    'get_file_extension',
    'is_valid_file',
    'format_duration',
    'calculate_daily_stats',
    
    # Logging
    'Logger',
    'get_logger',
    
    # Validation
    'validate_quantity',
    'validate_quality',
    'validate_date',
    'validate_time',
    'validate_duration',
    'validate_file_extension',
    'validate_required_fields',
    'validate_numeric_fields',
    'validate_date_range',
    'validate_production_data',
    'validate_downtime_data',
    'validate_stock_movement_data'
] 