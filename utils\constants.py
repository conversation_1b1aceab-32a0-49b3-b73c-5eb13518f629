from enum import Enum
from typing import Dict, List, Tuple

# Constantes de l'application
APP_NAME = "Suivi Unité Phosphate"
APP_VERSION = "1.0.0"
APP_AUTHOR = "OCP"
APP_DESCRIPTION = "Application de suivi de la production de l'unité phosphate"

# Constantes de la base de données
DEFAULT_DB_URL = "sqlite:///phosphate.db"
DEFAULT_DB_ECHO = False

# Constantes de l'interface
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
DEFAULT_REFRESH_INTERVAL = 30  # secondes
DEFAULT_DATE_FORMAT = "%d/%m/%Y"
DEFAULT_TIME_FORMAT = "%H:%M"
DEFAULT_DECIMAL_SEPARATOR = ","
DEFAULT_THOUSAND_SEPARATOR = " "

# Constantes de production
DEFAULT_WORKING_HOURS = 8.0
MIN_QUALITY = 0
MAX_QUALITY = 100
MIN_QUANTITY = 0
MAX_QUANTITY = 10000

# Constantes de fichiers
ALLOWED_EXPORT_EXTENSIONS = [".xlsx", ".xls", ".csv"]
DEFAULT_EXPORT_PATH = "exports"
DEFAULT_TEMPLATE_PATH = "templates"

# Constantes de logging
DEFAULT_LOG_LEVEL = "INFO"
DEFAULT_LOG_FILE = "app.log"
DEFAULT_LOG_MAX_SIZE = 10485760  # 10 MB
DEFAULT_LOG_BACKUP_COUNT = 5

# Constantes de validation
class ValidationError(Exception):
    """Exception levée lors d'une erreur de validation."""
    pass

# Constantes de messages
class Messages:
    """Messages de l'application."""
    
    # Messages d'erreur
    ERROR_INVALID_QUANTITY = "La quantité doit être comprise entre {min} et {max}."
    ERROR_INVALID_QUALITY = "La qualité doit être comprise entre {min} et {max}."
    ERROR_INVALID_DATE = "La date doit être au format JJ/MM/AAAA."
    ERROR_INVALID_TIME = "L'heure doit être au format HH:MM."
    ERROR_INVALID_DURATION = "La durée doit être un nombre positif."
    ERROR_INVALID_FILE = "Le fichier doit avoir une extension valide ({extensions})."
    ERROR_DATABASE = "Erreur de base de données: {error}"
    ERROR_EXPORT = "Erreur lors de l'export: {error}"
    ERROR_IMPORT = "Erreur lors de l'import: {error}"
    
    # Messages de succès
    SUCCESS_SAVE = "Données sauvegardées avec succès."
    SUCCESS_EXPORT = "Données exportées avec succès."
    SUCCESS_IMPORT = "Données importées avec succès."
    SUCCESS_DELETE = "Données supprimées avec succès."
    
    # Messages de confirmation
    CONFIRM_DELETE = "Voulez-vous vraiment supprimer ces données ?"
    CONFIRM_EXIT = "Voulez-vous vraiment quitter l'application ?"
    CONFIRM_OVERWRITE = "Le fichier existe déjà. Voulez-vous l'écraser ?"

# Constantes de style
class Styles:
    """Styles de l'application."""
    
    # Couleurs
    COLORS = {
        "primary": "#3498db",
        "secondary": "#2ecc71",
        "warning": "#f1c40f",
        "danger": "#e74c3c",
        "light": "#ecf0f1",
        "dark": "#2c3e50",
        "white": "#ffffff",
        "black": "#000000",
        "gray": "#95a5a6",
        "success": "#27ae60",
        "info": "#2980b9",
        "error": "#c0392b"
    }
    
    # Styles des widgets
    WIDGET_STYLES = {
        "QMainWindow": """
            QMainWindow {
                background-color: {light};
            }
        """,
        "QWidget": """
            QWidget {
                background-color: {light};
                color: {dark};
            }
        """,
        "QPushButton": """
            QPushButton {
                background-color: {primary};
                color: {white};
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: {info};
            }
            QPushButton:pressed {
                background-color: {dark};
            }
            QPushButton:disabled {
                background-color: {gray};
            }
        """,
        "QLineEdit": """
            QLineEdit {
                padding: 6px;
                border: 1px solid {gray};
                border-radius: 4px;
                background-color: {white};
            }
            QLineEdit:focus {
                border: 2px solid {primary};
            }
        """,
        "QComboBox": """
            QComboBox {
                padding: 6px;
                border: 1px solid {gray};
                border-radius: 4px;
                background-color: {white};
            }
            QComboBox:focus {
                border: 2px solid {primary};
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(resources/down-arrow.png);
                width: 12px;
                height: 12px;
            }
        """,
        "QTableWidget": """
            QTableWidget {
                background-color: {white};
                border: 1px solid {gray};
                border-radius: 4px;
                gridline-color: {light};
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: {primary};
                color: {white};
            }
            QHeaderView::section {
                background-color: {dark};
                color: {white};
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """,
        "QGroupBox": """
            QGroupBox {
                margin-top: 10px;
                padding: 15px;
                border: 1px solid {gray};
                border-radius: 5px;
                background-color: {light};
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: {dark};
                font-weight: bold;
                font-size: 14px;
                top: -8px;
                left: 10px;
            }
        """,
        "QLabel": """
            QLabel {
                font-size: 12px;
            }
            QLabel#title {
                font-size: 16px;
                font-weight: bold;
                color: {dark};
            }
            QLabel#subtitle {
                font-size: 14px;
                color: {gray};
            }
        """
    }
    
    @classmethod
    def get_style(cls, widget_type: str) -> str:
        """Récupère le style d'un widget."""
        style = cls.WIDGET_STYLES.get(widget_type, "")
        return style.format(**cls.COLORS)
    
    @classmethod
    def get_all_styles(cls) -> str:
        """Récupère tous les styles."""
        return "\n".join(cls.get_style(widget_type) for widget_type in cls.WIDGET_STYLES.keys()) 