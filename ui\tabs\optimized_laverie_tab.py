"""
Onglet laverie optimisé avec contrôle qualité et gestion des ressources.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QSlider,
                            QSpinBox, QDoubleSpinBox, QGroupBox, QLabel)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from datetime import datetime, timedelta
import logging

from .base_optimized import BaseOptimizedTab, QuickInputWidget, StatusWidget
from ui.widgets.professional_widgets import KPILine, StatusIndicator, ActionButton
from models.enums import ProcessStepEnum, StockMovementType, ResourceType
from ui.professional_theme import SotramineTheme

class OptimizedLaverieTab(BaseOptimizedTab):
    """Onglet laverie optimisé."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "💧 Laverie & Enrichissement", parent)
        self.is_running = False
        self.current_rate = 0
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface spécialisée."""
        # Section KPI Production & Qualité
        kpi_layout = self.add_kpi_section("📊 Performance & Qualité")
        
        kpi_container = QVBoxLayout()
        kpi_container.setSpacing(0)
        
        self.input_stock_line = KPILine("Stock Entrée", "0", "T")
        self.production_line = KPILine("Production Jour", "0", "T")
        self.quality_line = KPILine("Qualité P2O5", "0", "%")
        self.rendement_line = KPILine("Rendement", "0", "T/h")
        
        kpi_container.addWidget(self.input_stock_line)
        kpi_container.addWidget(self.production_line)
        kpi_container.addWidget(self.quality_line)
        kpi_container.addWidget(self.rendement_line)
        
        kpi_layout.addLayout(kpi_container)
        
        # Section Saisie Manuelle
        form_layout = self.add_form_section("📝 Saisie Production")
        
        fields = {
            'quantity_produced': {
                'label': 'Quantité Lavée (T):',
                'type': 'number',
                'placeholder': 'Ex: 75.5'
            },
            'quantity_consumed': {
                'label': 'Quantité Consommée (T):',
                'type': 'number',
                'placeholder': 'Ex: 90.0'
            },
            'quality_input': {
                'label': 'Qualité Entrée (%):',
                'type': 'number',
                'placeholder': 'Ex: 28.5'
            },
            'quality_output': {
                'label': 'Qualité Sortie (%):',
                'type': 'number',
                'placeholder': 'Ex: 31.2'
            },
            'water_used': {
                'label': 'Eau Utilisée (m³):',
                'type': 'number',
                'placeholder': 'Ex: 185.5'
            },
            'reactive_used': {
                'label': 'Réactifs Utilisés (kg):',
                'type': 'number',
                'placeholder': 'Ex: 42.5'
            },
            'production_hours': {
                'label': 'Heures de Production:',
                'type': 'number',
                'placeholder': 'Ex: 7.5'
            }
        }
        
        self.quick_input = QuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_washing_production)
        form_layout.addWidget(self.quick_input)
        
        self.add_stretch()
        
        # Tableau de production (zone droite)
        headers = ["Date/Heure", "Lavé (T)", "Consommé (T)", "Qualité (%)", "Eau (m³)", "Réactifs (kg)", "Efficacité (%)"]
        self.data_table = self.add_data_table("📈 Historique Laverie", headers)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers - KPI seulement."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)
        

    def update_kpi(self):
        """Met à jour les KPI."""
        try:
            # Stock d'entrée disponible pour la laverie (concassage)
            input_stock = self.db_manager.get_available_crushing_stock()
            self.input_stock_line.update_value(f"{input_stock:.1f}")
            
            # Production du jour
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=today
            )
            today_total = sum(p['quantity'] for p in today_productions)
            self.production_line.update_value(f"{today_total:.1f}")
            
            # Qualité moyenne du jour
            if today_productions:
                # Calculer qualité moyenne pondérée
                total_weighted_quality = 0
                total_quantity = 0
                for p in today_productions:
                    if p['quality'] and p['quantity'] > 0:
                        try:
                            quality_val = float(p['quality'].replace('%', ''))
                            total_weighted_quality += quality_val * p['quantity']
                            total_quantity += p['quantity']
                        except:
                            pass
                
                avg_quality = total_weighted_quality / total_quantity if total_quantity > 0 else 0
                self.quality_line.update_value(f"{avg_quality:.1f}")
            else:
                self.quality_line.update_value("0")
            
            # Rendement (production par heure)
            total_hours = sum(p.get('production_hours', 0) or 0 for p in today_productions)
            if total_hours > 0:
                rendement = today_total / total_hours
                self.rendement_line.update_value(f"{rendement:.1f}")
            else:
                self.rendement_line.update_value("0")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI laverie: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données."""
        try:
            # Dernières productions (7 derniers jours)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_date,
                end_date=end_date
            )
            
            self.populate_table(productions)
            
        except Exception as e:
            logging.error(f"Erreur actualisation données laverie: {str(e)}")
            
    def populate_table(self, productions):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(productions))
        
        for row, production in enumerate(productions):
            # Date/Heure
            self.data_table.setItem(row, 0,
                self.data_table.itemClass()(production['production_date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité lavée
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{production['quantity']:.2f}"))
            
            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))
            
            # Qualité
            quality = production.get('quality', '')
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()(quality if quality else "-"))
            
            # Eau utilisée (estimation basée sur la production)
            water_estimate = production['quantity'] * 2.5  # Estimation 2.5 m³/T
            self.data_table.setItem(row, 4,
                self.data_table.itemClass()(f"{water_estimate:.1f}"))
            
            # Réactifs utilisés (estimation)
            reactive_estimate = production['quantity'] * 0.5  # Estimation 0.5 kg/T
            self.data_table.setItem(row, 5,
                self.data_table.itemClass()(f"{reactive_estimate:.1f}"))
            
            # Efficacité
            if consumed and consumed > 0:
                efficiency = (production['quantity'] / consumed) * 100
                self.data_table.setItem(row, 6,
                    self.data_table.itemClass()(f"{efficiency:.1f}%"))
            else:
                self.data_table.setItem(row, 6,
                    self.data_table.itemClass()("-"))
                    
    def save_washing_production(self, data):
        """Sauvegarde une production manuelle de laverie."""
        try:
            # Validation
            if not data.get('quantity_produced') or float(data['quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité lavée doit être positive")
                return
                
            quantity_produced = float(data['quantity_produced'])
            quantity_consumed = float(data.get('quantity_consumed', 0))
            quality_output = data.get('quality_output', '31.0')
            water_used = float(data.get('water_used', 0))
            reactive_used = float(data.get('reactive_used', 0))
            hours = float(data.get('production_hours', 0))
            
            # Enregistrer la production
            # La méthode add_production gère déjà les mouvements de stock pour la consommation et la production
            self.db_manager.add_production(
                step=ProcessStepEnum.LAVERIE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality_output}%" if quality_output else None
            )
            
            # Note: Nous ne créons plus de mouvements de stock supplémentaires ici
            # car add_production crée déjà:
            # 1. Un mouvement de consommation sur le concassage
            # 2. Un mouvement de production sur la laverie
            
            # Enregistrer les consommations de ressources
            if water_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.WATER,
                    quantity=water_used,
                    step=ProcessStepEnum.LAVERIE
                )
                
            if reactive_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.REACTIVE,
                    quantity=reactive_used,
                    step=ProcessStepEnum.LAVERIE
                )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Succès",
                f"Production laverie de {quantity_produced:.1f}T enregistrée")
                
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production laverie: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
