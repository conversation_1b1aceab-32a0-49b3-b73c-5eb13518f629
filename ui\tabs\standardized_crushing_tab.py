"""
Onglet concassage standardisé avec interface cohérente et optimisée.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QSlider,
                            QSpinBox, QCheckBox, QGroupBox, QButtonGroup, QFileDialog)
from PyQt5.QtCore import Qt, QDateTime, QTimer
from datetime import datetime, timedelta
import logging
import pandas as pd

from .standardized_base_tab import StandardizedBaseTab, StandardizedQuickInputWidget
from ui.widgets.professional_widgets import KPICard, StatusIndicator, ActionButton
from models.enums import ProcessStepEnum, StockMovementType
from ui.professional_theme import SotramineTheme

class StandardizedCrushingTab(StandardizedBaseTab):
    """Onglet concassage standardisé avec structure cohérente."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🔨 Concassage & Broyage", parent)
        self.is_running = False
        self.current_rate = 0
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface standardisée."""
        # Section KPI (toujours visible en haut)
        self.stock_input_card = self.add_kpi_card("Stock Entrée", "0", "T")
        self.production_card = self.add_kpi_card("Production Jour", "0", "T") 
        self.efficiency_card = self.add_kpi_card("Efficacité", "0", "%")
        self.output_card = self.add_kpi_card("Stock Sortie", "0", "T")
        self.hourly_rate_card = self.add_kpi_card("Rendement", "0", "T/h")
        
        # Section Saisie Manuelle (zone gauche)
        form_layout = self.add_form_section("📝 Saisie Production")
        
        fields = {
            'quantity_produced': {
                'label': 'Quantité Produite (T):',
                'type': 'number',
                'placeholder': 'Ex: 85.5',
                'category': 'Production'
            },
            'quantity_consumed': {
                'label': 'Quantité Consommée (T):',
                'type': 'number', 
                'placeholder': 'Ex: 100.0',
                'category': 'Production'
            },
            'production_hours': {
                'label': 'Heures de Production:',
                'type': 'number',
                'placeholder': 'Ex: 8.5',
                'category': 'Temps'
            },
            'quality': {
                'label': 'Qualité Sortie (%):',
                'type': 'number',
                'placeholder': 'Ex: 30.5',
                'category': 'Qualité'
            },
            'production_date': {
                'label': 'Date/Heure:',
                'type': 'datetime',
                'category': 'Temps'
            },
            'notes': {
                'label': 'Notes:',
                'type': 'text',
                'placeholder': 'Commentaires optionnels',
                'category': 'Autres'
            }
        }
        
        self.quick_input = StandardizedQuickInputWidget(fields)
        self.quick_input.data_entered.connect(self.save_production)
        form_layout.addWidget(self.quick_input)
        
        # Section Actions (zone gauche)
        actions_layout = self.add_actions_section("🔧 Actions Avancées")
        
        # Bouton d'import Excel
        import_btn = ActionButton("📊 Importer Excel", "Importer", "primary")
        import_btn.clicked.connect(self.import_from_excel)
        actions_layout.addWidget(import_btn)
        
        # Bouton d'export détaillé
        export_btn = ActionButton("📤 Exporter Rapport", "Export", "primary")
        export_btn.clicked.connect(self.export_detailed_report)
        actions_layout.addWidget(export_btn)
        
        self.add_stretch()
        
        # Tableau de production (zone droite)
        headers = ["Date/Heure", "Produit (T)", "Consommé (T)", "Heures", "Rendement (T/h)", "Efficacité (%)", "Qualité (%)", "Actions"]
        self.data_table = self.add_data_table("📈 Historique de Production", headers, sortable=True, paginated=True)
        
        # Charger les données initiales
        self.refresh_data()
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer KPI pour mises à jour données
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_kpi)
        self.kpi_timer.start(30000)  # Mise à jour toutes les 30 secondes
        
    def update_kpi(self):
        """Met à jour les KPI."""
        try:
            # Stock d'entrée (réception)
            input_stock = self.db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
            self.stock_input_card.update_value(f"{input_stock:.1f}")
            
            # Stock de sortie (concassage)
            output_stock = self.db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
            self.output_card.update_value(f"{output_stock:.1f}")
            
            # Production du jour
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=today
            )
            today_total = sum(p['quantity'] for p in today_productions)
            self.production_card.update_value(f"{today_total:.1f}")
            
            # Efficacité (production vs consommation)
            today_consumed = sum(p['quantity_used'] for p in today_productions if p['quantity_used'])
            efficiency = (today_total / today_consumed * 100) if today_consumed > 0 else 0
            self.efficiency_card.update_value(f"{efficiency:.1f}")
            
            # Rendement horaire moyen
            total_hours = sum(p['production_hours'] for p in today_productions if p['production_hours'])
            hourly_rate = (today_total / total_hours) if total_hours > 0 else 0
            self.hourly_rate_card.update_value(f"{hourly_rate:.1f}")
            
            # Mettre à jour la barre d'état
            self.update_status(f"Dernière mise à jour: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Erreur mise à jour KPI concassage: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def refresh_data(self):
        """Actualise le tableau des données avec pagination."""
        try:
            self.update_status("Chargement des données...")
            
            # Récupérer les paramètres de pagination
            pagination = self.get_pagination_params()
            
            # Dernières productions (avec pagination)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 derniers jours par défaut
            
            # Récupérer le nombre total d'éléments pour la pagination
            total_count = self.db_manager.get_production_count(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date
            )
            self.total_items = total_count
            
            # Récupérer les données paginées
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date,
                limit=pagination['limit'],
                offset=pagination['offset'],
                order_by='production_date',
                order_direction='DESC'  # Plus récent en haut
            )
            
            self.populate_table(productions)
            self.update_status(f"Affichage de {len(productions)} enregistrements sur {total_count}")
            
        except Exception as e:
            logging.error(f"Erreur actualisation données concassage: {str(e)}")
            self.update_status(f"Erreur: {str(e)}")
            
    def populate_table(self, productions):
        """Remplit le tableau avec les données."""
        self.data_table.setRowCount(len(productions))
        
        for row, production in enumerate(productions):
            # Date/Heure
            self.data_table.setItem(row, 0,
                self.data_table.itemClass()(production['production_date'].strftime("%d/%m/%Y %H:%M")))
            
            # Quantité produite
            self.data_table.setItem(row, 1,
                self.data_table.itemClass()(f"{production['quantity']:.2f}"))
            
            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.data_table.setItem(row, 2,
                self.data_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))
            
            # Heures
            hours = production.get('production_hours', 0)
            self.data_table.setItem(row, 3,
                self.data_table.itemClass()(f"{hours:.2f}" if hours else "-"))
            
            # Rendement
            if hours and hours > 0:
                rendement = production['quantity'] / hours
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()(f"{rendement:.2f}"))
            else:
                self.data_table.setItem(row, 4,
                    self.data_table.itemClass()("-"))
            
            # Efficacité
            if consumed and consumed > 0:
                efficacity = (production['quantity'] / consumed) * 100
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()(f"{efficacity:.1f}%"))
            else:
                self.data_table.setItem(row, 5,
                    self.data_table.itemClass()("-"))
            
            # Qualité
            quality = production.get('quality', '')
            self.data_table.setItem(row, 6,
                self.data_table.itemClass()(quality if quality else "-"))
            
            # Actions
            self.data_table.setItem(row, 7,
                self.data_table.itemClass()("📝 Modifier | 🗑️ Supprimer"))
                    
    def save_production(self, data):
        """Sauvegarde une production manuelle."""
        try:
            # Validation
            if not data.get('quantity_produced') or float(data['quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return
                
            quantity_produced = float(data['quantity_produced'])
            quantity_consumed = float(data.get('quantity_consumed', 0)) if data.get('quantity_consumed') else 0
            hours = float(data.get('production_hours', 0)) if data.get('production_hours') else 0
            quality = data.get('quality', '30.0')
            production_date = data.get('production_date', datetime.now())
            notes = data.get('notes', '')
            
            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality}%" if quality else None,
                production_date=production_date,
                notes=notes
            )
            
            # Mouvements de stock
            if quantity_consumed > 0:
                # Consommer du stock de réception
                self.db_manager.add_stock_movement(
                    step=ProcessStepEnum.RECEPTION,
                    quantity=-quantity_consumed,
                    movement_type=StockMovementType.CONSUMPTION,
                    movement_date=production_date
                )
            
            # Ajouter au stock de concassage
            self.db_manager.add_stock_movement(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                movement_type=StockMovementType.PRODUCTION,
                movement_date=production_date,
                quality=float(quality) if quality else None
            )
            
            # Effacer le formulaire
            self.quick_input.clear_form()
            
            # Actualiser
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Succès",
                f"Production de {quantity_produced:.1f}T enregistrée")
                
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
            
    def import_from_excel(self):
        """Importe des données depuis un fichier Excel."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "Importer fichier Excel", 
                "", 
                "Fichiers Excel (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # Lire le fichier Excel
            df = pd.read_excel(file_path)
            
            # Vérifier les colonnes requises
            required_columns = ['Quantité', 'Date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "Erreur", 
                    f"Colonnes manquantes: {', '.join(missing_columns)}")
                return
                
            # Progress bar
            progress = QProgressBar()
            progress.setMaximum(len(df))
            progress.show()
            
            imported_count = 0
            
            for index, row in df.iterrows():
                try:
                    quantity = float(row['Quantité'])
                    date = pd.to_datetime(row['Date']).to_pydatetime()
                    quality = float(row.get('Qualité', 0)) if pd.notna(row.get('Qualité')) else None
                    consumed = float(row.get('Consommé', 0)) if pd.notna(row.get('Consommé')) else None
                    hours = float(row.get('Heures', 0)) if pd.notna(row.get('Heures')) else None
                    
                    # Enregistrer la production
                    self.db_manager.add_production(
                        step=ProcessStepEnum.CONCASSAGE,
                        quantity=quantity,
                        quantity_used=consumed,
                        production_hours=hours,
                        quality=f"{quality}%" if quality else None,
                        production_date=date
                    )
                    
                    # Mouvements de stock
                    if consumed and consumed > 0:
                        self.db_manager.add_stock_movement(
                            step=ProcessStepEnum.RECEPTION,
                            quantity=-consumed,
                            movement_type=StockMovementType.CONSUMPTION,
                            movement_date=date
                        )
                    
                    self.db_manager.add_stock_movement(
                        step=ProcessStepEnum.CONCASSAGE,
                        quantity=quantity,
                        movement_type=StockMovementType.PRODUCTION,
                        movement_date=date,
                        quality=quality
                    )
                    
                    imported_count += 1
                    progress.setValue(index + 1)
                    
                except Exception as e:
                    logging.warning(f"Erreur ligne {index + 1}: {str(e)}")
                    continue
                    
            progress.close()
            
            # Actualiser les données
            self.update_kpi()
            self.refresh_data()
            
            QMessageBox.information(self, "✅ Import Terminé", 
                f"{imported_count} productions importées avec succès")
                
        except Exception as e:
            logging.error(f"Erreur import Excel: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'import: {str(e)}")
            
    def export_data(self):
        """Exporte les données du tableau vers Excel."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter vers Excel",
                f"concassage_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            # Récupérer les données actuelles du tableau
            data = []
            for row in range(self.data_table.rowCount()):
                row_data = {}
                for col in range(self.data_table.columnCount() - 1):  # Exclure la colonne Actions
                    header = self.data_table.horizontalHeaderItem(col).text()
                    item = self.data_table.item(row, col)
                    row_data[header] = item.text() if item else ""
                data.append(row_data)
                
            # Créer DataFrame
            df = pd.DataFrame(data)
            
            # Sauvegarder
            df.to_excel(file_path, index=False, sheet_name='Concassage')
            
            self.update_status(f"Données exportées vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Données exportées vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")
            
    def export_detailed_report(self):
        """Exporte un rapport détaillé avec graphiques et analyses."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter Rapport Détaillé",
                f"rapport_concassage_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if not file_path:
                return
                
            self.update_status("Génération du rapport détaillé...")
            
            # Récupérer les données des 30 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date,
                limit=1000  # Nombre élevé pour avoir toutes les données
            )
            
            # Créer un DataFrame avec les données
            data = []
            for prod in productions:
                data.append({
                    'Date': prod['production_date'],
                    'Quantité Produite (T)': prod['quantity'],
                    'Quantité Consommée (T)': prod.get('quantity_used', 0) or 0,
                    'Heures de Production': prod.get('production_hours', 0) or 0,
                    'Rendement (T/h)': prod['quantity'] / prod['production_hours'] if prod.get('production_hours', 0) else 0,
                    'Efficacité (%)': (prod['quantity'] / prod['quantity_used'] * 100) if prod.get('quantity_used', 0) else 0,
                    'Qualité (%)': prod.get('quality', '').replace('%', '') if prod.get('quality') else ''
                })
                
            df = pd.DataFrame(data)
            
            # Créer un writer Excel avec xlsxwriter
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # Écrire les données brutes
            df.to_excel(writer, sheet_name='Données', index=False)
            
            # Créer une feuille de résumé
            summary = pd.DataFrame({
                'Métrique': [
                    'Total Produit (T)',
                    'Total Consommé (T)',
                    'Heures Totales',
                    'Rendement Moyen (T/h)',
                    'Efficacité Moyenne (%)',
                    'Qualité Moyenne (%)'
                ],
                'Valeur': [
                    df['Quantité Produite (T)'].sum(),
                    df['Quantité Consommée (T)'].sum(),
                    df['Heures de Production'].sum(),
                    df['Quantité Produite (T)'].sum() / df['Heures de Production'].sum() if df['Heures de Production'].sum() > 0 else 0,
                    df['Quantité Produite (T)'].sum() / df['Quantité Consommée (T)'].sum() * 100 if df['Quantité Consommée (T)'].sum() > 0 else 0,
                    pd.to_numeric(df['Qualité (%)'], errors='coerce').mean()
                ]
            })
            
            summary.to_excel(writer, sheet_name='Résumé', index=False)
            
            # Sauvegarder et fermer
            writer.save()
            
            self.update_status(f"Rapport détaillé exporté vers: {file_path}")
            QMessageBox.information(self, "✅ Export Terminé", 
                f"Rapport détaillé exporté vers:\n{file_path}")
                
        except Exception as e:
            logging.error(f"Erreur export rapport: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export du rapport: {str(e)}")
            
    def print_report(self):
        """Prépare et imprime un rapport optimisé pour l'impression."""
        try:
            self.update_status("Préparation de l'impression...")
            
            # Créer une mise en page spéciale pour l'impression
            # Cette fonction pourrait utiliser QPrinter et QPainter pour générer un rapport PDF
            # ou utiliser une bibliothèque comme ReportLab
            
            QMessageBox.information(self, "Impression", 
                "Fonctionnalité d'impression en cours de développement.\n"
                "Veuillez utiliser l'export Excel pour le moment.")
                
        except Exception as e:
            logging.error(f"Erreur impression: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la préparation de l'impression: {str(e)}")