#!/usr/bin/env python3
"""
Script de test et d'analyse du flux de matière dans l'application SOTRAMINE PHOSPHATE.
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_flux_matiere():
    """Teste le flux complet de matière."""
    print("🔍 Analyse du flux de matière...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum, StockMovementType
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Nettoyer les données existantes pour un test propre
        print("\n📊 État initial des stocks:")
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f} tonnes")
        
        print("\n🧪 Test du flux de matière:")
        
        # 1. RÉCEPTION - Ajouter du phosphate brut
        print("\n1️⃣ RÉCEPTION - Ajout de 1000 tonnes de phosphate brut")
        reception_id = db_manager.add_reception(
            quantity=1000.0,
            quality=25.0,  # 25% P2O5
            reception_date=datetime.now()
        )
        print(f"   ✅ Réception ajoutée (ID: {reception_id})")
        
        # Vérifier le stock de réception
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        print(f"   📦 Stock réception: {reception_stock:.1f} tonnes")
        
        # 2. CONCASSAGE - Traitement du phosphate brut
        print("\n2️⃣ CONCASSAGE - Traitement de 800 tonnes")
        try:
            production_id = db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=800.0,
                quality=25.0,
                quantity_used=900.0,  # Consomme 900 tonnes pour produire 800 tonnes
                production_hours=8.0,
                production_date=datetime.now()
            )
            print(f"   ✅ Production concassage ajoutée (ID: {production_id})")
        except Exception as e:
            print(f"   ❌ Erreur concassage: {str(e)}")
        
        # Vérifier les stocks après concassage
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        print(f"   📦 Stock réception après concassage: {reception_stock:.1f} tonnes")
        print(f"   📦 Stock concassage: {concassage_stock:.1f} tonnes")
        
        # 3. LAVERIE - Traitement du phosphate concassé
        print("\n3️⃣ LAVERIE - Traitement de 600 tonnes")
        try:
            production_id = db_manager.add_production(
                step=ProcessStepEnum.LAVERIE,
                quantity=600.0,
                quality=32.0,  # Amélioration de la qualité à 32% P2O5
                quantity_used=700.0,  # Consomme 700 tonnes pour produire 600 tonnes
                production_hours=10.0,
                production_date=datetime.now()
            )
            print(f"   ✅ Production laverie ajoutée (ID: {production_id})")
        except Exception as e:
            print(f"   ❌ Erreur laverie: {str(e)}")
        
        # Vérifier les stocks après laverie
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        laverie_stock = db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        stockage_final_stock = db_manager.get_current_stock(ProcessStepEnum.STOCKAGE_FINAL)
        print(f"   📦 Stock concassage après laverie: {concassage_stock:.1f} tonnes")
        print(f"   📦 Stock laverie: {laverie_stock:.1f} tonnes")
        print(f"   📦 Stock stockage final: {stockage_final_stock:.1f} tonnes")
        
        print("\n📊 État final des stocks:")
        for step in ProcessStepEnum:
            stock = db_manager.get_current_stock(step)
            print(f"  {step.value}: {stock:.1f} tonnes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_stock_movements():
    """Analyse les mouvements de stock."""
    print("\n🔍 Analyse des mouvements de stock...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum, StockMovementType
        
        db_manager = DatabaseManager()
        
        # Analyser les mouvements par étape
        for step in ProcessStepEnum:
            print(f"\n📋 Mouvements pour {step.value}:")
            movements = db_manager.get_stock_movements(step=step, limit=10)
            
            if not movements:
                print("   Aucun mouvement")
                continue
                
            for movement in movements[:5]:  # Afficher les 5 derniers
                date_str = movement['date'].strftime("%Y-%m-%d %H:%M")
                print(f"   {date_str}: {movement['movement_type']} - {movement['quantity']:+.1f}T")
                if movement.get('source_step'):
                    print(f"     Source: {movement['source_step']}")
                if movement.get('destination_step'):
                    print(f"     Destination: {movement['destination_step']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {str(e)}")
        return False

def check_flux_consistency():
    """Vérifie la cohérence du flux de matière."""
    print("\n🔍 Vérification de la cohérence du flux...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        db_manager = DatabaseManager()
        issues = []
        
        # 1. Vérifier que les stocks sont cohérents
        reception_stock = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        concassage_stock = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        laverie_stock = db_manager.get_current_stock(ProcessStepEnum.LAVERIE)
        stockage_final_stock = db_manager.get_current_stock(ProcessStepEnum.STOCKAGE_FINAL)
        
        # 2. Vérifier les stocks négatifs
        if reception_stock < 0:
            issues.append(f"❌ Stock réception négatif: {reception_stock:.1f}T")
        if concassage_stock < 0:
            issues.append(f"❌ Stock concassage négatif: {concassage_stock:.1f}T")
        if laverie_stock < 0:
            issues.append(f"❌ Stock laverie négatif: {laverie_stock:.1f}T")
        if stockage_final_stock < 0:
            issues.append(f"❌ Stock stockage final négatif: {stockage_final_stock:.1f}T")
        
        # 3. Vérifier la disponibilité pour le concassage
        available_crushing = db_manager.get_available_crushing_stock()
        if available_crushing < 0:
            issues.append(f"❌ Stock disponible concassage négatif: {available_crushing:.1f}T")
        
        # 4. Vérifier les méthodes de transfert
        try:
            # Test de transfert (sans l'exécuter réellement)
            if reception_stock > 0:
                print(f"✅ Transfert possible: {reception_stock:.1f}T disponible en réception")
            else:
                issues.append("⚠️ Aucun stock disponible pour transfert depuis réception")
        except Exception as e:
            issues.append(f"❌ Erreur méthode de transfert: {str(e)}")
        
        if issues:
            print("🚨 Problèmes détectés:")
            for issue in issues:
                print(f"  {issue}")
            return False
        else:
            print("✅ Flux de matière cohérent")
            return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {str(e)}")
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test et analyse du flux de matière SOTRAMINE PHOSPHATE")
    print("=" * 70)
    
    tests = [
        ("Flux de matière", test_flux_matiere),
        ("Mouvements de stock", analyze_stock_movements),
        ("Cohérence du flux", check_flux_consistency),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Résumé des résultats
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DE L'ANALYSE")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ OK" if result else "❌ PROBLÈME"
        print(f"{test_name:<25} : {status}")
        if result:
            passed += 1
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Le flux de matière fonctionne correctement !")
        return 0
    else:
        print("⚠️ Des corrections sont nécessaires dans le flux de matière.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
