class AppException(Exception):
    """Exception de base pour l'application."""
    pass

class DatabaseError(AppException):
    """Exception levée lors d'une erreur de base de données."""
    pass

class ValidationError(AppException):
    """Exception levée lors d'une erreur de validation."""
    pass

class ConfigError(AppException):
    """Exception levée lors d'une erreur de configuration."""
    pass

class ExportError(AppException):
    """Exception levée lors d'une erreur d'export."""
    pass

class ImportError(AppException):
    """Exception levée lors d'une erreur d'import."""
    pass

class ResourceError(AppException):
    """Exception levée lors d'une erreur de ressource."""
    pass

class UIError(AppException):
    """Exception levée lors d'une erreur d'interface."""
    pass 