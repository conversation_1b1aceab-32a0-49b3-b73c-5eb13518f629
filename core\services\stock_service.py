"""
Service de gestion des stocks.
"""

from datetime import datetime
from models.enums import ProcessStepEnum, MovementTypeEnum
from models.database_models import StockMovement

class StockService:
    def __init__(self, db_manager: object):
        self.db_manager = db_manager

    def add_stock_movement(self, quantity: float, step: ProcessStepEnum,
                           movement_type: MovementTypeEnum, movement_date: datetime) -> None:
        """Ajoute un mouvement de stock."""
        try:
            self.db_manager.add_stock_movement(
                quantity=quantity,
                step=step,
                movement_type=movement_type,
                movement_date=movement_date
            )
        except Exception as e:
            raise Exception(f"Erreur lors de l'ajout du mouvement de stock: {str(e)}")

    def get_current_stock(self, step: ProcessStepEnum) -> float:
        """Récupère le stock actuel pour une étape donnée."""
        try:
            return self.db_manager.get_current_stock(step)
        except Exception as e:
            raise Exception(f"Erreur lors de la récupération du stock actuel: {str(e)}")

    def get_stock_movements(self, step: ProcessStepEnum = None,
                          movement_type: MovementTypeEnum = None,
                          start_date: datetime = None,
                          end_date: datetime = None) -> list:
        """Récupère les mouvements de stock selon les filtres."""
        try:
            return self.db_manager.get_stock_movements(
                step=step,
                movement_type=movement_type,
                start_date=start_date,
                end_date=end_date
            )
        except Exception as e:
            raise Exception(f"Erreur lors de la récupération des mouvements de stock: {str(e)}")

    def transfer_stock(self, quantity: float, source_step: ProcessStepEnum, destination_step: ProcessStepEnum) -> None:
        """Transfère le stock d'une étape source à une étape destination."""
        try:
            # Vérifier le stock disponible à la source
            current_stock = self.db_manager.get_current_stock(source_step)
            if quantity > current_stock:
                raise ValueError(f"Stock insuffisant à la source. Disponible: {current_stock}, Demandé: {quantity}")

            # Ajouter le mouvement de sortie à la source
            self.db_manager.add_stock_movement(
                step=source_step,
                quantity=-quantity,
                movement_type=MovementTypeEnum.TRANSFER,
                reference_id=None
            )

            # Ajouter le mouvement d'entrée à la destination
            self.db_manager.add_stock_movement(
                step=destination_step,
                quantity=quantity,
                movement_type=MovementTypeEnum.TRANSFER,
                reference_id=None
            )
        except Exception as e:
            raise Exception(f"Erreur lors du transfert de stock: {str(e)}")