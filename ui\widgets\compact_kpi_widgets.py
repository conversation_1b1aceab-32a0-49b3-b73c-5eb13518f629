"""
Widgets KPI compacts et optimisés pour une meilleure lisibilité et un dimensionnement adapté.
"""

from PyQt5.QtWidgets import (QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
                            QGridLayout, QSizePolicy, QWidget, QGroupBox,
                            QScrollArea)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPainterPath
from ui.professional_theme import SotramineTheme
import math

class CompactKPICard(QFrame):
    """Carte KPI compacte avec dimensionnement optimisé."""
    
    def __init__(self, title, value="--", unit="", trend=None, category="", icon="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.trend = trend
        self.category = category
        self.icon = icon
        self.color = color or SotramineTheme.PRIMARY
        
        self.setProperty("class", "card")
        self.setFixedHeight(90)  # Hauteur fixe réduite
        self.setMinimumWidth(150)  # Largeur minimale réduite
        self.setMaximumWidth(200)  # Largeur maximale pour éviter l'étirement
        
        # Appliquer un style spécifique à cette carte
        self.setStyleSheet(f"""
            QFrame[class="card"] {{
                background-color: {SotramineTheme.BG_CARD};
                border: 1px solid {self.color}30;
                border-left: 3px solid {self.color};
                border-radius: 4px;
            }}
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la carte KPI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)  # Marges réduites
        layout.setSpacing(2)  # Espacement réduit
        
        # En-tête avec titre et icône
        header_layout = QHBoxLayout()
        header_layout.setSpacing(3)
        
        # Icône (si disponible)
        if self.icon:
            icon_label = QLabel()
            icon_label.setText(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 12pt;
                color: {self.color};
                margin-right: 3px;
            """)
            header_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 9pt;
            font-weight: bold;
            color: {self.color};
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        header_layout.addWidget(title_label, 1)
        
        layout.addLayout(header_layout)
        
        # Valeur principale et unité sur la même ligne
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 20pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        value_layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 9pt;
                color: {SotramineTheme.TEXT_MUTED};
                margin-left: 3px;
            """)
            unit_label.setAlignment(Qt.AlignBottom | Qt.AlignLeft)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        layout.addLayout(value_layout)
        
        # Tendance (si disponible) ou catégorie
        footer_layout = QHBoxLayout()
        
        if self.category:
            category_label = QLabel(self.category)
            category_label.setStyleSheet(f"""
                font-size: 7pt;
                color: {SotramineTheme.TEXT_MUTED};
                background-color: {self.color}10;
                border-radius: 2px;
                padding: 1px 3px;
            """)
            footer_layout.addWidget(category_label)
            footer_layout.addStretch()
        
        if self.trend is not None:
            self.trend_label = QLabel()
            self.update_trend(self.trend)
            self.trend_label.setAlignment(Qt.AlignRight)
            footer_layout.addWidget(self.trend_label)
        
        layout.addLayout(footer_layout)
    
    def update_value(self, value, trend=None):
        """Met à jour la valeur et éventuellement la tendance."""
        self.value = value
        self.value_label.setText(str(value))
        
        if trend is not None and hasattr(self, 'trend_label'):
            self.update_trend(trend)
    
    def update_trend(self, trend):
        """Met à jour l'affichage de la tendance."""
        self.trend = trend
        
        if not hasattr(self, 'trend_label'):
            return
            
        if trend > 0:
            self.trend_label.setText(f"↗ +{trend:.1f}%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.SECONDARY}; font-size: 7pt; font-weight: bold;")
        elif trend < 0:
            self.trend_label.setText(f"↘ {trend:.1f}%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.DANGER}; font-size: 7pt; font-weight: bold;")
        else:
            self.trend_label.setText("→ 0.0%")
            self.trend_label.setStyleSheet(f"color: {SotramineTheme.TEXT_MUTED}; font-size: 7pt; font-weight: bold;")


class CompactKPISection(QWidget):
    """Section compacte d'indicateurs KPI avec disposition en grille flexible."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.kpi_cards = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur de la section KPI."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(10)
        
        # Utiliser une grille pour les KPI
        self.grid_layout = QGridLayout()
        self.grid_layout.setHorizontalSpacing(8)  # Espacement horizontal réduit
        self.grid_layout.setVerticalSpacing(8)    # Espacement vertical réduit
        
        self.main_layout.addLayout(self.grid_layout)
    
    def add_kpi_card(self, title, value="0", unit="", trend=None, category="", icon="", color=None):
        """Ajoute une carte KPI à la section, organisée en grille."""
        # Créer la carte KPI
        card = CompactKPICard(title, value, unit, trend, category, icon, color)
        self.kpi_cards.append(card)
        
        # Ajouter à la grille
        row, col = divmod(len(self.kpi_cards) - 1, 4)  # 4 cartes par ligne
        self.grid_layout.addWidget(card, row, col)
        
        return card


class CompactIndicator(QFrame):
    """Indicateur compact pour les mesures de qualité et performance."""
    
    def __init__(self, title, value=0, min_value=0, max_value=100, target=None, 
                 unit="", color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.min_value = min_value
        self.max_value = max_value
        self.target = target
        self.unit = unit
        self.color = color or SotramineTheme.PRIMARY
        
        # Déterminer la couleur en fonction de la valeur
        self.update_color()
        
        self.setFixedHeight(70)  # Hauteur fixe réduite
        self.setMinimumWidth(120)  # Largeur minimale réduite
        self.setProperty("class", "card")
        
        self.setStyleSheet(f"""
            QFrame[class="card"] {{
                background-color: {SotramineTheme.BG_CARD};
                border: 1px solid {self.color}30;
                border-radius: 4px;
            }}
        """)
        
        self.init_ui()
    
    def update_color(self):
        """Met à jour la couleur en fonction de la valeur."""
        if self.target is not None:
            # Si on a une cible, la couleur dépend de la position par rapport à la cible
            if self.value >= self.target:
                self.color = SotramineTheme.SECONDARY  # Vert pour bon
            elif self.value >= self.target * 0.8:
                self.color = SotramineTheme.ACCENT  # Orange pour moyen
            else:
                self.color = SotramineTheme.DANGER  # Rouge pour mauvais
    
    def init_ui(self):
        """Initialise l'interface utilisateur de l'indicateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)  # Marges réduites
        layout.setSpacing(2)  # Espacement réduit
        
        # Titre et valeur sur la même ligne
        header_layout = QHBoxLayout()
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 9pt;
            font-weight: bold;
            color: {self.color};
        """)
        header_layout.addWidget(title_label)
        
        # Valeur et unité
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.color};
        """)
        self.value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        value_layout.addWidget(self.value_label, 1)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 8pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        header_layout.addLayout(value_layout)
        layout.addLayout(header_layout)
        
        # Jauge visuelle (sera dessinée dans paintEvent)
        self.gauge = QFrame()
        self.gauge.setFixedHeight(20)  # Hauteur fixe réduite
        self.gauge.paintEvent = self.paint_gauge
        layout.addWidget(self.gauge)
        
        # Afficher la cible si disponible
        if self.target is not None:
            target_label = QLabel(f"Cible: {self.target}{self.unit}")
            target_label.setStyleSheet(f"""
                font-size: 7pt;
                color: {SotramineTheme.TEXT_MUTED};
            """)
            target_label.setAlignment(Qt.AlignRight)
            layout.addWidget(target_label)
    
    def paint_gauge(self, event):
        """Dessine la jauge de performance."""
        painter = QPainter(self.gauge)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.gauge.width()
        height = self.gauge.height()
        
        # Calculer le pourcentage de progression
        range_value = self.max_value - self.min_value
        if range_value <= 0:
            progress = 0
        else:
            progress = (self.value - self.min_value) / range_value
            progress = max(0, min(1, progress))  # Limiter entre 0 et 1
        
        # Dessiner l'arrière-plan
        bg_color = QColor(self.color)
        bg_color.setAlpha(30)
        painter.setBrush(QBrush(bg_color))
        painter.setPen(Qt.NoPen)
        
        bg_rect = QRect(5, height // 2 - 5, width - 10, 10)  # Dimensions réduites
        painter.drawRoundedRect(bg_rect, 5, 5)  # Rayon réduit
        
        # Dessiner la progression
        if progress > 0:
            progress_width = int((width - 10) * progress)
            progress_rect = QRect(5, height // 2 - 5, progress_width, 10)  # Dimensions réduites
            
            painter.setBrush(QBrush(QColor(self.color)))
            painter.drawRoundedRect(progress_rect, 5, 5)  # Rayon réduit
        
        # Dessiner la cible si disponible
        if self.target is not None:
            target_progress = (self.target - self.min_value) / range_value
            target_progress = max(0, min(1, target_progress))
            
            target_x = 5 + int((width - 10) * target_progress)
            
            painter.setPen(QPen(QColor(SotramineTheme.TEXT_PRIMARY), 1))  # Épaisseur réduite
            painter.drawLine(target_x, height // 2 - 8, target_x, height // 2 + 8)  # Longueur réduite
            
            # Petit triangle pour marquer la cible
            path = QPainterPath()
            path.moveTo(target_x, height // 2 - 10)  # Position ajustée
            path.lineTo(target_x - 3, height // 2 - 7)  # Taille réduite
            path.lineTo(target_x + 3, height // 2 - 7)  # Taille réduite
            path.closeSubpath()
            
            painter.setBrush(QBrush(QColor(SotramineTheme.TEXT_PRIMARY)))
            painter.drawPath(path)
    
    def update_value(self, value):
        """Met à jour la valeur de l'indicateur."""
        self.value = value
        self.update_color()
        
        self.value_label.setText(f"{self.value}")
        self.value_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.color};
        """)
        
        # Mettre à jour la jauge
        self.gauge.update()


class KPITabWidget(QWidget):
    """Widget d'onglet optimisé pour afficher des KPI de manière organisée et compacte."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.sections = {}  # {section_name: section_widget}
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface utilisateur du widget d'onglet KPI."""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(5)  # Espacement réduit
        
        # Titre de l'onglet (si fourni)
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: 10pt;
                font-weight: bold;
                color: {SotramineTheme.TEXT_PRIMARY};
                padding: 2px 0;
            """)
            title_label.setAlignment(Qt.AlignCenter)
            self.main_layout.addWidget(title_label)
        
        # Conteneur avec défilement pour les sections
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_content)
        self.scroll_layout.setContentsMargins(0, 0, 0, 0)
        self.scroll_layout.setSpacing(10)  # Espacement entre sections
        
        self.scroll_area.setWidget(self.scroll_content)
        self.main_layout.addWidget(self.scroll_area)
    
    def add_section(self, title, collapsible=False):
        """Ajoute une section de KPI avec titre."""
        # Créer un groupe pour cette section
        section_group = QGroupBox(title)
        section_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 9pt;
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 4px;
                margin-top: 10px;
                background-color: {SotramineTheme.BG_CARD};
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 3px 8px;
                background-color: {SotramineTheme.PRIMARY};
                color: {SotramineTheme.TEXT_WHITE};
                border-radius: 3px;
            }}
        """)
        
        # Créer une section KPI
        section = CompactKPISection()
        
        # Layout pour le groupe
        group_layout = QVBoxLayout(section_group)
        group_layout.setContentsMargins(8, 15, 8, 8)  # Marges réduites
        group_layout.setSpacing(5)  # Espacement réduit
        group_layout.addWidget(section)
        
        # Ajouter le groupe à la mise en page
        self.scroll_layout.addWidget(section_group)
        
        # Stocker la référence à la section
        self.sections[title] = section
        
        return section
    
    def add_indicators_section(self, title):
        """Ajoute une section pour les indicateurs visuels."""
        # Créer un groupe pour cette section
        section_group = QGroupBox(title)
        section_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 9pt;
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 4px;
                margin-top: 10px;
                background-color: {SotramineTheme.BG_CARD};
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 3px 8px;
                background-color: {SotramineTheme.ACCENT};
                color: {SotramineTheme.TEXT_WHITE};
                border-radius: 3px;
            }}
        """)
        
        # Layout pour le groupe
        group_layout = QHBoxLayout(section_group)
        group_layout.setContentsMargins(8, 15, 8, 8)  # Marges réduites
        group_layout.setSpacing(8)  # Espacement réduit
        
        # Ajouter le groupe à la mise en page
        self.scroll_layout.addWidget(section_group)
        
        return group_layout