from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
from models.enums import ProcessStepEnum, ResourceType, StockMovementType

class DatabaseInterface(ABC):
    """Interface définissant les méthodes que doit implémenter le gestionnaire de base de données."""
    
    @abstractmethod
    def initialize_database(self) -> None:
        """Initialise la base de données avec les tables nécessaires."""
        pass
    
    @abstractmethod
    def add_production(self, step: ProcessStepEnum, quantity: float, quality: Optional[float] = None,
                      quantity_used: Optional[float] = None, production_hours: Optional[float] = None,
                      production_date: Optional[datetime] = None) -> int:
        """Ajoute une nouvelle production."""
        pass
    
    @abstractmethod
    def add_downtime(self, step: ProcessStepEnum, reason: str, duration: float,
                    downtime_date: Optional[datetime] = None, description: Optional[str] = None) -> int:
        """Ajoute un nouvel arrêt de production."""
        pass
    
    @abstractmethod
    def add_stock_movement(self, step: ProcessStepEnum, quantity: float,
                          movement_type: StockMovementType, reference_id: Optional[int] = None) -> int:
        """Ajoute un nouveau mouvement de stock."""
        pass
    
    @abstractmethod
    def add_resource_consumption(self, step: ProcessStepEnum, resource_type: ResourceType,
                               quantity: float, consumption_date: Optional[datetime] = None) -> int:
        """Ajoute une nouvelle consommation de ressource."""
        pass
    
    @abstractmethod
    def get_production_summary(self, step: Optional[ProcessStepEnum] = None,
                             start_date: Optional[datetime] = None,
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des productions."""
        pass
    
    @abstractmethod
    def get_downtime_summary(self, step: Optional[ProcessStepEnum] = None,
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des arrêts."""
        pass
    
    @abstractmethod
    def get_current_stock(self, step: ProcessStepEnum) -> float:
        """Récupère le stock actuel pour une étape donnée."""
        pass
    
    @abstractmethod
    def get_stock_movements(self, step: Optional[ProcessStepEnum] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère l'historique des mouvements de stock."""
        pass
    
    @abstractmethod
    def get_resource_consumption_summary(self, step: Optional[ProcessStepEnum] = None,
                                       resource_type: Optional[ResourceType] = None,
                                       start_date: Optional[datetime] = None,
                                       end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Récupère un résumé des consommations de ressources."""
        pass
    
    @abstractmethod
    def get_available_crushing_stock(self) -> float:
        """Récupère le stock disponible venant du concassage."""
        pass