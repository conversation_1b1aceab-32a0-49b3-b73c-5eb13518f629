import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleCanvas(FigureCanvas):
    """Simple canvas for matplotlib figure."""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """Initialize the canvas."""
        print("Creating figure...")
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        
        # Create a simple bar chart
        categories = ['A', 'B', 'C', 'D']
        values = [3, 7, 5, 9]
        
        self.axes.bar(categories, values)
        self.axes.set_title('Simple Bar Chart')
        self.axes.set_xlabel('Categories')
        self.axes.set_ylabel('Values')
        
        print("Initializing FigureCanvas...")
        super().__init__(self.fig)
        self.setParent(parent)
        print("Canvas created")

class SimpleChartWindow(QMainWindow):
    """Simple window with a chart."""
    
    def __init__(self):
        print("Initializing window...")
        super().__init__()
        self.init_ui()
        print("Window initialized")
    
    def init_ui(self):
        """Initialize the UI."""
        self.setWindowTitle('Simple Chart')
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Add the canvas
        print("Creating canvas...")
        canvas = SimpleCanvas(self)
        layout.addWidget(canvas)
        print("Canvas added to layout")

def main():
    """Main entry point."""
    try:
        # Create the application
        print("Creating QApplication...")
        app = QApplication(sys.argv)
        print("QApplication created")
        
        # Create and show the window
        print("Creating window...")
        window = SimpleChartWindow()
        print("Window created")
        
        print("Showing window...")
        window.show()
        print("Window displayed")
        
        # Run the application
        print("Starting event loop...")
        return app.exec_()
        
    except Exception as e:
        print(f"Error starting the application: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    print("Starting application...")
    try:
        exit_code = main()
        print(f"Application exited with code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        print(f"Unhandled exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)