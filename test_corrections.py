#!/usr/bin/env python3
"""
Script de test pour vérifier les corrections apportées à l'application.
"""

import sys
import os
import logging
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_imports():
    """Teste que tous les imports fonctionnent correctement."""
    print("🔍 Test des imports...")
    
    try:
        # Test des imports principaux
        from database.database_manager import DatabaseManager
        print("✅ DatabaseManager importé avec succès")
        
        from ui.main_window import MainWindow
        print("✅ MainWindow importé avec succès")
        
        from ui.professional_theme import get_professional_style, SotramineTheme
        print("✅ Thème professionnel importé avec succès")
        
        from utils.cache_manager import cache_manager, invalidate_cache_for
        print("✅ Cache manager importé avec succès")
        
        # Test des onglets optimisés
        from ui.tabs.optimized_reception_tab import OptimizedReceptionTab
        from ui.tabs.optimized_crushing_tab import OptimizedCrushingTab
        from ui.tabs.optimized_laverie_tab import OptimizedLaverieTab
        from ui.tabs.optimized_stock_tab import OptimizedStockTab
        from ui.tabs.optimized_reports_tab import OptimizedReportsTab
        print("✅ Onglets optimisés importés avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {str(e)}")
        return False

def test_database():
    """Teste les fonctionnalités de base de données."""
    print("\n🔍 Test de la base de données...")
    
    try:
        from database.database_manager import DatabaseManager
        from models.enums import ProcessStepEnum
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Test du cache des stocks
        stock_reception = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        print(f"✅ Stock réception: {stock_reception} tonnes")
        
        stock_concassage = db_manager.get_current_stock(ProcessStepEnum.CONCASSAGE)
        print(f"✅ Stock concassage: {stock_concassage} tonnes")
        
        # Test du cache (deuxième appel devrait être plus rapide)
        stock_reception_cached = db_manager.get_current_stock(ProcessStepEnum.RECEPTION)
        print(f"✅ Stock réception (cache): {stock_reception_cached} tonnes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de base de données: {str(e)}")
        return False

def test_cache():
    """Teste le système de cache."""
    print("\n🔍 Test du système de cache...")
    
    try:
        from utils.cache_manager import cache_manager, get_cache_stats, clean_expired_cache
        
        # Test de base du cache
        cache_manager.set("test_key", "test_value", ttl=60)
        cached_value = cache_manager.get("test_key")
        
        if cached_value == "test_value":
            print("✅ Cache fonctionne correctement")
        else:
            print("❌ Problème avec le cache")
            return False
        
        # Test des statistiques
        stats = get_cache_stats()
        print(f"✅ Statistiques du cache: {stats}")
        
        # Test du nettoyage
        cleaned = clean_expired_cache()
        print(f"✅ Nettoyage du cache: {cleaned} entrées supprimées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de cache: {str(e)}")
        return False

def test_theme():
    """Teste le thème professionnel."""
    print("\n🔍 Test du thème professionnel...")
    
    try:
        from ui.professional_theme import get_professional_style, SotramineTheme
        
        # Vérifier que le style CSS est généré
        style = get_professional_style()
        
        if "transform" in style:
            print("❌ Le style contient encore des propriétés 'transform' non supportées")
            return False
        
        if "QMainWindow" in style and "QPushButton" in style:
            print("✅ Style CSS généré correctement")
        else:
            print("❌ Style CSS incomplet")
            return False
        
        # Vérifier les couleurs du thème
        if hasattr(SotramineTheme, 'PRIMARY') and hasattr(SotramineTheme, 'SECONDARY'):
            print("✅ Couleurs du thème définies")
        else:
            print("❌ Couleurs du thème manquantes")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de thème: {str(e)}")
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Démarrage des tests de correction de l'application SOTRAMINE PHOSPHATE")
    print("=" * 70)
    
    # Configuration du logging pour les tests
    logging.basicConfig(level=logging.WARNING)  # Réduire le bruit des logs
    
    tests = [
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Système de cache", test_cache),
        ("Thème professionnel", test_theme),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Résumé des résultats
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name:<20} : {status}")
        if result:
            passed += 1
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Toutes les corrections ont été appliquées avec succès !")
        return 0
    else:
        print("⚠️  Certaines corrections nécessitent encore des ajustements.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
