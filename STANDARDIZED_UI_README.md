# Interface Utilisateur Standardisée pour SOTRAMINE PHOSPHATE

Ce document décrit les améliorations apportées à l'interface utilisateur de l'application SOTRAMINE PHOSPHATE pour assurer une structure cohérente et une expérience utilisateur optimisée.

## Principes de Conception

### Structure Cohérente
Tous les onglets suivent désormais une structure identique avec les sections suivantes dans le même ordre :
1. **KPI en haut** - Toujours visibles pour un accès rapide aux indicateurs clés
2. **Formulaire de saisie** - Zone de gauche pour la saisie des données
3. **Tableau de données** - Zone de droite pour l'affichage des données

### Palette de Couleurs Unifiée
- Utilisation des mêmes couleurs pour les mêmes types d'informations dans tous les onglets
- Palette de couleurs professionnelle définie dans `ui/professional_theme.py`

### Espacement Standardisé
- Marges et espacements identiques dans tous les onglets
- Mode compact disponible pour optimiser l'espace d'affichage

### Regroupement Intelligent
- Champs organisés par catégories logiques avec des séparateurs visuels
- Formulaires structurés pour une saisie intuitive

### Optimisation des Tableaux
- Colonnes prioritaires affichées par défaut
- Tri intelligent par date (plus récent en haut)
- Pagination optimisée (10-15 lignes par page)

### Personnalisation
- Possibilité de choisir quels KPI afficher
- Option d'affichage dense (mode compact)
- Mise en page responsive adaptée à différentes tailles d'écran
- Mise en page spéciale pour l'impression des rapports

## Utilisation des Nouveaux Composants

### Classe de Base Standardisée
La classe `StandardizedBaseTab` fournit une base commune pour tous les onglets :

```python
from ui.tabs.standardized_base_tab import StandardizedBaseTab

class MonOnglet(StandardizedBaseTab):
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "Titre de l'Onglet", parent)
        self.setup_interface()
        
    def setup_interface(self):
        # Ajouter des KPI
        self.kpi1 = self.add_kpi_card("Mon KPI", "0", "unité")
        
        # Ajouter un formulaire
        form_layout = self.add_form_section("Titre du Formulaire")
        
        # Ajouter un tableau
        headers = ["Colonne 1", "Colonne 2", "Colonne 3"]
        self.data_table = self.add_data_table("Titre du Tableau", headers)
```

### Widget de Saisie Standardisé
Le widget `StandardizedQuickInputWidget` permet de créer des formulaires cohérents :

```python
fields = {
    'field1': {
        'label': 'Étiquette:',
        'type': 'text',
        'placeholder': 'Exemple',
        'category': 'Catégorie'
    },
    'field2': {
        'label': 'Nombre:',
        'type': 'number',
        'placeholder': '0.00',
        'category': 'Catégorie'
    }
}

self.quick_input = StandardizedQuickInputWidget(fields)
self.quick_input.data_entered.connect(self.save_data)
```

## Fonctionnalités Avancées

### Mode Compact
Chaque onglet dispose d'un bouton pour activer le mode compact, qui réduit les espacements et la taille des éléments pour afficher plus de contenu.

### Personnalisation de l'Affichage
Un menu de personnalisation permet de :
- Choisir quels KPI afficher
- Sélectionner les colonnes visibles dans les tableaux

### Pagination Intelligente
Les tableaux de données incluent :
- Navigation par pages
- Sélection du nombre d'éléments par page
- Tri automatique par date (plus récent en haut)

### Impression Optimisée
Une mise en page spéciale pour l'impression des rapports est disponible via le bouton d'impression.

## Exemples d'Implémentation

Deux onglets standardisés ont été créés comme exemples :
- `StandardizedReceptionTab` - Onglet de réception standardisé
- `StandardizedCrushingTab` - Onglet de concassage standardisé

Pour voir ces onglets en action, exécutez :
```
python standardized_app_example.py
```

## Migration des Onglets Existants

Pour migrer un onglet existant vers la nouvelle structure standardisée :

1. Créez une nouvelle classe héritant de `StandardizedBaseTab`
2. Implémentez la méthode `setup_interface()` en suivant la structure standardisée
3. Adaptez les méthodes existantes pour utiliser les nouveaux composants

## Avantages de la Standardisation

- **Cohérence** : Interface uniforme dans toute l'application
- **Maintenabilité** : Code plus facile à maintenir et à faire évoluer
- **Expérience utilisateur** : Navigation intuitive et apprentissage rapide
- **Performance** : Optimisation de l'affichage et des interactions
- **Adaptabilité** : S'adapte à différentes tailles d'écran et préférences utilisateur